#include "pch.h"
#include "DcmStoreScu.h"

#include "DcmHead.h"
//	如遇到压缩型JPEG保存的DICOM图片， 需使用DJDecoderRegistration::registerCodecs(); 
//	注册解压算法，然后再读取DICOM图片就能读出来了。用完后用DJDecoderRegistration::cleanup(); 释放内存。注意用这个的时候需要
#include <iostream>

using namespace std;

namespace LPM
{
	static int g_nOptAcseTimeout = 30;//	超时设置 s
	static int g_nOptDimseTimeout = 0;

	CLock g_objApiLock;

	CDcmStoreScu::CDcmStoreScu()
	{
		OFStandard::initializeNetwork();
		DJDecoderRegistration::registerCodecs();
	}

	CDcmStoreScu::~CDcmStoreScu()
	{
		DJDecoderRegistration::cleanup();
		//	关闭网络
		OFStandard::shutdownNetwork();
	}

	int CDcmStoreScu::InitParam(const char* _szRemoteIp, const int _nRemotePort, const char* _szRemoteTitle, const char* _szLocalTitle)
	{
		if (_szRemoteIp == nullptr || _nRemotePort <=0 || _szRemoteTitle == nullptr || _szLocalTitle == nullptr)
		{
			WriteLogEx(ModuleVideo, LogError, u8"CDicomOpt::InitNet , 参数不合法 !");
			return -1;
		}

		if (m_pStoreScuParams)
		{
			WriteLogEx(ModuleVideo, LogError, u8"CDicomOpt::InitNet , 已初始化 !");
			return -1;
		}

		CAutoLock tempLock(&g_objApiLock);
		memset(m_szRemoteIp, 0, 256);
		memset(m_szRemoteTitle, 0, 256);
		memset(m_szLocalTitle, 0, 256);

		memcpy(m_szRemoteIp, _szRemoteIp, strlen(_szRemoteIp));

		m_nRemotePort = _nRemotePort;
		
		memcpy(m_szRemoteTitle, _szRemoteTitle, strlen(_szRemoteTitle));
		memcpy(m_szLocalTitle, _szLocalTitle, strlen(_szLocalTitle));


		return 0;
	}

	//	连接网络
	int CDcmStoreScu::ConnectNet()
	{
		CAutoLock tempLock(&g_objApiLock);

		std::string strRemoteIp(m_szRemoteIp);
		if (m_nRemotePort <= 0 || strRemoteIp.empty())
			return -1;

		CraateParam();
		CreateNet();
		int nRet = CreateAssoc();

		return nRet;
	}

	int CDcmStoreScu::CraateParam()
	{
		if (m_pStoreScuParams)
			return -1;

		//	2	初始化关联参数，即创建一个T_ASC_Parameters* 的实例
		OFCondition cond = ASC_createAssociationParameters(&m_pStoreScuParams, ASC_DEFAULTMAXPDU);
		if (cond.bad())
		{
			OFString strTemp;
			WriteLogEx(ModuleVideo, LogError, u8"创建一个T_ASC_Parameters* 的实例 失败，%s ", DimseCondition::dump(strTemp, cond).c_str());
			return -1;
		}

		//	3	设置客户端和服务端的 AETitle
		//ASC_setAPTitles(m_pStoreScuParams, "AyjPaics", m_szRemoteTitle, nullptr);
		ASC_setAPTitles(m_pStoreScuParams, m_szLocalTitle, m_szRemoteTitle, nullptr);
		cond = ASC_setTransportLayerType(m_pStoreScuParams, false);
		if (cond.bad())
		{
			WriteLogEx(ModuleVideo, LogError, u8"设置传送层安全类型失败:%s", cond.text());
			return -1;
		}
		//	4	设置服务端的地址和端口
		DIC_NODENAME tagLocalHost = {};
		gethostname(tagLocalHost, sizeof(tagLocalHost) - 1);
		DIC_NODENAME tagRemoteHost = {};
		sprintf(tagRemoteHost, "%s:%d", m_szRemoteIp, OFstatic_cast(int, m_nRemotePort));

		ASC_setPresentationAddresses(m_pStoreScuParams, tagLocalHost, tagRemoteHost);

		//	5	根据要传输的 dicom 对象的 sop class uid ，设置传输语法和抽象语法
		int nPresentId = 1;
		//const char cAbstractSyntax[]{ UID_SecondaryCaptureImageStorage };//屏幕拷贝 未压缩的图片
		const char cAbstractSyntax[]{ UID_UltrasoundImageStorage };// 压缩过的图片
		const char* szTransferSyntaxList[]{ UID_LittleEndianImplicitTransferSyntax };//小端隐式传输语法
		cond = ASC_addPresentationContext(m_pStoreScuParams, nPresentId, cAbstractSyntax, szTransferSyntaxList, 1);
		if (cond.bad())
		{
			cout << cond.text() << endl;
			WriteLogEx(ModuleVideo, LogError, u8"ASC_addPresentationContext  %s", cond.text());
			return -1;
		}

		return 0;
	}

	int CDcmStoreScu::CreateNet()
	{
		if (m_pStoreScuNet)
			return -1;
		//	1	初始化网络，即创建一个T_ASC_Network*的实例
		OFCondition cond = ASC_initializeNetwork(NET_REQUESTOR, 0, g_nOptAcseTimeout, &m_pStoreScuNet);
		if (cond.bad())
		{
			//WriteLogEx(ModuleVideo, LogError, "DICOM 底层网络初始化失败");
			return -1;
		}

		return 0;
	}
	
	int CDcmStoreScu::CreateAssoc()
	{
		if (m_pAssoc)
			return -1;

		if (!m_pStoreScuNet || !m_pStoreScuParams)
			return -1;

		//	创建连接
		OFCondition cond = ASC_requestAssociation(m_pStoreScuNet, m_pStoreScuParams, &m_pAssoc);
		if (cond.bad())
		{
			WriteLogEx(ModuleVideo, LogError, u8"ASC_requestAssociation 连接服务器失败, %s", LPM::W2U(LPM::A2WEx( cond.text())).c_str());
			return -1;
		}

		if (ASC_countAcceptedPresentationContexts(m_pStoreScuParams) == 0)
		{
			WriteLogEx(ModuleVideo, LogError, u8"服务端不接受制定的抽象语法与传输语法");
			return -1;
		}

		m_bConnectScp = true;

		return 0;
	}

	//	断开网络
	int CDcmStoreScu::DisconnectNet()
	{
		//	解除关联，即终止到SCP的网络连接
		CAutoLock tempLock(&g_objApiLock);
		OFCondition cond = {};
		if (m_pAssoc)
		{
			cond = ASC_releaseAssociation(m_pAssoc);	//	释放关联
			cond = ASC_destroyAssociation(&m_pAssoc);				//	销毁
		}
		if (m_pStoreScuNet)
		{
			cond = ASC_dropNetwork(&m_pStoreScuNet);
		}

		m_pStoreScuParams = nullptr;

		m_bConnectScp = false;
		
		return 0;
	}

	int CDcmStoreScu::SendFileStoreScu(const char* _szDcmFilePath, const tagPatientShotPicInfo& _objInfo, bool _bIsGeServer)
	{
		//	1	加载 dicom 文件
		DcmFileFormat objDcmFile;
		OFCondition cond = objDcmFile.loadFile(_szDcmFilePath, EXS_Unknown, EGL_noChange, DCM_MaxReadLength, ERM_autoDetect);
		if (cond.bad())		
			return -1;		

		//	2、添加患者信息
		if(_bIsGeServer)
			AddPicInfoGe(objDcmFile.getDataset(), _objInfo);
		else
			AddPicInfo(objDcmFile.getDataset(), _objInfo);

		//	3、保存测试
		/*string strPath1;
		if (strPath1.empty())
		{
			wstring wstrPath = GetCurExePath();
			strPath1 = LPM::W2AEx(wstrPath) + "tempFile1.dcm";
			objDcmFile.saveFile(strPath1.c_str());
		}*/

		//	4、发送到服务器
		return  SendFileStoreScu(&objDcmFile, _objInfo);
	}
	
	int CDcmStoreScu::SendFileStoreScu(DcmFileFormat* _objFileFmt, const tagPatientShotPicInfo& _objInfo)
	{
		if (nullptr == m_pAssoc)
			return -1;
		
		//	2	找出哪些SOP类和SOP实例被封装在文件中
		OFCondition cond = {};
		T_ASC_PresentationContextID presID = {};
		DIC_UI sopClass = {};
		DIC_UI sopInstance = {};

		if (!DU_findSOPClassAndInstanceInDataSet(_objFileFmt->getDataset(),
			sopClass, sizeof(sopClass), sopInstance, sizeof(sopInstance), OFFalse))
		{
			WriteLogEx(ModuleVideo, LogError, u8"文件中没有SOP类或实例UID:%s");
			return -1;
		}
		
		//	找出应该使用哪些可接受的表示上下文
		DcmXfer filexfer(_objFileFmt->getDataset()->getOriginalXfer());
		if (filexfer.isNotEncapsulated())
			filexfer = EXS_DeflatedLittleEndianExplicit;
		else
		{
			//传输语法解压或转换
			filexfer = UID_LittleEndianImplicitTransferSyntax;
			cond = _objFileFmt->getDataset()->chooseRepresentation(filexfer.getXfer(), NULL);
			if (cond.bad())
				return -1;
		}

		if (filexfer.getXfer() != EXS_Unknown)
			presID = ASC_findAcceptedPresentationContextID(m_pAssoc, sopClass, filexfer.getXferID());
		else
			presID = ASC_findAcceptedPresentationContextID(m_pAssoc, sopClass);

		if (presID == 0)
		{
			const char *modalityName = dcmSOPClassUIDToModality(sopClass);
			if (!modalityName)
				modalityName = dcmFindNameOfUID(sopClass);
			if (!modalityName)
				modalityName = "unknown SOP class";
			cout << "No valid presentation context ID , for: (" << modalityName << ") " << sopClass;

			WriteLogEx(ModuleVideo, LogError, u8"No valid presentation context ID , for: (%s) %s", modalityName, sopClass);
			return -1;
		}
		//return -1;
		/*string strPath;
		if (strPath.empty() && _objFileFmt)
		{
			wstring wstrPath = GetCurExePath();
			strPath = LPM::W2AEx(wstrPath) + "tempFile.dcm";
			_objFileFmt->saveFile(strPath.c_str(), EXS_LittleEndianExplicit);
		}*/
		
		T_ASC_PresentationContext pc;
		ASC_findAcceptedPresentationContext(m_pAssoc->params, presID, &pc);
		DcmXfer netTransfer(pc.acceptedTransferSyntax);

		//	如果需要，转储有关传输语法的一般信息

		//	准备数据传输
		T_DIMSE_C_StoreRQ objReq = {};
		T_DIMSE_C_StoreRSP objRsp = {};
		memset(OFreinterpret_cast(char *, &objReq), 0, sizeof(objReq));
		DIC_US msgId = m_pAssoc->nextMsgID++;
		objReq.MessageID = msgId;

		OFStandard::strlcpy(objReq.AffectedSOPClassUID, sopClass, sizeof(objReq.AffectedSOPClassUID));
		OFStandard::strlcpy(objReq.AffectedSOPInstanceUID, sopInstance, sizeof(objReq.AffectedSOPInstanceUID));
		objReq.DataSetType = DIMSE_DATASET_PRESENT;
		objReq.Priority = DIMSE_PRIORITY_MEDIUM;

		int nRet = -1;
		//	进行数据传输
		DcmDataset* pStatusDetail = nullptr;
		cond = DIMSE_storeUser(m_pAssoc, presID, &objReq, nullptr, _objFileFmt->getDataset(), StoreScuCallback, nullptr,
			(T_DIMSE_BlockingMode)m_nBlockingMode, g_nOptDimseTimeout, &objRsp, &pStatusDetail, nullptr);

		if (cond == EC_Normal && (objRsp.DimseStatus == STATUS_Success || DICOM_WARNING_STATUS(objRsp.DimseStatus)))
		{
			WriteLogEx(ModuleVideo, LogDebug, u8"DIMSE_storeUser success");
			nRet = 0;
		}
		else
		{
			WriteLogEx(ModuleVideo, LogError, u8"DIMSE_storeUser error");

			nRet = -1;
		}
		int nLastStatusCode = objRsp.DimseStatus;
		if (pStatusDetail)
		{
			delete pStatusDetail;
			pStatusDetail = nullptr;
		}

		return 0;
	}
	
	int CDcmStoreScu::SendFileStoreScu(const char* _szPicFilePath, bool _bJpgOrBmp, const tagPatientShotPicInfo& _objInfo)
	{
		if (_szPicFilePath == nullptr)
			return -1;
		static string strPath;
		if (strPath.empty())
		{
			wstring wstrPath = GetCurExePath();
			strPath = LPM::W2AEx(wstrPath) + "tempFile.dcm";
		}
		

		CAutoLock tempLock(&g_objApiLock);

		bool bOk = CreateDcmPicEx(LPM::U2A(_szPicFilePath).c_str(), true, _objInfo, strPath.c_str());

		WriteLogEx(ModuleVideo, LogDebug, u8"Dicom Store file: %s ", _szPicFilePath);

		if (bOk)
			return SendFileStoreScu(strPath.c_str(), _objInfo);
		else
			WriteLogEx(ModuleVideo, LogError, u8"Bad DICOM file: %s", _szPicFilePath);

		return -1;
	}

	int CDcmStoreScu::SendFileStoreScuGe(const char* _szPicFilePath, bool _bJpgOrBmp, const tagPatientShotPicInfo& _objInfo)
	{
		if (_szPicFilePath == nullptr)
			return -1;
		static string strPath;
		if (strPath.empty())
		{
			wstring wstrPath = GetCurExePath();
			strPath = LPM::W2AEx(wstrPath) + "tempFile.dcm";
		}


		CAutoLock tempLock(&g_objApiLock);

		bool bOk = CreateDcmPicEx(LPM::U2A(_szPicFilePath).c_str(), true, _objInfo, strPath.c_str());

		WriteLogEx(ModuleVideo, LogDebug, u8"Dicom Store file: %s ", _szPicFilePath);

		if (bOk)
			return  SendFileStoreScu(strPath.c_str(), _objInfo, true);
		else
			WriteLogEx(ModuleVideo, LogError, u8"Bad DICOM file: %s", _szPicFilePath);

		return -1;
	}

	int CDcmStoreScu::SendFileStoreScu(const char* _szImageData, int _nDataLength, bool _bJpgOrBmp, const tagPatientShotPicInfo& _objInfo)
	{
		CAutoLock tempLock(&g_objApiLock);

		return 0;
	}

	//	发送测量值给工作站
	int CDcmStoreScu::SendMeasuredScu(const tagStoreMeasuredInfo& _objMeasuredInfo)
	{
		// 初始化数据集 
		DcmFileFormat objDcmFrmat;
		DcmDataset* pDataset = objDcmFrmat.getDataset();

		// 设置必要的DICOM标签 
		pDataset->putAndInsertString(DCM_SOPClassUID, UID_ComprehensiveSRStorage);

		char sopInstanceUID[100] = {};
		dcmGenerateUniqueIdentifier(sopInstanceUID, SITE_INSTANCE_UID_ROOT);
		pDataset->putAndInsertString(DCM_SOPInstanceUID, sopInstanceUID);
		pDataset->putAndInsertString(DCM_Modality, "SR");
		//pDataset->putAndInsertString(DCM_MediaStorageSOPClassUID, UID_MultiframeTrueColorSecondaryCaptureImageStorage);
		// 生成Study和Series UID 
		char studyUID[100] = {}, seriesUID[100] = {};
		dcmGenerateUniqueIdentifier(studyUID, SITE_STUDY_UID_ROOT);
		dcmGenerateUniqueIdentifier(seriesUID, SITE_SERIES_UID_ROOT);



		pDataset->putAndInsertString(DCM_StudyInstanceUID, studyUID);
		pDataset->putAndInsertString(DCM_SeriesInstanceUID, seriesUID);

		// 设置患者信息 
		pDataset->putAndInsertString(DCM_PatientName, _objMeasuredInfo.strPatientName.c_str());
		pDataset->putAndInsertString(DCM_PatientID, _objMeasuredInfo.strPatientId.c_str());
		pDataset->putAndInsertString(DCM_AccessionNumber, _objMeasuredInfo.strAccessionNumber.c_str());
		// 设置日期和时间 
		pDataset->putAndInsertString(DCM_StudyDate, _objMeasuredInfo.strStudyDate.c_str());
		pDataset->putAndInsertString(DCM_StudyTime, "120000");
		pDataset->putAndInsertString(DCM_ContentDate, _objMeasuredInfo.strStudyDate.c_str());
		pDataset->putAndInsertString(DCM_ContentTime, "120000");

		// 创建内容序列 (0040,A730)
		DcmSequenceOfItems *contentSeq = new DcmSequenceOfItems(DCM_ContentSequence);

		// 添加测量项
		std::string strCodeInfo = "123";
		int nIndex = 1;
		for (const auto& itMer : _objMeasuredInfo.vectMeasured)
		{
			AddMeasurement(contentSeq, std::string( strCodeInfo + std::to_string(nIndex++)).c_str(), "DCM", "Biparietal diameter", "23.7", "mm");
		}
		//AddMeasurement(contentSeq, "12304", "DCM", "Biparietal diameter", "23.7", "mm");
		AddMeasurement(contentSeq, "11984-2", "LN", "Head Circumference", "10.77", "cm");
		//AddMeasurement(contentSeq, "12306", "DCM", "Abdominal Circumference", "61.2", "mm");
		//AddMeasurement(contentSeq, "12307", "DCM", "Femur Length", "23.9", "mm");

		pDataset->insert(contentSeq);

		// 发送到工作站 
		tagPatientShotPicInfo objPatientInfo;
		
		return SendFileStoreScu(&objDcmFrmat, objPatientInfo);
	}

	//	生成一个dicom 测量值文件
	int CDcmStoreScu::CreateMeasuredDcmFile(const char* _szSavecmFilePathUtf8, tagStoreMeasuredInfo& _objInfo)
	{
		ParseMeasurementDcmFile("D://work//MyWork//GeViewPoint_file//demo_data_1//SR841224//00847588");
		int nRet = -1;
		//return -1;
		if (_szSavecmFilePathUtf8 == nullptr)
		{
			WriteLogEx(ModuleVideo, LogError, u8"bad save path");
			return -1;
		}
		return -1;
		// 初始化数据集 
		DcmFileFormat fileformat;
		DcmDataset* pDataset = fileformat.getDataset();

		// 设置必要的DICOM标签 
		pDataset->putAndInsertString(DCM_SOPClassUID, UID_ComprehensiveSRStorage);
		
		char sopInstanceUID[100] = {};
		dcmGenerateUniqueIdentifier(sopInstanceUID, SITE_INSTANCE_UID_ROOT);
		pDataset->putAndInsertString(DCM_SOPInstanceUID, sopInstanceUID);
		pDataset->putAndInsertString(DCM_Modality, "SR");
		pDataset->putAndInsertString(DCM_InstanceCreationTime, "120000");
		pDataset->putAndInsertString(DCM_InstanceCreationDate, _objInfo.strStudyDate.c_str());
		pDataset->putAndInsertString(DCM_SpecificCharacterSet, "ISO_IR 100");
		pDataset->putAndInsertString(DCM_PatientSex, "F");
		// 生成Study和Series UID 
		char studyUID[100] = {}, seriesUID[100] = {};
		dcmGenerateUniqueIdentifier(studyUID, SITE_STUDY_UID_ROOT);
		dcmGenerateUniqueIdentifier(seriesUID, SITE_SERIES_UID_ROOT);

		pDataset->putAndInsertString(DCM_ValueType, "CONTAINER");
		

		pDataset->putAndInsertString(DCM_StudyInstanceUID, studyUID);
		pDataset->putAndInsertString(DCM_SeriesInstanceUID, seriesUID);

		// 设置患者信息 
		pDataset->putAndInsertString(DCM_PatientName, _objInfo.strPatientName.c_str());
		pDataset->putAndInsertString(DCM_PatientID, _objInfo.strPatientId.c_str());
		pDataset->putAndInsertString(DCM_AccessionNumber, _objInfo.strAccessionNumber.c_str());
		// 设置日期和时间 
		pDataset->putAndInsertString(DCM_StudyDate, _objInfo.strStudyDate.c_str());
		pDataset->putAndInsertString(DCM_StudyTime, "120000");
		pDataset->putAndInsertString(DCM_ContentDate, _objInfo.strStudyDate.c_str());
		pDataset->putAndInsertString(DCM_ContentTime, "120000");

		DcmSequenceOfItems *contentSeq1 = new DcmSequenceOfItems(DCM_ConceptNameCodeSequence); //DCM_ConceptNameCodeSequence
		DcmItem* pMeasurItem = new DcmItem();

		pMeasurItem->putAndInsertString(DCM_CodeValue, "125000");
		pMeasurItem->putAndInsertString(DCM_CodingSchemeDesignator, "DCM");
		pMeasurItem->putAndInsertString(DCM_CodeMeaning, "OB-GYN Ultrasound Procedure Report");
		contentSeq1->insert(pMeasurItem);
		// 创建内容序列 (0040,A730)
		DcmSequenceOfItems *contentSeq = new DcmSequenceOfItems(DCM_ContentSequence); //DCM_ConceptNameCodeSequence
		//DcmSequenceOfItems* measurementSequence = new DcmSequenceOfItems(DCM_MeasurementsSequence);
		// 添加测量项
		AddMeasurement(contentSeq, "11984-2", "LN", "Head Circumference", "12.77", "cm");
		//AddMeasurement(contentSeq, "12304", "DCM", "Biparietal diameter", "23.7", "mm");
		//AddMeasurement(contentSeq, "12305", "DCM", "Head Circumference", "10.77", "cm");
		//AddMeasurement(contentSeq, "12306", "DCM", "Abdominal Circumference", "61.2", "mm");
		//AddMeasurement(contentSeq, "12307", "DCM", "Femur Length", "23.9", "mm");
		pDataset->insert(contentSeq1);
		pDataset->putAndInsertString(DCM_ContinuityOfContent, "SEPARATE");
		//pDataset->putAndInsertString(DCM_PerformedProcedureCodeSequence, "SQ");
		pDataset->putAndInsertString(DCM_CompletionFlag, "PARTIAL");
		pDataset->putAndInsertString(DCM_VerificationFlag, "UNVERIFIED");
		pDataset->insert(contentSeq);

		// 保存文件 
		OFCondition status = fileformat.saveFile(LPM::U2AEx(_szSavecmFilePathUtf8).c_str(), EXS_LittleEndianExplicit);
		if (status.bad()) {
			WriteLogEx(ModuleVideo, LogError, u8"Error saving DICOM file: %s",LPM::W2U( LPM::A2WEx(status.text()).c_str()).c_str());
		}
		else
		{
			nRet = 0;
		}
		//delete contentSeq;

		return nRet;
	}
	
	int CDcmStoreScu::CreateMeasuredDcmFileEx(const char* _szSavecmFilePathUtf8)
	{
		if (_szSavecmFilePathUtf8 == nullptr)
		{
			WriteLogEx(ModuleVideo, LogError, u8"bad save path");
			return -1;
		}
		//	生成测量值
	}
	//	添加测量值
	void CDcmStoreScu::AddMeasurement(DcmSequenceOfItems* _pSeq, const char* _szCodeInfo, const char* _szCodingScheme,
		const char* _szCodeMeaning, const char* _szCodeValue, const char* _szCodeUnit)
	{
		DcmItem* pMeasurItem = new DcmItem();

		// 创建概念名称代码序列 
		DcmSequenceOfItems* pConceptSeq = new DcmSequenceOfItems(DCM_ConceptNameCodeSequence);
		DcmItem *codeItem = new DcmItem();
		codeItem->putAndInsertString(DCM_CodeValue, _szCodeInfo);
		codeItem->putAndInsertString(DCM_CodingSchemeDesignator, _szCodingScheme);
		codeItem->putAndInsertString(DCM_CodeMeaning, _szCodeMeaning);
		pConceptSeq->insert(codeItem);
		pMeasurItem->insert(pConceptSeq);

		// 创建测量值序列 
		DcmSequenceOfItems* pMeasSeq = new DcmSequenceOfItems(DCM_MeasuredValueSequence);
		DcmItem *measItem = new DcmItem();

		// 设置数值 
		measItem->putAndInsertString(DCM_NumericValue, _szCodeValue);

		// 设置单位代码序列 
		DcmSequenceOfItems *unitSeq = new DcmSequenceOfItems(DCM_MeasurementUnitsCodeSequence);
		DcmItem *unitItem = new DcmItem();
		unitItem->putAndInsertString(DCM_CodeValue, _szCodeUnit);
		unitItem->putAndInsertString(DCM_CodingSchemeDesignator, "UCUM");
		unitItem->putAndInsertString(DCM_CodingSchemeVersion, _szCodeValue);
		//unitItem->putAndInsertString(DCM_CodeMeaning, (strcmp(_szCodeUnit, "mm") == 0) ? "millimeter" : "centimeter");
		unitItem->putAndInsertString(DCM_CodeMeaning, _szCodeUnit);
		unitSeq->insert(unitItem);
		measItem->insert(unitSeq);

		pMeasSeq->insert(measItem);
		pMeasurItem->insert(pMeasSeq);

		_pSeq->insert(pMeasurItem);
	}
	
	bool CDcmStoreScu::ParseMeasurementDcmFile(const char* _szInputDcmFile)
	{
		bool bRet = false;
		if (_szInputDcmFile == nullptr)
			return bRet;

		DcmFileFormat fileformat;
		OFCondition status = fileformat.loadFile(_szInputDcmFile);
		if (status.bad())
		{
			//WriteLogEx(ModuleVideo, LogError, u8"无法打开DICOM文件");
			return bRet;
		}

		DcmDataset *dataset = fileformat.getDataset();
		if (!dataset)
		{
			//WriteLogEx(ModuleVideo, LogError, u8"无法获取数据集");
			return bRet;
		}

		// 获取测量序列
		DcmSequenceOfItems *measureSeq = nullptr;
		if (dataset->findAndGetSequence(DCM_ContentSequence, measureSeq).good() && measureSeq != nullptr)
		{
			unsigned long numItems = measureSeq->card();
			//WriteLogEx(ModuleVideo, LogDebug, u8"发现 %d 个测量值", numItems);
			// 遍历所有测量项
			for (unsigned long i = 0; i < numItems; i++)
			{
				DcmItem *item = measureSeq->getItem(i);
				if (item)
				{
					OFString numericValue;
					OFString units;
					OFString conceptName;

					// 获取数值
					if (item->findAndGetOFString(DCM_NumericValue, numericValue).good())
					{
						//WriteLogEx(ModuleVideo, LogDebug, u8"测量值 %s 个测量值", numericValue.c_str());
					}

					// 获取单位
					DcmSequenceOfItems *unitsSeq = nullptr;
					if (item->findAndGetSequence(DCM_MeasurementUnitsCodeSequence, unitsSeq).good() && unitsSeq != nullptr)
					{
						DcmItem *unitsItem = unitsSeq->getItem(0);
						if (unitsItem && unitsItem->findAndGetOFString(DCM_CodeMeaning, units).good())
						{
							WriteLogEx(ModuleVideo, LogDebug, u8"  %s", units.c_str());
						}
					}

					// 获取概念名称（如头围、腹围等）
					DcmSequenceOfItems *conceptSeq = nullptr;
					if (item->findAndGetSequence(DCM_ConceptNameCodeSequence, conceptSeq).good() && conceptSeq != nullptr)
					{
						DcmItem *conceptItem = conceptSeq->getItem(0);
						if (conceptItem && conceptItem->findAndGetOFString(DCM_CodeMeaning, conceptName).good())
						{
							WriteLogEx(ModuleVideo, LogDebug, u8"  (%s)", conceptName.c_str());
						}
					}
				}
			}
		
			bRet = true;
		}
		else
		{
			//WriteLogEx(ModuleVideo, LogError, u8" 未找到测量序列");
			return bRet;
		}

		return bRet;
	}
	
	//	修改测量值文件数据
	bool CDcmStoreScu::ModifyMeasurementDcmFile(const char* _szInputDcmFile, const char* _szInputDcmFileOut, tagStoreMeasuredInfo& _objInfo)
	{
		bool bRet = false;
		if (_szInputDcmFile == nullptr)
			return bRet;

		DcmFileFormat fileformat;
		OFCondition status = fileformat.loadFile(_szInputDcmFile);
		if (status.bad())
		{
			//WriteLogEx(ModuleVideo, LogError, u8"无法打开DICOM文件");
			return bRet;
		}

		DcmDataset *pDataset = fileformat.getDataset();
		if (!pDataset)
		{
			//WriteLogEx(ModuleVideo, LogError, u8"无法获取数据集");
			return bRet;
		}

		char sopInstanceUID[100] = {};
		dcmGenerateUniqueIdentifier(sopInstanceUID, SITE_INSTANCE_UID_ROOT);
		pDataset->putAndInsertString(DCM_SOPInstanceUID, sopInstanceUID);
		//pDataset->putAndInsertString(DCM_Modality, "SR");
		//pDataset->putAndInsertString(DCM_InstanceCreationTime, "120000");
		//pDataset->putAndInsertString(DCM_InstanceCreationDate, _objInfo.strStudyDate.c_str());
		//pDataset->putAndInsertString(DCM_SpecificCharacterSet, "ISO_IR 100");
		//pDataset->putAndInsertString(DCM_PatientSex, "F");
		// 生成Study和Series UID 
		char studyUID[100] = {}, seriesUID[100] = {};
		dcmGenerateUniqueIdentifier(studyUID, SITE_STUDY_UID_ROOT);
		dcmGenerateUniqueIdentifier(seriesUID, SITE_SERIES_UID_ROOT);
		//pDataset->putAndInsertString(DCM_ValueType, "CONTAINER");

		pDataset->putAndInsertString(DCM_StudyInstanceUID, studyUID);
		pDataset->putAndInsertString(DCM_SeriesInstanceUID, seriesUID);

		// 设置患者信息 
		pDataset->putAndInsertString(DCM_PatientName, _objInfo.strPatientName.c_str());
		pDataset->putAndInsertString(DCM_PatientID, _objInfo.strPatientId.c_str());
		pDataset->putAndInsertString(DCM_AccessionNumber, _objInfo.strAccessionNumber.c_str());
		// 设置日期和时间 
		pDataset->putAndInsertString(DCM_StudyDate, _objInfo.strStudyDate.c_str());
		//pDataset->putAndInsertString(DCM_StudyTime, "120000");
		pDataset->putAndInsertString(DCM_ContentDate, _objInfo.strStudyDate.c_str());
		pDataset->putAndInsertString(DCM_SeriesDate, _objInfo.strStudyDate.c_str());
		//pDataset->putAndInsertString(DCM_ContentTime, "120000");

		// 保存文件 
		status = fileformat.saveFile(LPM::U2AEx(_szInputDcmFileOut).c_str());
		if (status.bad()) {
			WriteLogEx(ModuleVideo, LogError, u8"Error saving DICOM file: %s", LPM::W2U(LPM::A2WEx(status.text()).c_str()).c_str());
		}
	
		return true;
	}
	void CDcmStoreScu::StoreScuCallback(void* callbackData, T_DIMSE_StoreProgress* _pProgress, T_DIMSE_C_StoreRQ* _pReq)
	{
		if (_pProgress->state == DIMSE_StoreBegin)
		{
			OFString str;
			//OFLOG_DEBUG(storescuLogger, DIMSE_dumpMessage(str, *req, DIMSE_OUTGOING));
		}

		// We can't use oflog for the pdu output, but we use a special logger for
		// generating this output. If it is set to level "INFO" we generate the
		// output, if it's set to "DEBUG" then we'll assume that there is debug output
		// generated for each PDU elsewhere.
		/*OFLogger progressLogger = OFLog::getLogger("dcmtk.apps." OFFIS_CONSOLE_APPLICATION ".progress");
		if (progressLogger.getChainedLogLevel() == OFLogger::INFO_LOG_LEVEL) {
			switch (progress->state) {
			case DIMSE_StoreBegin:
				COUT << "XMIT: "; break;
			case DIMSE_StoreEnd:
				COUT << OFendl; break;
			default:
				COUT << "."; break;
			}
			COUT.flush();
		}*/
	}
}

