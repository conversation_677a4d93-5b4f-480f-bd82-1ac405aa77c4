
# Dicom-File-Format

# Dicom-Meta-Information-Header
# Used TransferSyntax: Little Endian Explicit
(0002,0000) UL 190                                      #   4, 1 FileMetaInformationGroupLength
(0002,0001) OB 00\01                                    #   2, 1 FileMetaInformationVersion
(0002,0002) UI =ComprehensiveSRStorage                  #  30, 1 MediaStorageSOPClassUID
(0002,0003) UI [1.2.276.********.1.2.2025.100.42390.7436629] #  44, 1 MediaStorageSOPInstanceUID
(0002,0010) UI =LittleEndianExplicit                    #  20, 1 TransferSyntaxUID
(0002,0012) UI [1.2.840.113619.6.169]                   #  20, 1 ImplementationClassUID
(0002,0013) SH [ViewPoint_6_]                           #  12, 1 ImplementationVersionName
(0002,0016) AE [VP]                                     #   2, 1 SourceApplicationEntityTitle

# Dicom-Data-Set
# Used TransferSyntax: Little Endian Explicit
(0008,0005) CS [ISO_IR 100]                             #  10, 1 SpecificCharacterSet
(0008,0012) DA [20250307]                               #   8, 1 InstanceCreationDate
(0008,0013) TM [194630]                                 #   6, 1 InstanceCreationTime
(0008,0014) UI [1.2.276.0.7230010.3.0.3.6.0]            #  28, 1 InstanceCreatorUID
(0008,0016) UI =ComprehensiveSRStorage                  #  30, 1 SOPClassUID
(0008,0018) UI [1.2.276.********.1.2.2025.100.42390.7436629] #  44, 1 SOPInstanceUID
(0008,0020) DA [20250307]                               #   8, 1 StudyDate
(0008,0023) DA [20250307]                               #   8, 1 ContentDate
(0008,0030) TM [194529]                                 #   6, 1 StudyTime
(0008,0033) TM [194630]                                 #   6, 1 ContentTime
(0008,0050) SH [25111001]                               #   8, 1 AccessionNumber
(0008,0060) CS [SR]                                     #   2, 1 Modality
(0008,0070) LO [GE Healthcare Austria GmbH & Co OG]     #  34, 1 Manufacturer
(0008,0090) PN (no value available)                     #   0, 0 ReferringPhysicianName
(0008,1030) LO [?????]                                  #   6, 1 StudyDescription
(0008,1090) LO [4DView]                                 #   6, 1 ManufacturerModelName
(0008,1111) SQ (Sequence with explicit length #=0)      #   0, 1 ReferencedPerformedProcedureStepSequence
(fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
(0010,0010) PN [2025^0308]                              #  10, 1 PatientName
(0010,0020) LO [250308001]                              #  10, 1 PatientID
(0010,0030) DA (no value available)                     #   0, 0 PatientBirthDate
(0010,0040) CS [F]                                      #   2, 1 PatientSex
(0018,1000) LO [4DView]                                 #   6, 1 DeviceSerialNumber
(0018,1020) LO [18 Ext.3 Rev.b]                         #  14, 1 SoftwareVersions
(0020,000d) UI [1.2.840.113619.2.169.619808478160662.8.11075.**********] #  56, 1 StudyInstanceUID
(0020,000e) UI [1.2.276.********.1.2.2025.100.42329.6012923.1] #  46, 1 SeriesInstanceUID
(0020,0010) SH [1]                                      #   2, 1 StudyID
(0020,0011) IS [1]                                      #   2, 1 SeriesNumber
(0020,0013) IS [1]                                      #   2, 1 InstanceNumber
(0040,a040) CS [CONTAINER]                              #  10, 1 ValueType
(0040,a043) SQ (Sequence with explicit length #=1)      #  76, 1 ConceptNameCodeSequence
  (fffe,e000) na (Item with explicit length #=3)          #  68, 1 Item
    (0008,0100) SH [125000]                                 #   6, 1 CodeValue
    (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
    (0008,0104) LO [OB-GYN Ultrasound Procedure Report]     #  34, 1 CodeMeaning
  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
(fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
(0040,a050) CS [SEPARATE]                               #   8, 1 ContinuityOfContent
(0040,a372) SQ (Sequence with explicit length #=0)      #   0, 1 PerformedProcedureCodeSequence
(fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
(0040,a491) CS [PARTIAL]                                #   8, 1 CompletionFlag
(0040,a493) CS [UNVERIFIED]                             #  10, 1 VerificationFlag
(0040,a504) SQ (Sequence with explicit length #=1)      #  32, 1 ContentTemplateSequence
  (fffe,e000) na (Item with explicit length #=2)          #  24, 1 Item
    (0008,0105) CS [DCMR]                                   #   4, 1 MappingResource
    (0040,db00) CS [5000]                                   #   4, 1 TemplateIdentifier
  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
(fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
(0040,a730) SQ (Sequence with explicit length #=4)      # 12208, 1 ContentSequence
  (fffe,e000) na (Item with explicit length #=5)          # 476, 1 Item
    (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
    (0040,a040) CS [CONTAINER]                              #  10, 1 ValueType
    (0040,a043) SQ (Sequence with explicit length #=1)      #  56, 1 ConceptNameCodeSequence
      (fffe,e000) na (Item with explicit length #=3)          #  48, 1 Item
        (0008,0100) SH [121005]                                 #   6, 1 CodeValue
        (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
        (0008,0104) LO [Observer Type]                          #  14, 1 CodeMeaning
      (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
    (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
    (0040,a050) CS [SEPARATE]                               #   8, 1 ContinuityOfContent
    (0040,a730) SQ (Sequence with explicit length #=2)      # 346, 1 ContentSequence
      (fffe,e000) na (Item with explicit length #=4)          # 130, 1 Item
        (0040,a010) CS [HAS OBS CONTEXT]                        #  16, 1 RelationshipType
        (0040,a040) CS [PNAME]                                  #   6, 1 ValueType
        (0040,a043) SQ (Sequence with explicit length #=1)      #  62, 1 ConceptNameCodeSequence
          (fffe,e000) na (Item with explicit length #=3)          #  54, 1 Item
            (0008,0100) SH [121008]                                 #   6, 1 CodeValue
            (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
            (0008,0104) LO [Person Observer Name]                   #  20, 1 CodeMeaning
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
        (0040,a123) PN [Billy Hou]                              #  10, 1 PersonName
      (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
      (fffe,e000) na (Item with explicit length #=4)          # 200, 1 Item
        (0040,a010) CS [HAS OBS CONTEXT]                        #  16, 1 RelationshipType
        (0040,a040) CS [CODE]                                   #   4, 1 ValueType
        (0040,a043) SQ (Sequence with explicit length #=1)      #  86, 1 ConceptNameCodeSequence
          (fffe,e000) na (Item with explicit length #=3)          #  78, 1 Item
            (0008,0100) SH [121010]                                 #   6, 1 CodeValue
            (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
            (0008,0104) LO [Person Observations Role in the Organisation] #  44, 1 CodeMeaning
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
        (0040,a168) SQ (Sequence with explicit length #=1)      #  54, 1 ConceptCodeSequence
          (fffe,e000) na (Item with explicit length #=3)          #  46, 1 Item
            (0008,0100) SH [121093]                                 #   6, 1 CodeValue
            (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
            (0008,0104) LO [Sonographer]                            #  12, 1 CodeMeaning
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
      (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
    (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
  (fffe,e000) na (Item with explicit length #=5)          # 1604, 1 Item
    (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
    (0040,a040) CS [CONTAINER]                              #  10, 1 ValueType
    (0040,a043) SQ (Sequence with explicit length #=1)      #  56, 1 ConceptNameCodeSequence
      (fffe,e000) na (Item with explicit length #=3)          #  48, 1 Item
        (0008,0100) SH [125008]                                 #   6, 1 CodeValue
        (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
        (0008,0104) LO [Fetus Summary]                          #  14, 1 CodeMeaning
      (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
    (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
    (0040,a050) CS [SEPARATE]                               #   8, 1 ContinuityOfContent
    (0040,a730) SQ (Sequence with explicit length #=6)      # 1474, 1 ContentSequence
      (fffe,e000) na (Item with explicit length #=4)          # 112, 1 Item
        (0040,a010) CS [HAS OBS CONTEXT]                        #  16, 1 RelationshipType
        (0040,a040) CS [TEXT]                                   #   4, 1 ValueType
        (0040,a043) SQ (Sequence with explicit length #=1)      #  50, 1 ConceptNameCodeSequence
          (fffe,e000) na (Item with explicit length #=3)          #  42, 1 Item
            (0008,0100) SH [11951-1]                                #   8, 1 CodeValue
            (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
            (0008,0104) LO [Fetus ID]                               #   8, 1 CodeMeaning
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
        (0040,a160) UT [1]                                      #   2, 1 TextValue
      (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
      (fffe,e000) na (Item with explicit length #=5)          # 400, 1 Item
        (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
        (0040,a040) CS [NUM]                                    #   4, 1 ValueType
        (0040,a043) SQ (Sequence with explicit length #=1)      #  58, 1 ConceptNameCodeSequence
          (fffe,e000) na (Item with explicit length #=3)          #  50, 1 Item
            (0008,0100) SH [11727-5]                                #   8, 1 CodeValue
            (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
            (0008,0104) LO [Estimated Weight]                       #  16, 1 CodeMeaning
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
        (0040,a300) SQ (Sequence with explicit length #=1)      #  84, 1 MeasuredValueSequence
          (fffe,e000) na (Item with explicit length #=2)          #  76, 1 Item
            (0040,08ea) SQ (Sequence with explicit length #=1)      #  52, 1 MeasurementUnitsCodeSequence
              (fffe,e000) na (Item with explicit length #=4)          #  44, 1 Item
                (0008,0100) SH [g]                                      #   2, 1 CodeValue
                (0008,0102) SH [UCUM]                                   #   4, 1 CodingSchemeDesignator
                (0008,0103) SH [1.4]                                    #   4, 1 CodingSchemeVersion
                (0008,0104) LO [g]                                      #   2, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a30a) DS [114]                                    #   4, 1 NumericValue
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
        (0040,a730) SQ (Sequence with explicit length #=1)      # 194, 1 ContentSequence
          (fffe,e000) na (Item with explicit length #=4)          # 186, 1 Item
            (0040,a010) CS [INFERRED FROM]                          #  14, 1 RelationshipType
            (0040,a040) CS [CODE]                                   #   4, 1 ValueType
            (0040,a043) SQ (Sequence with explicit length #=1)      #  50, 1 ConceptNameCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  42, 1 Item
                (0008,0100) SH [121420]                                 #   6, 1 CodeValue
                (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
                (0008,0104) LO [Equation]                               #   8, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a168) SQ (Sequence with explicit length #=1)      #  78, 1 ConceptCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  70, 1 Item
                (0008,0100) SH [11732-5]                                #   8, 1 CodeValue
                (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
                (0008,0104) LO [EFW by AC, BPD, FL, HC, Hadlock 1985]   #  36, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
      (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
      (fffe,e000) na (Item with explicit length #=5)          # 396, 1 Item
        (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
        (0040,a040) CS [NUM]                                    #   4, 1 ValueType
        (0040,a043) SQ (Sequence with explicit length #=1)      #  66, 1 ConceptNameCodeSequence
          (fffe,e000) na (Item with explicit length #=3)          #  58, 1 Item
            (0008,0100) SH [99005-4]                                #   8, 1 CodeValue
            (0008,0102) SH [GEK]                                    #   4, 1 CodingSchemeDesignator
            (0008,0104) LO [Gestational Age by EFW]                 #  22, 1 CodeMeaning
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
        (0040,a300) SQ (Sequence with explicit length #=1)      #  88, 1 MeasuredValueSequence
          (fffe,e000) na (Item with explicit length #=2)          #  80, 1 Item
            (0040,08ea) SQ (Sequence with explicit length #=1)      #  56, 1 MeasurementUnitsCodeSequence
              (fffe,e000) na (Item with explicit length #=4)          #  48, 1 Item
                (0008,0100) SH [days]                                   #   4, 1 CodeValue
                (0008,0102) SH [UCUM]                                   #   4, 1 CodingSchemeDesignator
                (0008,0103) SH [1.4]                                    #   4, 1 CodingSchemeVersion
                (0008,0104) LO [days]                                   #   4, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a30a) DS [104]                                    #   4, 1 NumericValue
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
        (0040,a730) SQ (Sequence with explicit length #=1)      # 178, 1 ContentSequence
          (fffe,e000) na (Item with explicit length #=4)          # 170, 1 Item
            (0040,a010) CS [INFERRED FROM]                          #  14, 1 RelationshipType
            (0040,a040) CS [CODE]                                   #   4, 1 ValueType
            (0040,a043) SQ (Sequence with explicit length #=1)      #  50, 1 ConceptNameCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  42, 1 Item
                (0008,0100) SH [121420]                                 #   6, 1 CodeValue
                (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
                (0008,0104) LO [Equation]                               #   8, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a168) SQ (Sequence with explicit length #=1)      #  62, 1 ConceptCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  54, 1 Item
                (0008,0100) SH [99323-0]                                #   8, 1 CodeValue
                (0008,0102) SH [GEK]                                    #   4, 1 CodingSchemeDesignator
                (0008,0104) LO [EFW, Hadlock 1991]                      #  18, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
      (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
      (fffe,e000) na (Item with explicit length #=4)          # 204, 1 Item
        (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
        (0040,a040) CS [NUM]                                    #   4, 1 ValueType
        (0040,a043) SQ (Sequence with explicit length #=1)      #  64, 1 ConceptNameCodeSequence
          (fffe,e000) na (Item with explicit length #=3)          #  56, 1 Item
            (0008,0100) SH [11884-4]                                #   8, 1 CodeValue
            (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
            (0008,0104) LO [Average Ultrasound Age]                 #  22, 1 CodeMeaning
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
        (0040,a300) SQ (Sequence with explicit length #=1)      #  88, 1 MeasuredValueSequence
          (fffe,e000) na (Item with explicit length #=2)          #  80, 1 Item
            (0040,08ea) SQ (Sequence with explicit length #=1)      #  56, 1 MeasurementUnitsCodeSequence
              (fffe,e000) na (Item with explicit length #=4)          #  48, 1 Item
                (0008,0100) SH [days]                                   #   4, 1 CodeValue
                (0008,0102) SH [UCUM]                                   #   4, 1 CodingSchemeDesignator
                (0008,0103) SH [1.4]                                    #   4, 1 CodingSchemeVersion
                (0008,0104) LO [days]                                   #   4, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a30a) DS [102]                                    #   4, 1 NumericValue
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
      (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
      (fffe,e000) na (Item with explicit length #=4)          # 130, 1 Item
        (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
        (0040,a040) CS [DATE]                                   #   4, 1 ValueType
        (0040,a043) SQ (Sequence with explicit length #=1)      #  74, 1 ConceptNameCodeSequence
          (fffe,e000) na (Item with explicit length #=3)          #  66, 1 Item
            (0008,0100) SH [11781-2]                                #   8, 1 CodeValue
            (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
            (0008,0104) LO [EDD from average ultrasound age]        #  32, 1 CodeMeaning
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
        (0040,a121) DA [20250901]                               #   8, 1 Date
      (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
      (fffe,e000) na (Item with explicit length #=4)          # 184, 1 Item
        (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
        (0040,a040) CS [TEXT]                                   #   4, 1 ValueType
        (0040,a043) SQ (Sequence with explicit length #=1)      #  50, 1 ConceptNameCodeSequence
          (fffe,e000) na (Item with explicit length #=3)          #  42, 1 Item
            (0008,0100) SH [121106]                                 #   6, 1 CodeValue
            (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
            (0008,0104) LO [Comment]                                #   8, 1 CodeMeaning
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
        (0040,a160) UT [Caution: Measurement Inaccuracies may occur due to limitations of ... #  82, 1 TextValue
      (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
    (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
  (fffe,e000) na (Item with explicit length #=5)          # 2532, 1 Item
    (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
    (0040,a040) CS [CONTAINER]                              #  10, 1 ValueType
    (0040,a043) SQ (Sequence with explicit length #=1)      #  64, 1 ConceptNameCodeSequence
      (fffe,e000) na (Item with explicit length #=3)          #  56, 1 Item
        (0008,0100) SH [125001]                                 #   6, 1 CodeValue
        (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
        (0008,0104) LO [Fetus Biometry Ratios]                  #  22, 1 CodeMeaning
      (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
    (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
    (0040,a050) CS [SEPARATE]                               #   8, 1 ContinuityOfContent
    (0040,a730) SQ (Sequence with explicit length #=6)      # 2394, 1 ContentSequence
      (fffe,e000) na (Item with explicit length #=4)          # 112, 1 Item
        (0040,a010) CS [HAS OBS CONTEXT]                        #  16, 1 RelationshipType
        (0040,a040) CS [TEXT]                                   #   4, 1 ValueType
        (0040,a043) SQ (Sequence with explicit length #=1)      #  50, 1 ConceptNameCodeSequence
          (fffe,e000) na (Item with explicit length #=3)          #  42, 1 Item
            (0008,0100) SH [11951-1]                                #   8, 1 CodeValue
            (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
            (0008,0104) LO [Fetus ID]                               #   8, 1 CodeMeaning
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
        (0040,a160) UT [1]                                      #   2, 1 TextValue
      (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
      (fffe,e000) na (Item with explicit length #=5)          # 638, 1 Item
        (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
        (0040,a040) CS [NUM]                                    #   4, 1 ValueType
        (0040,a043) SQ (Sequence with explicit length #=1)      #  56, 1 ConceptNameCodeSequence
          (fffe,e000) na (Item with explicit length #=3)          #  48, 1 Item
            (0008,0100) SH [11823-2]                                #   8, 1 CodeValue
            (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
            (0008,0104) LO [Cephalic Index]                         #  14, 1 CodeMeaning
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
        (0040,a300) SQ (Sequence with explicit length #=1)      #  86, 1 MeasuredValueSequence
          (fffe,e000) na (Item with explicit length #=2)          #  78, 1 Item
            (0040,08ea) SQ (Sequence with explicit length #=1)      #  52, 1 MeasurementUnitsCodeSequence
              (fffe,e000) na (Item with explicit length #=4)          #  44, 1 Item
                (0008,0100) SH [%]                                      #   2, 1 CodeValue
                (0008,0102) SH [UCUM]                                   #   4, 1 CodingSchemeDesignator
                (0008,0103) SH [1.4]                                    #   4, 1 CodingSchemeVersion
                (0008,0104) LO [%]                                      #   2, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a30a) DS [64.58]                                  #   6, 1 NumericValue
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
        (0040,a730) SQ (Sequence with explicit length #=2)      # 432, 1 ContentSequence
          (fffe,e000) na (Item with explicit length #=4)          # 208, 1 Item
            (0040,a010) CS [HAS PROPERTIES]                         #  14, 1 RelationshipType
            (0040,a040) CS [NUM]                                    #   4, 1 ValueType
            (0040,a043) SQ (Sequence with explicit length #=1)      #  68, 1 ConceptNameCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  60, 1 Item
                (0008,0100) SH [R-10041]                                #   8, 1 CodeValue
                (0008,0102) SH [SRT]                                    #   4, 1 CodingSchemeDesignator
                (0008,0104) LO [Normal Range Lower Limit]               #  24, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a300) SQ (Sequence with explicit length #=1)      #  82, 1 MeasuredValueSequence
              (fffe,e000) na (Item with explicit length #=2)          #  74, 1 Item
                (0040,08ea) SQ (Sequence with explicit length #=1)      #  52, 1 MeasurementUnitsCodeSequence
                  (fffe,e000) na (Item with explicit length #=4)          #  44, 1 Item
                    (0008,0100) SH [%]                                      #   2, 1 CodeValue
                    (0008,0102) SH [UCUM]                                   #   4, 1 CodingSchemeDesignator
                    (0008,0103) SH [1.4]                                    #   4, 1 CodingSchemeVersion
                    (0008,0104) LO [%]                                      #   2, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a30a) DS [70]                                     #   2, 1 NumericValue
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
          (fffe,e000) na (Item with explicit length #=4)          # 208, 1 Item
            (0040,a010) CS [HAS PROPERTIES]                         #  14, 1 RelationshipType
            (0040,a040) CS [NUM]                                    #   4, 1 ValueType
            (0040,a043) SQ (Sequence with explicit length #=1)      #  68, 1 ConceptNameCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  60, 1 Item
                (0008,0100) SH [R-0038B]                                #   8, 1 CodeValue
                (0008,0102) SH [SRT]                                    #   4, 1 CodingSchemeDesignator
                (0008,0104) LO [Normal Range Upper Limit]               #  24, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a300) SQ (Sequence with explicit length #=1)      #  82, 1 MeasuredValueSequence
              (fffe,e000) na (Item with explicit length #=2)          #  74, 1 Item
                (0040,08ea) SQ (Sequence with explicit length #=1)      #  52, 1 MeasurementUnitsCodeSequence
                  (fffe,e000) na (Item with explicit length #=4)          #  44, 1 Item
                    (0008,0100) SH [%]                                      #   2, 1 CodeValue
                    (0008,0102) SH [UCUM]                                   #   4, 1 CodingSchemeDesignator
                    (0008,0103) SH [1.4]                                    #   4, 1 CodingSchemeVersion
                    (0008,0104) LO [%]                                      #   2, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a30a) DS [86]                                     #   2, 1 NumericValue
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
      (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
      (fffe,e000) na (Item with explicit length #=5)          # 390, 1 Item
        (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
        (0040,a040) CS [NUM]                                    #   4, 1 ValueType
        (0040,a043) SQ (Sequence with explicit length #=1)      #  48, 1 ConceptNameCodeSequence
          (fffe,e000) na (Item with explicit length #=3)          #  40, 1 Item
            (0008,0100) SH [11947-9]                                #   8, 1 CodeValue
            (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
            (0008,0104) LO [HC/AC]                                  #   6, 1 CodeMeaning
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
        (0040,a300) SQ (Sequence with explicit length #=1)      #  90, 1 MeasuredValueSequence
          (fffe,e000) na (Item with explicit length #=2)          #  82, 1 Item
            (0040,08ea) SQ (Sequence with explicit length #=1)      #  58, 1 MeasurementUnitsCodeSequence
              (fffe,e000) na (Item with explicit length #=4)          #  50, 1 Item
                (0008,0100) SH [1]                                      #   2, 1 CodeValue
                (0008,0102) SH [UCUM]                                   #   4, 1 CodingSchemeDesignator
                (0008,0103) SH [1.4]                                    #   4, 1 CodingSchemeVersion
                (0008,0104) LO [no unit]                                #   8, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a30a) DS [1.76]                                   #   4, 1 NumericValue
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
        (0040,a730) SQ (Sequence with explicit length #=1)      # 188, 1 ContentSequence
          (fffe,e000) na (Item with explicit length #=4)          # 180, 1 Item
            (0040,a010) CS [INFERRED FROM]                          #  14, 1 RelationshipType
            (0040,a040) CS [CODE]                                   #   4, 1 ValueType
            (0040,a043) SQ (Sequence with explicit length #=1)      #  64, 1 ConceptNameCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  56, 1 Item
                (0008,0100) SH [99324-0]                                #   8, 1 CodeValue
                (0008,0102) SH [GEK]                                    #   4, 1 CodingSchemeDesignator
                (0008,0104) LO [HC/AC, Campbell 1977]                   #  20, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a168) SQ (Sequence with explicit length #=1)      #  58, 1 ConceptCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  50, 1 Item
                (0008,0100) SH [121424]                                 #   6, 1 CodeValue
                (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
                (0008,0104) LO [Table of Values]                        #  16, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
      (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
      (fffe,e000) na (Item with explicit length #=5)          # 630, 1 Item
        (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
        (0040,a040) CS [NUM]                                    #   4, 1 ValueType
        (0040,a043) SQ (Sequence with explicit length #=1)      #  48, 1 ConceptNameCodeSequence
          (fffe,e000) na (Item with explicit length #=3)          #  40, 1 Item
            (0008,0100) SH [11871-1]                                #   8, 1 CodeValue
            (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
            (0008,0104) LO [FL/AC]                                  #   6, 1 CodeMeaning
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
        (0040,a300) SQ (Sequence with explicit length #=1)      #  86, 1 MeasuredValueSequence
          (fffe,e000) na (Item with explicit length #=2)          #  78, 1 Item
            (0040,08ea) SQ (Sequence with explicit length #=1)      #  52, 1 MeasurementUnitsCodeSequence
              (fffe,e000) na (Item with explicit length #=4)          #  44, 1 Item
                (0008,0100) SH [%]                                      #   2, 1 CodeValue
                (0008,0102) SH [UCUM]                                   #   4, 1 CodingSchemeDesignator
                (0008,0103) SH [1.4]                                    #   4, 1 CodingSchemeVersion
                (0008,0104) LO [%]                                      #   2, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a30a) DS [39.05]                                  #   6, 1 NumericValue
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
        (0040,a730) SQ (Sequence with explicit length #=2)      # 432, 1 ContentSequence
          (fffe,e000) na (Item with explicit length #=4)          # 208, 1 Item
            (0040,a010) CS [HAS PROPERTIES]                         #  14, 1 RelationshipType
            (0040,a040) CS [NUM]                                    #   4, 1 ValueType
            (0040,a043) SQ (Sequence with explicit length #=1)      #  68, 1 ConceptNameCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  60, 1 Item
                (0008,0100) SH [R-10041]                                #   8, 1 CodeValue
                (0008,0102) SH [SRT]                                    #   4, 1 CodingSchemeDesignator
                (0008,0104) LO [Normal Range Lower Limit]               #  24, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a300) SQ (Sequence with explicit length #=1)      #  82, 1 MeasuredValueSequence
              (fffe,e000) na (Item with explicit length #=2)          #  74, 1 Item
                (0040,08ea) SQ (Sequence with explicit length #=1)      #  52, 1 MeasurementUnitsCodeSequence
                  (fffe,e000) na (Item with explicit length #=4)          #  44, 1 Item
                    (0008,0100) SH [%]                                      #   2, 1 CodeValue
                    (0008,0102) SH [UCUM]                                   #   4, 1 CodingSchemeDesignator
                    (0008,0103) SH [1.4]                                    #   4, 1 CodingSchemeVersion
                    (0008,0104) LO [%]                                      #   2, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a30a) DS [20]                                     #   2, 1 NumericValue
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
          (fffe,e000) na (Item with explicit length #=4)          # 208, 1 Item
            (0040,a010) CS [HAS PROPERTIES]                         #  14, 1 RelationshipType
            (0040,a040) CS [NUM]                                    #   4, 1 ValueType
            (0040,a043) SQ (Sequence with explicit length #=1)      #  68, 1 ConceptNameCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  60, 1 Item
                (0008,0100) SH [R-0038B]                                #   8, 1 CodeValue
                (0008,0102) SH [SRT]                                    #   4, 1 CodingSchemeDesignator
                (0008,0104) LO [Normal Range Upper Limit]               #  24, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a300) SQ (Sequence with explicit length #=1)      #  82, 1 MeasuredValueSequence
              (fffe,e000) na (Item with explicit length #=2)          #  74, 1 Item
                (0040,08ea) SQ (Sequence with explicit length #=1)      #  52, 1 MeasurementUnitsCodeSequence
                  (fffe,e000) na (Item with explicit length #=4)          #  44, 1 Item
                    (0008,0100) SH [%]                                      #   2, 1 CodeValue
                    (0008,0102) SH [UCUM]                                   #   4, 1 CodingSchemeDesignator
                    (0008,0103) SH [1.4]                                    #   4, 1 CodingSchemeVersion
                    (0008,0104) LO [%]                                      #   2, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a30a) DS [24]                                     #   2, 1 NumericValue
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
      (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
      (fffe,e000) na (Item with explicit length #=4)          # 186, 1 Item
        (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
        (0040,a040) CS [NUM]                                    #   4, 1 ValueType
        (0040,a043) SQ (Sequence with explicit length #=1)      #  48, 1 ConceptNameCodeSequence
          (fffe,e000) na (Item with explicit length #=3)          #  40, 1 Item
            (0008,0100) SH [11872-9]                                #   8, 1 CodeValue
            (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
            (0008,0104) LO [FL/BPD]                                 #   6, 1 CodeMeaning
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
        (0040,a300) SQ (Sequence with explicit length #=1)      #  86, 1 MeasuredValueSequence
          (fffe,e000) na (Item with explicit length #=2)          #  78, 1 Item
            (0040,08ea) SQ (Sequence with explicit length #=1)      #  52, 1 MeasurementUnitsCodeSequence
              (fffe,e000) na (Item with explicit length #=4)          #  44, 1 Item
                (0008,0100) SH [%]                                      #   2, 1 CodeValue
                (0008,0102) SH [UCUM]                                   #   4, 1 CodingSchemeDesignator
                (0008,0103) SH [1.4]                                    #   4, 1 CodingSchemeVersion
                (0008,0104) LO [%]                                      #   2, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a30a) DS [100.84]                                 #   6, 1 NumericValue
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
      (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
      (fffe,e000) na (Item with explicit length #=5)          # 390, 1 Item
        (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
        (0040,a040) CS [NUM]                                    #   4, 1 ValueType
        (0040,a043) SQ (Sequence with explicit length #=1)      #  48, 1 ConceptNameCodeSequence
          (fffe,e000) na (Item with explicit length #=3)          #  40, 1 Item
            (0008,0100) SH [11873-7]                                #   8, 1 CodeValue
            (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
            (0008,0104) LO [FL/HC]                                  #   6, 1 CodeMeaning
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
        (0040,a300) SQ (Sequence with explicit length #=1)      #  90, 1 MeasuredValueSequence
          (fffe,e000) na (Item with explicit length #=2)          #  82, 1 Item
            (0040,08ea) SQ (Sequence with explicit length #=1)      #  58, 1 MeasurementUnitsCodeSequence
              (fffe,e000) na (Item with explicit length #=4)          #  50, 1 Item
                (0008,0100) SH [1]                                      #   2, 1 CodeValue
                (0008,0102) SH [UCUM]                                   #   4, 1 CodingSchemeDesignator
                (0008,0103) SH [1.4]                                    #   4, 1 CodingSchemeVersion
                (0008,0104) LO [no unit]                                #   8, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a30a) DS [0.22]                                   #   4, 1 NumericValue
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
        (0040,a730) SQ (Sequence with explicit length #=1)      # 188, 1 ContentSequence
          (fffe,e000) na (Item with explicit length #=4)          # 180, 1 Item
            (0040,a010) CS [INFERRED FROM]                          #  14, 1 RelationshipType
            (0040,a040) CS [CODE]                                   #   4, 1 ValueType
            (0040,a043) SQ (Sequence with explicit length #=1)      #  64, 1 ConceptNameCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  56, 1 Item
                (0008,0100) SH [99324-1]                                #   8, 1 CodeValue
                (0008,0102) SH [GEK]                                    #   4, 1 CodingSchemeDesignator
                (0008,0104) LO [FL/HC, Hadlock 1984]                    #  20, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a168) SQ (Sequence with explicit length #=1)      #  58, 1 ConceptCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  50, 1 Item
                (0008,0100) SH [121424]                                 #   6, 1 CodeValue
                (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
                (0008,0104) LO [Table of Values]                        #  16, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
      (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
    (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
  (fffe,e000) na (Item with explicit length #=5)          # 7564, 1 Item
    (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
    (0040,a040) CS [CONTAINER]                              #  10, 1 ValueType
    (0040,a043) SQ (Sequence with explicit length #=1)      #  56, 1 ConceptNameCodeSequence
      (fffe,e000) na (Item with explicit length #=3)          #  48, 1 Item
        (0008,0100) SH [125002]                                 #   6, 1 CodeValue
        (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
        (0008,0104) LO [Fetal Biometry]                         #  14, 1 CodeMeaning
      (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
    (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
    (0040,a050) CS [SEPARATE]                               #   8, 1 ContinuityOfContent
    (0040,a730) SQ (Sequence with explicit length #=9)      # 7434, 1 ContentSequence
      (fffe,e000) na (Item with explicit length #=4)          # 112, 1 Item
        (0040,a010) CS [HAS OBS CONTEXT]                        #  16, 1 RelationshipType
        (0040,a040) CS [TEXT]                                   #   4, 1 ValueType
        (0040,a043) SQ (Sequence with explicit length #=1)      #  50, 1 ConceptNameCodeSequence
          (fffe,e000) na (Item with explicit length #=3)          #  42, 1 Item
            (0008,0100) SH [11951-1]                                #   8, 1 CodeValue
            (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
            (0008,0104) LO [Fetus ID]                               #   8, 1 CodeMeaning
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
        (0040,a160) UT [1]                                      #   2, 1 TextValue
      (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
      (fffe,e000) na (Item with explicit length #=5)          # 1222, 1 Item
        (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
        (0040,a040) CS [CONTAINER]                              #  10, 1 ValueType
        (0040,a043) SQ (Sequence with explicit length #=1)      #  56, 1 ConceptNameCodeSequence
          (fffe,e000) na (Item with explicit length #=3)          #  48, 1 Item
            (0008,0100) SH [125005]                                 #   6, 1 CodeValue
            (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
            (0008,0104) LO [Biometry Group]                         #  14, 1 CodeMeaning
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
        (0040,a050) CS [SEPARATE]                               #   8, 1 ContinuityOfContent
        (0040,a730) SQ (Sequence with explicit length #=4)      # 1092, 1 ContentSequence
          (fffe,e000) na (Item with explicit length #=4)          # 198, 1 Item
            (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
            (0040,a040) CS [NUM]                                    #   4, 1 ValueType
            (0040,a043) SQ (Sequence with explicit length #=1)      #  62, 1 ConceptNameCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  54, 1 Item
                (0008,0100) SH [11820-8]                                #   8, 1 CodeValue
                (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
                (0008,0104) LO [Biparietal Diameter]                    #  20, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a300) SQ (Sequence with explicit length #=1)      #  84, 1 MeasuredValueSequence
              (fffe,e000) na (Item with explicit length #=2)          #  76, 1 Item
                (0040,08ea) SQ (Sequence with explicit length #=1)      #  52, 1 MeasurementUnitsCodeSequence
                  (fffe,e000) na (Item with explicit length #=4)          #  44, 1 Item
                    (0008,0100) SH [cm]                                     #   2, 1 CodeValue
                    (0008,0102) SH [UCUM]                                   #   4, 1 CodingSchemeDesignator
                    (0008,0103) SH [1.4]                                    #   4, 1 CodingSchemeVersion
                    (0008,0104) LO [cm]                                     #   2, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a30a) DS [2.37]                                   #   4, 1 NumericValue
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
          (fffe,e000) na (Item with explicit length #=5)          # 378, 1 Item
            (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
            (0040,a040) CS [NUM]                                    #   4, 1 ValueType
            (0040,a043) SQ (Sequence with explicit length #=1)      #  62, 1 ConceptNameCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  54, 1 Item
                (0008,0100) SH [11820-8]                                #   8, 1 CodeValue
                (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
                (0008,0104) LO [Biparietal Diameter]                    #  20, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a300) SQ (Sequence with explicit length #=1)      #  84, 1 MeasuredValueSequence
              (fffe,e000) na (Item with explicit length #=2)          #  76, 1 Item
                (0040,08ea) SQ (Sequence with explicit length #=1)      #  52, 1 MeasurementUnitsCodeSequence
                  (fffe,e000) na (Item with explicit length #=4)          #  44, 1 Item
                    (0008,0100) SH [cm]                                     #   2, 1 CodeValue
                    (0008,0102) SH [UCUM]                                   #   4, 1 CodingSchemeDesignator
                    (0008,0103) SH [1.4]                                    #   4, 1 CodingSchemeVersion
                    (0008,0104) LO [cm]                                     #   2, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a30a) DS [2.37]                                   #   4, 1 NumericValue
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a730) SQ (Sequence with explicit length #=1)      # 168, 1 ContentSequence
              (fffe,e000) na (Item with explicit length #=4)          # 160, 1 Item
                (0040,a010) CS [HAS CONCEPT MOD]                        #  16, 1 RelationshipType
                (0040,a040) CS [CODE]                                   #   4, 1 ValueType
                (0040,a043) SQ (Sequence with explicit length #=1)      #  52, 1 ConceptNameCodeSequence
                  (fffe,e000) na (Item with explicit length #=3)          #  44, 1 Item
                    (0008,0100) SH [121401]                                 #   6, 1 CodeValue
                    (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
                    (0008,0104) LO [Derivation]                             #  10, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a168) SQ (Sequence with explicit length #=1)      #  48, 1 ConceptCodeSequence
                  (fffe,e000) na (Item with explicit length #=3)          #  40, 1 Item
                    (0008,0100) SH [R-00317]                                #   8, 1 CodeValue
                    (0008,0102) SH [SRT]                                    #   4, 1 CodingSchemeDesignator
                    (0008,0104) LO [Mean]                                   #   4, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
          (fffe,e000) na (Item with explicit length #=5)          # 382, 1 Item
            (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
            (0040,a040) CS [NUM]                                    #   4, 1 ValueType
            (0040,a043) SQ (Sequence with explicit length #=1)      #  58, 1 ConceptNameCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  50, 1 Item
                (0008,0100) SH [18185-9]                                #   8, 1 CodeValue
                (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
                (0008,0104) LO [Gestational Age]                        #  16, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a300) SQ (Sequence with explicit length #=1)      #  84, 1 MeasuredValueSequence
              (fffe,e000) na (Item with explicit length #=2)          #  76, 1 Item
                (0040,08ea) SQ (Sequence with explicit length #=1)      #  54, 1 MeasurementUnitsCodeSequence
                  (fffe,e000) na (Item with explicit length #=4)          #  46, 1 Item
                    (0008,0100) SH [d]                                      #   2, 1 CodeValue
                    (0008,0102) SH [UCUM]                                   #   4, 1 CodingSchemeDesignator
                    (0008,0103) SH [1.4]                                    #   4, 1 CodingSchemeVersion
                    (0008,0104) LO [days]                                   #   4, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a30a) DS [98]                                     #   2, 1 NumericValue
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a730) SQ (Sequence with explicit length #=1)      # 176, 1 ContentSequence
              (fffe,e000) na (Item with explicit length #=4)          # 168, 1 Item
                (0040,a010) CS [INFERRED FROM]                          #  14, 1 RelationshipType
                (0040,a040) CS [CODE]                                   #   4, 1 ValueType
                (0040,a043) SQ (Sequence with explicit length #=1)      #  50, 1 ConceptNameCodeSequence
                  (fffe,e000) na (Item with explicit length #=3)          #  42, 1 Item
                    (0008,0100) SH [121420]                                 #   6, 1 CodeValue
                    (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
                    (0008,0104) LO [Equation]                               #   8, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a168) SQ (Sequence with explicit length #=1)      #  60, 1 ConceptCodeSequence
                  (fffe,e000) na (Item with explicit length #=3)          #  52, 1 Item
                    (0008,0100) SH [11902-4]                                #   8, 1 CodeValue
                    (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
                    (0008,0104) LO [BPD, Hadlock 1984]                      #  18, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
          (fffe,e000) na (Item with explicit length #=4)          # 102, 1 Item
            (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
            (0040,a040) CS [DATE]                                   #   4, 1 ValueType
            (0040,a043) SQ (Sequence with explicit length #=1)      #  46, 1 ConceptNameCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  38, 1 Item
                (0008,0100) SH [11778-8]                                #   8, 1 CodeValue
                (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
                (0008,0104) LO [EDD]                                    #   4, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a121) DA [20250905]                               #   8, 1 Date
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
      (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
      (fffe,e000) na (Item with explicit length #=5)          # 734, 1 Item
        (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
        (0040,a040) CS [CONTAINER]                              #  10, 1 ValueType
        (0040,a043) SQ (Sequence with explicit length #=1)      #  56, 1 ConceptNameCodeSequence
          (fffe,e000) na (Item with explicit length #=3)          #  48, 1 Item
            (0008,0100) SH [125005]                                 #   6, 1 CodeValue
            (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
            (0008,0104) LO [Biometry Group]                         #  14, 1 CodeMeaning
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
        (0040,a050) CS [SEPARATE]                               #   8, 1 ContinuityOfContent
        (0040,a730) SQ (Sequence with explicit length #=2)      # 604, 1 ContentSequence
          (fffe,e000) na (Item with explicit length #=4)          # 204, 1 Item
            (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
            (0040,a040) CS [NUM]                                    #   4, 1 ValueType
            (0040,a043) SQ (Sequence with explicit length #=1)      #  68, 1 ConceptNameCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  60, 1 Item
                (0008,0100) SH [11851-3]                                #   8, 1 CodeValue
                (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
                (0008,0104) LO [Occipital-Frontal Diameter]             #  26, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a300) SQ (Sequence with explicit length #=1)      #  84, 1 MeasuredValueSequence
              (fffe,e000) na (Item with explicit length #=2)          #  76, 1 Item
                (0040,08ea) SQ (Sequence with explicit length #=1)      #  52, 1 MeasurementUnitsCodeSequence
                  (fffe,e000) na (Item with explicit length #=4)          #  44, 1 Item
                    (0008,0100) SH [cm]                                     #   2, 1 CodeValue
                    (0008,0102) SH [UCUM]                                   #   4, 1 CodingSchemeDesignator
                    (0008,0103) SH [1.4]                                    #   4, 1 CodingSchemeVersion
                    (0008,0104) LO [cm]                                     #   2, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a30a) DS [3.67]                                   #   4, 1 NumericValue
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
          (fffe,e000) na (Item with explicit length #=5)          # 384, 1 Item
            (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
            (0040,a040) CS [NUM]                                    #   4, 1 ValueType
            (0040,a043) SQ (Sequence with explicit length #=1)      #  68, 1 ConceptNameCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  60, 1 Item
                (0008,0100) SH [11851-3]                                #   8, 1 CodeValue
                (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
                (0008,0104) LO [Occipital-Frontal Diameter]             #  26, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a300) SQ (Sequence with explicit length #=1)      #  84, 1 MeasuredValueSequence
              (fffe,e000) na (Item with explicit length #=2)          #  76, 1 Item
                (0040,08ea) SQ (Sequence with explicit length #=1)      #  52, 1 MeasurementUnitsCodeSequence
                  (fffe,e000) na (Item with explicit length #=4)          #  44, 1 Item
                    (0008,0100) SH [cm]                                     #   2, 1 CodeValue
                    (0008,0102) SH [UCUM]                                   #   4, 1 CodingSchemeDesignator
                    (0008,0103) SH [1.4]                                    #   4, 1 CodingSchemeVersion
                    (0008,0104) LO [cm]                                     #   2, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a30a) DS [3.67]                                   #   4, 1 NumericValue
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a730) SQ (Sequence with explicit length #=1)      # 168, 1 ContentSequence
              (fffe,e000) na (Item with explicit length #=4)          # 160, 1 Item
                (0040,a010) CS [HAS CONCEPT MOD]                        #  16, 1 RelationshipType
                (0040,a040) CS [CODE]                                   #   4, 1 ValueType
                (0040,a043) SQ (Sequence with explicit length #=1)      #  52, 1 ConceptNameCodeSequence
                  (fffe,e000) na (Item with explicit length #=3)          #  44, 1 Item
                    (0008,0100) SH [121401]                                 #   6, 1 CodeValue
                    (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
                    (0008,0104) LO [Derivation]                             #  10, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a168) SQ (Sequence with explicit length #=1)      #  48, 1 ConceptCodeSequence
                  (fffe,e000) na (Item with explicit length #=3)          #  40, 1 Item
                    (0008,0100) SH [R-00317]                                #   8, 1 CodeValue
                    (0008,0102) SH [SRT]                                    #   4, 1 CodingSchemeDesignator
                    (0008,0104) LO [Mean]                                   #   4, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
      (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
      (fffe,e000) na (Item with explicit length #=5)          # 1220, 1 Item
        (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
        (0040,a040) CS [CONTAINER]                              #  10, 1 ValueType
        (0040,a043) SQ (Sequence with explicit length #=1)      #  56, 1 ConceptNameCodeSequence
          (fffe,e000) na (Item with explicit length #=3)          #  48, 1 Item
            (0008,0100) SH [125005]                                 #   6, 1 CodeValue
            (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
            (0008,0104) LO [Biometry Group]                         #  14, 1 CodeMeaning
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
        (0040,a050) CS [SEPARATE]                               #   8, 1 ContinuityOfContent
        (0040,a730) SQ (Sequence with explicit length #=4)      # 1090, 1 ContentSequence
          (fffe,e000) na (Item with explicit length #=4)          # 198, 1 Item
            (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
            (0040,a040) CS [NUM]                                    #   4, 1 ValueType
            (0040,a043) SQ (Sequence with explicit length #=1)      #  60, 1 ConceptNameCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  52, 1 Item
                (0008,0100) SH [11984-2]                                #   8, 1 CodeValue
                (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
                (0008,0104) LO [Head Circumference]                     #  18, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a300) SQ (Sequence with explicit length #=1)      #  86, 1 MeasuredValueSequence
              (fffe,e000) na (Item with explicit length #=2)          #  78, 1 Item
                (0040,08ea) SQ (Sequence with explicit length #=1)      #  52, 1 MeasurementUnitsCodeSequence
                  (fffe,e000) na (Item with explicit length #=4)          #  44, 1 Item
                    (0008,0100) SH [cm]                                     #   2, 1 CodeValue
                    (0008,0102) SH [UCUM]                                   #   4, 1 CodingSchemeDesignator
                    (0008,0103) SH [1.4]                                    #   4, 1 CodingSchemeVersion
                    (0008,0104) LO [cm]                                     #   2, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a30a) DS [10.77]                                  #   6, 1 NumericValue
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
          (fffe,e000) na (Item with explicit length #=5)          # 378, 1 Item
            (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
            (0040,a040) CS [NUM]                                    #   4, 1 ValueType
            (0040,a043) SQ (Sequence with explicit length #=1)      #  60, 1 ConceptNameCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  52, 1 Item
                (0008,0100) SH [11984-2]                                #   8, 1 CodeValue
                (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
                (0008,0104) LO [Head Circumference]                     #  18, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a300) SQ (Sequence with explicit length #=1)      #  86, 1 MeasuredValueSequence
              (fffe,e000) na (Item with explicit length #=2)          #  78, 1 Item
                (0040,08ea) SQ (Sequence with explicit length #=1)      #  52, 1 MeasurementUnitsCodeSequence
                  (fffe,e000) na (Item with explicit length #=4)          #  44, 1 Item
                    (0008,0100) SH [cm]                                     #   2, 1 CodeValue
                    (0008,0102) SH [UCUM]                                   #   4, 1 CodingSchemeDesignator
                    (0008,0103) SH [1.4]                                    #   4, 1 CodingSchemeVersion
                    (0008,0104) LO [cm]                                     #   2, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a30a) DS [10.77]                                  #   6, 1 NumericValue
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a730) SQ (Sequence with explicit length #=1)      # 168, 1 ContentSequence
              (fffe,e000) na (Item with explicit length #=4)          # 160, 1 Item
                (0040,a010) CS [HAS CONCEPT MOD]                        #  16, 1 RelationshipType
                (0040,a040) CS [CODE]                                   #   4, 1 ValueType
                (0040,a043) SQ (Sequence with explicit length #=1)      #  52, 1 ConceptNameCodeSequence
                  (fffe,e000) na (Item with explicit length #=3)          #  44, 1 Item
                    (0008,0100) SH [121401]                                 #   6, 1 CodeValue
                    (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
                    (0008,0104) LO [Derivation]                             #  10, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a168) SQ (Sequence with explicit length #=1)      #  48, 1 ConceptCodeSequence
                  (fffe,e000) na (Item with explicit length #=3)          #  40, 1 Item
                    (0008,0100) SH [R-00317]                                #   8, 1 CodeValue
                    (0008,0102) SH [SRT]                                    #   4, 1 CodingSchemeDesignator
                    (0008,0104) LO [Mean]                                   #   4, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
          (fffe,e000) na (Item with explicit length #=5)          # 380, 1 Item
            (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
            (0040,a040) CS [NUM]                                    #   4, 1 ValueType
            (0040,a043) SQ (Sequence with explicit length #=1)      #  58, 1 ConceptNameCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  50, 1 Item
                (0008,0100) SH [18185-9]                                #   8, 1 CodeValue
                (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
                (0008,0104) LO [Gestational Age]                        #  16, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a300) SQ (Sequence with explicit length #=1)      #  86, 1 MeasuredValueSequence
              (fffe,e000) na (Item with explicit length #=2)          #  78, 1 Item
                (0040,08ea) SQ (Sequence with explicit length #=1)      #  54, 1 MeasurementUnitsCodeSequence
                  (fffe,e000) na (Item with explicit length #=4)          #  46, 1 Item
                    (0008,0100) SH [d]                                      #   2, 1 CodeValue
                    (0008,0102) SH [UCUM]                                   #   4, 1 CodingSchemeDesignator
                    (0008,0103) SH [1.4]                                    #   4, 1 CodingSchemeVersion
                    (0008,0104) LO [days]                                   #   4, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a30a) DS [106]                                    #   4, 1 NumericValue
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a730) SQ (Sequence with explicit length #=1)      # 172, 1 ContentSequence
              (fffe,e000) na (Item with explicit length #=4)          # 164, 1 Item
                (0040,a010) CS [INFERRED FROM]                          #  14, 1 RelationshipType
                (0040,a040) CS [CODE]                                   #   4, 1 ValueType
                (0040,a043) SQ (Sequence with explicit length #=1)      #  50, 1 ConceptNameCodeSequence
                  (fffe,e000) na (Item with explicit length #=3)          #  42, 1 Item
                    (0008,0100) SH [121420]                                 #   6, 1 CodeValue
                    (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
                    (0008,0104) LO [Equation]                               #   8, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a168) SQ (Sequence with explicit length #=1)      #  56, 1 ConceptCodeSequence
                  (fffe,e000) na (Item with explicit length #=3)          #  48, 1 Item
                    (0008,0100) SH [99313-0]                                #   8, 1 CodeValue
                    (0008,0102) SH [GEK]                                    #   4, 1 CodingSchemeDesignator
                    (0008,0104) LO [HC, Hadlock]                            #  12, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
          (fffe,e000) na (Item with explicit length #=4)          # 102, 1 Item
            (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
            (0040,a040) CS [DATE]                                   #   4, 1 ValueType
            (0040,a043) SQ (Sequence with explicit length #=1)      #  46, 1 ConceptNameCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  38, 1 Item
                (0008,0100) SH [11778-8]                                #   8, 1 CodeValue
                (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
                (0008,0104) LO [EDD]                                    #   4, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a121) DA [20250828]                               #   8, 1 Date
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
      (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
      (fffe,e000) na (Item with explicit length #=5)          # 1228, 1 Item
        (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
        (0040,a040) CS [CONTAINER]                              #  10, 1 ValueType
        (0040,a043) SQ (Sequence with explicit length #=1)      #  56, 1 ConceptNameCodeSequence
          (fffe,e000) na (Item with explicit length #=3)          #  48, 1 Item
            (0008,0100) SH [125005]                                 #   6, 1 CodeValue
            (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
            (0008,0104) LO [Biometry Group]                         #  14, 1 CodeMeaning
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
        (0040,a050) CS [SEPARATE]                               #   8, 1 ContinuityOfContent
        (0040,a730) SQ (Sequence with explicit length #=4)      # 1098, 1 ContentSequence
          (fffe,e000) na (Item with explicit length #=4)          # 202, 1 Item
            (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
            (0040,a040) CS [NUM]                                    #   4, 1 ValueType
            (0040,a043) SQ (Sequence with explicit length #=1)      #  66, 1 ConceptNameCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  58, 1 Item
                (0008,0100) SH [11979-2]                                #   8, 1 CodeValue
                (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
                (0008,0104) LO [Abdominal Circumference]                #  24, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a300) SQ (Sequence with explicit length #=1)      #  84, 1 MeasuredValueSequence
              (fffe,e000) na (Item with explicit length #=2)          #  76, 1 Item
                (0040,08ea) SQ (Sequence with explicit length #=1)      #  52, 1 MeasurementUnitsCodeSequence
                  (fffe,e000) na (Item with explicit length #=4)          #  44, 1 Item
                    (0008,0100) SH [cm]                                     #   2, 1 CodeValue
                    (0008,0102) SH [UCUM]                                   #   4, 1 CodingSchemeDesignator
                    (0008,0103) SH [1.4]                                    #   4, 1 CodingSchemeVersion
                    (0008,0104) LO [cm]                                     #   2, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a30a) DS [6.12]                                   #   4, 1 NumericValue
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
          (fffe,e000) na (Item with explicit length #=5)          # 382, 1 Item
            (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
            (0040,a040) CS [NUM]                                    #   4, 1 ValueType
            (0040,a043) SQ (Sequence with explicit length #=1)      #  66, 1 ConceptNameCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  58, 1 Item
                (0008,0100) SH [11979-2]                                #   8, 1 CodeValue
                (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
                (0008,0104) LO [Abdominal Circumference]                #  24, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a300) SQ (Sequence with explicit length #=1)      #  84, 1 MeasuredValueSequence
              (fffe,e000) na (Item with explicit length #=2)          #  76, 1 Item
                (0040,08ea) SQ (Sequence with explicit length #=1)      #  52, 1 MeasurementUnitsCodeSequence
                  (fffe,e000) na (Item with explicit length #=4)          #  44, 1 Item
                    (0008,0100) SH [cm]                                     #   2, 1 CodeValue
                    (0008,0102) SH [UCUM]                                   #   4, 1 CodingSchemeDesignator
                    (0008,0103) SH [1.4]                                    #   4, 1 CodingSchemeVersion
                    (0008,0104) LO [cm]                                     #   2, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a30a) DS [6.12]                                   #   4, 1 NumericValue
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a730) SQ (Sequence with explicit length #=1)      # 168, 1 ContentSequence
              (fffe,e000) na (Item with explicit length #=4)          # 160, 1 Item
                (0040,a010) CS [HAS CONCEPT MOD]                        #  16, 1 RelationshipType
                (0040,a040) CS [CODE]                                   #   4, 1 ValueType
                (0040,a043) SQ (Sequence with explicit length #=1)      #  52, 1 ConceptNameCodeSequence
                  (fffe,e000) na (Item with explicit length #=3)          #  44, 1 Item
                    (0008,0100) SH [121401]                                 #   6, 1 CodeValue
                    (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
                    (0008,0104) LO [Derivation]                             #  10, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a168) SQ (Sequence with explicit length #=1)      #  48, 1 ConceptCodeSequence
                  (fffe,e000) na (Item with explicit length #=3)          #  40, 1 Item
                    (0008,0100) SH [R-00317]                                #   8, 1 CodeValue
                    (0008,0102) SH [SRT]                                    #   4, 1 CodingSchemeDesignator
                    (0008,0104) LO [Mean]                                   #   4, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
          (fffe,e000) na (Item with explicit length #=5)          # 380, 1 Item
            (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
            (0040,a040) CS [NUM]                                    #   4, 1 ValueType
            (0040,a043) SQ (Sequence with explicit length #=1)      #  58, 1 ConceptNameCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  50, 1 Item
                (0008,0100) SH [18185-9]                                #   8, 1 CodeValue
                (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
                (0008,0104) LO [Gestational Age]                        #  16, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a300) SQ (Sequence with explicit length #=1)      #  84, 1 MeasuredValueSequence
              (fffe,e000) na (Item with explicit length #=2)          #  76, 1 Item
                (0040,08ea) SQ (Sequence with explicit length #=1)      #  54, 1 MeasurementUnitsCodeSequence
                  (fffe,e000) na (Item with explicit length #=4)          #  46, 1 Item
                    (0008,0100) SH [d]                                      #   2, 1 CodeValue
                    (0008,0102) SH [UCUM]                                   #   4, 1 CodingSchemeDesignator
                    (0008,0103) SH [1.4]                                    #   4, 1 CodingSchemeVersion
                    (0008,0104) LO [days]                                   #   4, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a30a) DS [90]                                     #   2, 1 NumericValue
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a730) SQ (Sequence with explicit length #=1)      # 174, 1 ContentSequence
              (fffe,e000) na (Item with explicit length #=4)          # 166, 1 Item
                (0040,a010) CS [INFERRED FROM]                          #  14, 1 RelationshipType
                (0040,a040) CS [CODE]                                   #   4, 1 ValueType
                (0040,a043) SQ (Sequence with explicit length #=1)      #  50, 1 ConceptNameCodeSequence
                  (fffe,e000) na (Item with explicit length #=3)          #  42, 1 Item
                    (0008,0100) SH [121420]                                 #   6, 1 CodeValue
                    (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
                    (0008,0104) LO [Equation]                               #   8, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a168) SQ (Sequence with explicit length #=1)      #  58, 1 ConceptCodeSequence
                  (fffe,e000) na (Item with explicit length #=3)          #  50, 1 Item
                    (0008,0100) SH [11892-7]                                #   8, 1 CodeValue
                    (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
                    (0008,0104) LO [AC, Hadlock 1984]                       #  16, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
          (fffe,e000) na (Item with explicit length #=4)          # 102, 1 Item
            (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
            (0040,a040) CS [DATE]                                   #   4, 1 ValueType
            (0040,a043) SQ (Sequence with explicit length #=1)      #  46, 1 ConceptNameCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  38, 1 Item
                (0008,0100) SH [11778-8]                                #   8, 1 CodeValue
                (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
                (0008,0104) LO [EDD]                                    #   4, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a121) DA [20250913]                               #   8, 1 Date
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
      (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
      (fffe,e000) na (Item with explicit length #=4)          # 118, 1 Item
        (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
        (0040,a040) CS [CONTAINER]                              #  10, 1 ValueType
        (0040,a043) SQ (Sequence with explicit length #=1)      #  56, 1 ConceptNameCodeSequence
          (fffe,e000) na (Item with explicit length #=3)          #  48, 1 Item
            (0008,0100) SH [125005]                                 #   6, 1 CodeValue
            (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
            (0008,0104) LO [Biometry Group]                         #  14, 1 CodeMeaning
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
        (0040,a050) CS [SEPARATE]                               #   8, 1 ContinuityOfContent
      (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
      (fffe,e000) na (Item with explicit length #=4)          # 118, 1 Item
        (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
        (0040,a040) CS [CONTAINER]                              #  10, 1 ValueType
        (0040,a043) SQ (Sequence with explicit length #=1)      #  56, 1 ConceptNameCodeSequence
          (fffe,e000) na (Item with explicit length #=3)          #  48, 1 Item
            (0008,0100) SH [125005]                                 #   6, 1 CodeValue
            (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
            (0008,0104) LO [Biometry Group]                         #  14, 1 CodeMeaning
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
        (0040,a050) CS [SEPARATE]                               #   8, 1 ContinuityOfContent
      (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
      (fffe,e000) na (Item with explicit length #=5)          # 1206, 1 Item
        (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
        (0040,a040) CS [CONTAINER]                              #  10, 1 ValueType
        (0040,a043) SQ (Sequence with explicit length #=1)      #  56, 1 ConceptNameCodeSequence
          (fffe,e000) na (Item with explicit length #=3)          #  48, 1 Item
            (0008,0100) SH [125005]                                 #   6, 1 CodeValue
            (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
            (0008,0104) LO [Biometry Group]                         #  14, 1 CodeMeaning
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
        (0040,a050) CS [SEPARATE]                               #   8, 1 ContinuityOfContent
        (0040,a730) SQ (Sequence with explicit length #=4)      # 1076, 1 ContentSequence
          (fffe,e000) na (Item with explicit length #=4)          # 190, 1 Item
            (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
            (0040,a040) CS [NUM]                                    #   4, 1 ValueType
            (0040,a043) SQ (Sequence with explicit length #=1)      #  54, 1 ConceptNameCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  46, 1 Item
                (0008,0100) SH [11963-6]                                #   8, 1 CodeValue
                (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
                (0008,0104) LO [Femur Length]                           #  12, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a300) SQ (Sequence with explicit length #=1)      #  84, 1 MeasuredValueSequence
              (fffe,e000) na (Item with explicit length #=2)          #  76, 1 Item
                (0040,08ea) SQ (Sequence with explicit length #=1)      #  52, 1 MeasurementUnitsCodeSequence
                  (fffe,e000) na (Item with explicit length #=4)          #  44, 1 Item
                    (0008,0100) SH [cm]                                     #   2, 1 CodeValue
                    (0008,0102) SH [UCUM]                                   #   4, 1 CodingSchemeDesignator
                    (0008,0103) SH [1.4]                                    #   4, 1 CodingSchemeVersion
                    (0008,0104) LO [cm]                                     #   2, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a30a) DS [2.39]                                   #   4, 1 NumericValue
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
          (fffe,e000) na (Item with explicit length #=5)          # 370, 1 Item
            (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
            (0040,a040) CS [NUM]                                    #   4, 1 ValueType
            (0040,a043) SQ (Sequence with explicit length #=1)      #  54, 1 ConceptNameCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  46, 1 Item
                (0008,0100) SH [11963-6]                                #   8, 1 CodeValue
                (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
                (0008,0104) LO [Femur Length]                           #  12, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a300) SQ (Sequence with explicit length #=1)      #  84, 1 MeasuredValueSequence
              (fffe,e000) na (Item with explicit length #=2)          #  76, 1 Item
                (0040,08ea) SQ (Sequence with explicit length #=1)      #  52, 1 MeasurementUnitsCodeSequence
                  (fffe,e000) na (Item with explicit length #=4)          #  44, 1 Item
                    (0008,0100) SH [cm]                                     #   2, 1 CodeValue
                    (0008,0102) SH [UCUM]                                   #   4, 1 CodingSchemeDesignator
                    (0008,0103) SH [1.4]                                    #   4, 1 CodingSchemeVersion
                    (0008,0104) LO [cm]                                     #   2, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a30a) DS [2.39]                                   #   4, 1 NumericValue
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a730) SQ (Sequence with explicit length #=1)      # 168, 1 ContentSequence
              (fffe,e000) na (Item with explicit length #=4)          # 160, 1 Item
                (0040,a010) CS [HAS CONCEPT MOD]                        #  16, 1 RelationshipType
                (0040,a040) CS [CODE]                                   #   4, 1 ValueType
                (0040,a043) SQ (Sequence with explicit length #=1)      #  52, 1 ConceptNameCodeSequence
                  (fffe,e000) na (Item with explicit length #=3)          #  44, 1 Item
                    (0008,0100) SH [121401]                                 #   6, 1 CodeValue
                    (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
                    (0008,0104) LO [Derivation]                             #  10, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a168) SQ (Sequence with explicit length #=1)      #  48, 1 ConceptCodeSequence
                  (fffe,e000) na (Item with explicit length #=3)          #  40, 1 Item
                    (0008,0100) SH [R-00317]                                #   8, 1 CodeValue
                    (0008,0102) SH [SRT]                                    #   4, 1 CodingSchemeDesignator
                    (0008,0104) LO [Mean]                                   #   4, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
          (fffe,e000) na (Item with explicit length #=5)          # 382, 1 Item
            (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
            (0040,a040) CS [NUM]                                    #   4, 1 ValueType
            (0040,a043) SQ (Sequence with explicit length #=1)      #  58, 1 ConceptNameCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  50, 1 Item
                (0008,0100) SH [18185-9]                                #   8, 1 CodeValue
                (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
                (0008,0104) LO [Gestational Age]                        #  16, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a300) SQ (Sequence with explicit length #=1)      #  86, 1 MeasuredValueSequence
              (fffe,e000) na (Item with explicit length #=2)          #  78, 1 Item
                (0040,08ea) SQ (Sequence with explicit length #=1)      #  54, 1 MeasurementUnitsCodeSequence
                  (fffe,e000) na (Item with explicit length #=4)          #  46, 1 Item
                    (0008,0100) SH [d]                                      #   2, 1 CodeValue
                    (0008,0102) SH [UCUM]                                   #   4, 1 CodingSchemeDesignator
                    (0008,0103) SH [1.4]                                    #   4, 1 CodingSchemeVersion
                    (0008,0104) LO [days]                                   #   4, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a30a) DS [120]                                    #   4, 1 NumericValue
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a730) SQ (Sequence with explicit length #=1)      # 174, 1 ContentSequence
              (fffe,e000) na (Item with explicit length #=4)          # 166, 1 Item
                (0040,a010) CS [INFERRED FROM]                          #  14, 1 RelationshipType
                (0040,a040) CS [CODE]                                   #   4, 1 ValueType
                (0040,a043) SQ (Sequence with explicit length #=1)      #  50, 1 ConceptNameCodeSequence
                  (fffe,e000) na (Item with explicit length #=3)          #  42, 1 Item
                    (0008,0100) SH [121420]                                 #   6, 1 CodeValue
                    (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
                    (0008,0104) LO [Equation]                               #   8, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a168) SQ (Sequence with explicit length #=1)      #  58, 1 ConceptCodeSequence
                  (fffe,e000) na (Item with explicit length #=3)          #  50, 1 Item
                    (0008,0100) SH [11920-6]                                #   8, 1 CodeValue
                    (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
                    (0008,0104) LO [FL, Hadlock 1984]                       #  16, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
          (fffe,e000) na (Item with explicit length #=4)          # 102, 1 Item
            (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
            (0040,a040) CS [DATE]                                   #   4, 1 ValueType
            (0040,a043) SQ (Sequence with explicit length #=1)      #  46, 1 ConceptNameCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  38, 1 Item
                (0008,0100) SH [11778-8]                                #   8, 1 CodeValue
                (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
                (0008,0104) LO [EDD]                                    #   4, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a121) DA [20250814]                               #   8, 1 Date
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
      (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
      (fffe,e000) na (Item with explicit length #=5)          # 1404, 1 Item
        (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
        (0040,a040) CS [CONTAINER]                              #  10, 1 ValueType
        (0040,a043) SQ (Sequence with explicit length #=1)      #  56, 1 ConceptNameCodeSequence
          (fffe,e000) na (Item with explicit length #=3)          #  48, 1 Item
            (0008,0100) SH [125005]                                 #   6, 1 CodeValue
            (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
            (0008,0104) LO [Biometry Group]                         #  14, 1 CodeMeaning
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
        (0040,a050) CS [SEPARATE]                               #   8, 1 ContinuityOfContent
        (0040,a730) SQ (Sequence with explicit length #=4)      # 1274, 1 ContentSequence
          (fffe,e000) na (Item with explicit length #=5)          # 380, 1 Item
            (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
            (0040,a040) CS [NUM]                                    #   4, 1 ValueType
            (0040,a043) SQ (Sequence with explicit length #=1)      #  60, 1 ConceptNameCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  52, 1 Item
                (0008,0100) SH [11984-2]                                #   8, 1 CodeValue
                (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
                (0008,0104) LO [Head Circumference]                     #  18, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a300) SQ (Sequence with explicit length #=1)      #  84, 1 MeasuredValueSequence
              (fffe,e000) na (Item with explicit length #=2)          #  76, 1 Item
                (0040,08ea) SQ (Sequence with explicit length #=1)      #  52, 1 MeasurementUnitsCodeSequence
                  (fffe,e000) na (Item with explicit length #=4)          #  44, 1 Item
                    (0008,0100) SH [cm]                                     #   2, 1 CodeValue
                    (0008,0102) SH [UCUM]                                   #   4, 1 CodingSchemeDesignator
                    (0008,0103) SH [1.4]                                    #   4, 1 CodingSchemeVersion
                    (0008,0104) LO [cm]                                     #   2, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a30a) DS [9.60]                                   #   4, 1 NumericValue
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a730) SQ (Sequence with explicit length #=1)      # 172, 1 ContentSequence
              (fffe,e000) na (Item with explicit length #=4)          # 164, 1 Item
                (0040,a010) CS [HAS CONCEPT MOD]                        #  16, 1 RelationshipType
                (0040,a040) CS [CODE]                                   #   4, 1 ValueType
                (0040,a043) SQ (Sequence with explicit length #=1)      #  52, 1 ConceptNameCodeSequence
                  (fffe,e000) na (Item with explicit length #=3)          #  44, 1 Item
                    (0008,0100) SH [121401]                                 #   6, 1 CodeValue
                    (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
                    (0008,0104) LO [Derivation]                             #  10, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a168) SQ (Sequence with explicit length #=1)      #  52, 1 ConceptCodeSequence
                  (fffe,e000) na (Item with explicit length #=3)          #  44, 1 Item
                    (0008,0100) SH [121428]                                 #   6, 1 CodeValue
                    (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
                    (0008,0104) LO [Calculated]                             #  10, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
          (fffe,e000) na (Item with explicit length #=5)          # 380, 1 Item
            (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
            (0040,a040) CS [NUM]                                    #   4, 1 ValueType
            (0040,a043) SQ (Sequence with explicit length #=1)      #  60, 1 ConceptNameCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  52, 1 Item
                (0008,0100) SH [11984-2]                                #   8, 1 CodeValue
                (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
                (0008,0104) LO [Head Circumference]                     #  18, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a300) SQ (Sequence with explicit length #=1)      #  84, 1 MeasuredValueSequence
              (fffe,e000) na (Item with explicit length #=2)          #  76, 1 Item
                (0040,08ea) SQ (Sequence with explicit length #=1)      #  52, 1 MeasurementUnitsCodeSequence
                  (fffe,e000) na (Item with explicit length #=4)          #  44, 1 Item
                    (0008,0100) SH [cm]                                     #   2, 1 CodeValue
                    (0008,0102) SH [UCUM]                                   #   4, 1 CodingSchemeDesignator
                    (0008,0103) SH [1.4]                                    #   4, 1 CodingSchemeVersion
                    (0008,0104) LO [cm]                                     #   2, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a30a) DS [9.60]                                   #   4, 1 NumericValue
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a730) SQ (Sequence with explicit length #=1)      # 172, 1 ContentSequence
              (fffe,e000) na (Item with explicit length #=4)          # 164, 1 Item
                (0040,a010) CS [HAS CONCEPT MOD]                        #  16, 1 RelationshipType
                (0040,a040) CS [CODE]                                   #   4, 1 ValueType
                (0040,a043) SQ (Sequence with explicit length #=1)      #  52, 1 ConceptNameCodeSequence
                  (fffe,e000) na (Item with explicit length #=3)          #  44, 1 Item
                    (0008,0100) SH [121401]                                 #   6, 1 CodeValue
                    (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
                    (0008,0104) LO [Derivation]                             #  10, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a168) SQ (Sequence with explicit length #=1)      #  52, 1 ConceptCodeSequence
                  (fffe,e000) na (Item with explicit length #=3)          #  44, 1 Item
                    (0008,0100) SH [121428]                                 #   6, 1 CodeValue
                    (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
                    (0008,0104) LO [Calculated]                             #  10, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
          (fffe,e000) na (Item with explicit length #=5)          # 380, 1 Item
            (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
            (0040,a040) CS [NUM]                                    #   4, 1 ValueType
            (0040,a043) SQ (Sequence with explicit length #=1)      #  58, 1 ConceptNameCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  50, 1 Item
                (0008,0100) SH [18185-9]                                #   8, 1 CodeValue
                (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
                (0008,0104) LO [Gestational Age]                        #  16, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a300) SQ (Sequence with explicit length #=1)      #  86, 1 MeasuredValueSequence
              (fffe,e000) na (Item with explicit length #=2)          #  78, 1 Item
                (0040,08ea) SQ (Sequence with explicit length #=1)      #  54, 1 MeasurementUnitsCodeSequence
                  (fffe,e000) na (Item with explicit length #=4)          #  46, 1 Item
                    (0008,0100) SH [d]                                      #   2, 1 CodeValue
                    (0008,0102) SH [UCUM]                                   #   4, 1 CodingSchemeDesignator
                    (0008,0103) SH [1.4]                                    #   4, 1 CodingSchemeVersion
                    (0008,0104) LO [days]                                   #   4, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a30a) DS [101]                                    #   4, 1 NumericValue
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a730) SQ (Sequence with explicit length #=1)      # 172, 1 ContentSequence
              (fffe,e000) na (Item with explicit length #=4)          # 164, 1 Item
                (0040,a010) CS [INFERRED FROM]                          #  14, 1 RelationshipType
                (0040,a040) CS [CODE]                                   #   4, 1 ValueType
                (0040,a043) SQ (Sequence with explicit length #=1)      #  50, 1 ConceptNameCodeSequence
                  (fffe,e000) na (Item with explicit length #=3)          #  42, 1 Item
                    (0008,0100) SH [121420]                                 #   6, 1 CodeValue
                    (0008,0102) SH [DCM]                                    #   4, 1 CodingSchemeDesignator
                    (0008,0104) LO [Equation]                               #   8, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
                (0040,a168) SQ (Sequence with explicit length #=1)      #  56, 1 ConceptCodeSequence
                  (fffe,e000) na (Item with explicit length #=3)          #  48, 1 Item
                    (0008,0100) SH [99313-0]                                #   8, 1 CodeValue
                    (0008,0102) SH [GEK]                                    #   4, 1 CodingSchemeDesignator
                    (0008,0104) LO [HC, Hadlock]                            #  12, 1 CodeMeaning
                  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
                (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
          (fffe,e000) na (Item with explicit length #=4)          # 102, 1 Item
            (0040,a010) CS [CONTAINS]                               #   8, 1 RelationshipType
            (0040,a040) CS [DATE]                                   #   4, 1 ValueType
            (0040,a043) SQ (Sequence with explicit length #=1)      #  46, 1 ConceptNameCodeSequence
              (fffe,e000) na (Item with explicit length #=3)          #  38, 1 Item
                (0008,0100) SH [11778-8]                                #   8, 1 CodeValue
                (0008,0102) SH [LN]                                     #   2, 1 CodingSchemeDesignator
                (0008,0104) LO [EDD]                                    #   4, 1 CodeMeaning
              (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
            (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
            (0040,a121) DA [20250902]                               #   8, 1 Date
          (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
        (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
      (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
    (fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
  (fffe,e00d) na (ItemDelimitationItem for re-encoding)   #   0, 0 ItemDelimitationItem
(fffe,e0dd) na (SequenceDelimitationItem for re-encod.) #   0, 0 SequenceDelimitationItem
(6501,0010) LO [KRETZ_M]                                #   8, 1 PrivateCreator
(6501,1001) OB 4f\00\42\00\5f\00\45\00\46\00\57\00\2e\00\09\00\31\00\31\00\34\00... # 2436, 1 Unknown Tag & Data
