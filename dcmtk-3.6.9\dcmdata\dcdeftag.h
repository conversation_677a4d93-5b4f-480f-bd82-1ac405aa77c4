/*
** DO NOT EDIT THIS FILE !!!
** It was generated automatically by:
**
**   User: j<PERSON><PERSON><PERSON>
**   Host: thinkpad2
**   Date: 2024-11-16 10:42:04
**   Prog: /home/<USER>/Source/dcmtk-full/public/dcmdata/libsrc/mkdeftag
**
**   From: ../data/dicom.dic
**         ../data/private.dic
**
*/

#ifndef DCDEFTAG_H
#define DCDEFTAG_H

#include "dcmtk/dcmdata/dctagkey.h"

#define DCM_DICT_DEFTAG_BUILD_DATE "2024-11-16 10:42:04"


/*
** Fixed Tags in ascending (gggg,eeee) order.
** Number of entries: 5206
** Tags with a repeating component (repeating tags) are listed later.
*/
#define DCM_CommandGroupLength                   DcmTagKey(0x0000, 0x0000)
#define DCM_RETIRED_CommandLengthToEnd           DcmTagKey(0x0000, 0x0001)
#define DCM_AffectedSOPClassUID                  DcmTagKey(0x0000, 0x0002)
#define DCM_RequestedSOPClassUID                 DcmTagKey(0x0000, 0x0003)
#define DCM_RETIRED_CommandRecognitionCode       DcmTagKey(0x0000, 0x0010)
#define DCM_CommandField                         DcmTagKey(0x0000, 0x0100)
#define DCM_MessageID                            DcmTagKey(0x0000, 0x0110)
#define DCM_MessageIDBeingRespondedTo            DcmTagKey(0x0000, 0x0120)
#define DCM_RETIRED_Initiator                    DcmTagKey(0x0000, 0x0200)
#define DCM_RETIRED_Receiver                     DcmTagKey(0x0000, 0x0300)
#define DCM_RETIRED_FindLocation                 DcmTagKey(0x0000, 0x0400)
#define DCM_MoveDestination                      DcmTagKey(0x0000, 0x0600)
#define DCM_Priority                             DcmTagKey(0x0000, 0x0700)
#define DCM_CommandDataSetType                   DcmTagKey(0x0000, 0x0800)
#define DCM_RETIRED_NumberOfMatches              DcmTagKey(0x0000, 0x0850)
#define DCM_RETIRED_ResponseSequenceNumber       DcmTagKey(0x0000, 0x0860)
#define DCM_Status                               DcmTagKey(0x0000, 0x0900)
#define DCM_OffendingElement                     DcmTagKey(0x0000, 0x0901)
#define DCM_ErrorComment                         DcmTagKey(0x0000, 0x0902)
#define DCM_ErrorID                              DcmTagKey(0x0000, 0x0903)
#define DCM_AffectedSOPInstanceUID               DcmTagKey(0x0000, 0x1000)
#define DCM_RequestedSOPInstanceUID              DcmTagKey(0x0000, 0x1001)
#define DCM_EventTypeID                          DcmTagKey(0x0000, 0x1002)
#define DCM_AttributeIdentifierList              DcmTagKey(0x0000, 0x1005)
#define DCM_ActionTypeID                         DcmTagKey(0x0000, 0x1008)
#define DCM_NumberOfRemainingSuboperations       DcmTagKey(0x0000, 0x1020)
#define DCM_NumberOfCompletedSuboperations       DcmTagKey(0x0000, 0x1021)
#define DCM_NumberOfFailedSuboperations          DcmTagKey(0x0000, 0x1022)
#define DCM_NumberOfWarningSuboperations         DcmTagKey(0x0000, 0x1023)
#define DCM_MoveOriginatorApplicationEntityTitle DcmTagKey(0x0000, 0x1030)
#define DCM_MoveOriginatorMessageID              DcmTagKey(0x0000, 0x1031)
#define DCM_RETIRED_DialogReceiver               DcmTagKey(0x0000, 0x4000)
#define DCM_RETIRED_TerminalType                 DcmTagKey(0x0000, 0x4010)
#define DCM_RETIRED_MessageSetID                 DcmTagKey(0x0000, 0x5010)
#define DCM_RETIRED_EndMessageID                 DcmTagKey(0x0000, 0x5020)
#define DCM_RETIRED_DisplayFormat                DcmTagKey(0x0000, 0x5110)
#define DCM_RETIRED_PagePositionID               DcmTagKey(0x0000, 0x5120)
#define DCM_RETIRED_TextFormatID                 DcmTagKey(0x0000, 0x5130)
#define DCM_RETIRED_NormalReverse                DcmTagKey(0x0000, 0x5140)
#define DCM_RETIRED_AddGrayScale                 DcmTagKey(0x0000, 0x5150)
#define DCM_RETIRED_Borders                      DcmTagKey(0x0000, 0x5160)
#define DCM_RETIRED_Copies                       DcmTagKey(0x0000, 0x5170)
#define DCM_RETIRED_CommandMagnificationType     DcmTagKey(0x0000, 0x5180)
#define DCM_RETIRED_Erase                        DcmTagKey(0x0000, 0x5190)
#define DCM_RETIRED_Print                        DcmTagKey(0x0000, 0x51a0)
#define DCM_RETIRED_Overlays                     DcmTagKey(0x0000, 0x51b0)
#define DCM_FileMetaInformationGroupLength       DcmTagKey(0x0002, 0x0000)
#define DCM_FileMetaInformationVersion           DcmTagKey(0x0002, 0x0001)
#define DCM_MediaStorageSOPClassUID              DcmTagKey(0x0002, 0x0002)
#define DCM_MediaStorageSOPInstanceUID           DcmTagKey(0x0002, 0x0003)
#define DCM_TransferSyntaxUID                    DcmTagKey(0x0002, 0x0010)
#define DCM_ImplementationClassUID               DcmTagKey(0x0002, 0x0012)
#define DCM_ImplementationVersionName            DcmTagKey(0x0002, 0x0013)
#define DCM_SourceApplicationEntityTitle         DcmTagKey(0x0002, 0x0016)
#define DCM_SendingApplicationEntityTitle        DcmTagKey(0x0002, 0x0017)
#define DCM_ReceivingApplicationEntityTitle      DcmTagKey(0x0002, 0x0018)
#define DCM_SourcePresentationAddress            DcmTagKey(0x0002, 0x0026)
#define DCM_SendingPresentationAddress           DcmTagKey(0x0002, 0x0027)
#define DCM_ReceivingPresentationAddress         DcmTagKey(0x0002, 0x0028)
#define DCM_RTVMetaInformationVersion            DcmTagKey(0x0002, 0x0031)
#define DCM_RTVCommunicationSOPClassUID          DcmTagKey(0x0002, 0x0032)
#define DCM_RTVCommunicationSOPInstanceUID       DcmTagKey(0x0002, 0x0033)
#define DCM_RTVSourceIdentifier                  DcmTagKey(0x0002, 0x0035)
#define DCM_RTVFlowIdentifier                    DcmTagKey(0x0002, 0x0036)
#define DCM_RTVFlowRTPSamplingRate               DcmTagKey(0x0002, 0x0037)
#define DCM_RTVFlowActualFrameDuration           DcmTagKey(0x0002, 0x0038)
#define DCM_PrivateInformationCreatorUID         DcmTagKey(0x0002, 0x0100)
#define DCM_PrivateInformation                   DcmTagKey(0x0002, 0x0102)
#define DCM_FileSetID                            DcmTagKey(0x0004, 0x1130)
#define DCM_FileSetDescriptorFileID              DcmTagKey(0x0004, 0x1141)
#define DCM_SpecificCharacterSetOfFileSetDescriptorFile DcmTagKey(0x0004, 0x1142)
#define DCM_OffsetOfTheFirstDirectoryRecordOfTheRootDirectoryEntity DcmTagKey(0x0004, 0x1200)
#define DCM_OffsetOfTheLastDirectoryRecordOfTheRootDirectoryEntity DcmTagKey(0x0004, 0x1202)
#define DCM_FileSetConsistencyFlag               DcmTagKey(0x0004, 0x1212)
#define DCM_DirectoryRecordSequence              DcmTagKey(0x0004, 0x1220)
#define DCM_OffsetOfTheNextDirectoryRecord       DcmTagKey(0x0004, 0x1400)
#define DCM_RecordInUseFlag                      DcmTagKey(0x0004, 0x1410)
#define DCM_OffsetOfReferencedLowerLevelDirectoryEntity DcmTagKey(0x0004, 0x1420)
#define DCM_DirectoryRecordType                  DcmTagKey(0x0004, 0x1430)
#define DCM_PrivateRecordUID                     DcmTagKey(0x0004, 0x1432)
#define DCM_ReferencedFileID                     DcmTagKey(0x0004, 0x1500)
#define DCM_RETIRED_MRDRDirectoryRecordOffset    DcmTagKey(0x0004, 0x1504)
#define DCM_ReferencedSOPClassUIDInFile          DcmTagKey(0x0004, 0x1510)
#define DCM_ReferencedSOPInstanceUIDInFile       DcmTagKey(0x0004, 0x1511)
#define DCM_ReferencedTransferSyntaxUIDInFile    DcmTagKey(0x0004, 0x1512)
#define DCM_ReferencedRelatedGeneralSOPClassUIDInFile DcmTagKey(0x0004, 0x151a)
#define DCM_RETIRED_NumberOfReferences           DcmTagKey(0x0004, 0x1600)
#define DCM_CurrentFrameFunctionalGroupsSequence DcmTagKey(0x0006, 0x0001)
#define DCM_RETIRED_LengthToEnd                  DcmTagKey(0x0008, 0x0001)
#define DCM_SpecificCharacterSet                 DcmTagKey(0x0008, 0x0005)
#define DCM_LanguageCodeSequence                 DcmTagKey(0x0008, 0x0006)
#define DCM_ImageType                            DcmTagKey(0x0008, 0x0008)
#define DCM_RETIRED_RecognitionCode              DcmTagKey(0x0008, 0x0010)
#define DCM_InstanceCreationDate                 DcmTagKey(0x0008, 0x0012)
#define DCM_InstanceCreationTime                 DcmTagKey(0x0008, 0x0013)
#define DCM_InstanceCreatorUID                   DcmTagKey(0x0008, 0x0014)
#define DCM_InstanceCoercionDateTime             DcmTagKey(0x0008, 0x0015)
#define DCM_SOPClassUID                          DcmTagKey(0x0008, 0x0016)
#define DCM_AcquisitionUID                       DcmTagKey(0x0008, 0x0017)
#define DCM_SOPInstanceUID                       DcmTagKey(0x0008, 0x0018)
#define DCM_PyramidUID                           DcmTagKey(0x0008, 0x0019)
#define DCM_RelatedGeneralSOPClassUID            DcmTagKey(0x0008, 0x001a)
#define DCM_OriginalSpecializedSOPClassUID       DcmTagKey(0x0008, 0x001b)
#define DCM_SyntheticData                        DcmTagKey(0x0008, 0x001c)
#define DCM_StudyDate                            DcmTagKey(0x0008, 0x0020)
#define DCM_SeriesDate                           DcmTagKey(0x0008, 0x0021)
#define DCM_AcquisitionDate                      DcmTagKey(0x0008, 0x0022)
#define DCM_ContentDate                          DcmTagKey(0x0008, 0x0023)
#define DCM_RETIRED_OverlayDate                  DcmTagKey(0x0008, 0x0024)
#define DCM_RETIRED_CurveDate                    DcmTagKey(0x0008, 0x0025)
#define DCM_AcquisitionDateTime                  DcmTagKey(0x0008, 0x002a)
#define DCM_StudyTime                            DcmTagKey(0x0008, 0x0030)
#define DCM_SeriesTime                           DcmTagKey(0x0008, 0x0031)
#define DCM_AcquisitionTime                      DcmTagKey(0x0008, 0x0032)
#define DCM_ContentTime                          DcmTagKey(0x0008, 0x0033)
#define DCM_RETIRED_OverlayTime                  DcmTagKey(0x0008, 0x0034)
#define DCM_RETIRED_CurveTime                    DcmTagKey(0x0008, 0x0035)
#define DCM_RETIRED_DataSetType                  DcmTagKey(0x0008, 0x0040)
#define DCM_RETIRED_DataSetSubtype               DcmTagKey(0x0008, 0x0041)
#define DCM_RETIRED_NuclearMedicineSeriesType    DcmTagKey(0x0008, 0x0042)
#define DCM_AccessionNumber                      DcmTagKey(0x0008, 0x0050)
#define DCM_IssuerOfAccessionNumberSequence      DcmTagKey(0x0008, 0x0051)
#define DCM_QueryRetrieveLevel                   DcmTagKey(0x0008, 0x0052)
#define DCM_QueryRetrieveView                    DcmTagKey(0x0008, 0x0053)
#define DCM_RetrieveAETitle                      DcmTagKey(0x0008, 0x0054)
#define DCM_StationAETitle                       DcmTagKey(0x0008, 0x0055)
#define DCM_InstanceAvailability                 DcmTagKey(0x0008, 0x0056)
#define DCM_FailedSOPInstanceUIDList             DcmTagKey(0x0008, 0x0058)
#define DCM_Modality                             DcmTagKey(0x0008, 0x0060)
#define DCM_ModalitiesInStudy                    DcmTagKey(0x0008, 0x0061)
#define DCM_SOPClassesInStudy                    DcmTagKey(0x0008, 0x0062)
#define DCM_AnatomicRegionsInStudyCodeSequence   DcmTagKey(0x0008, 0x0063)
#define DCM_ConversionType                       DcmTagKey(0x0008, 0x0064)
#define DCM_PresentationIntentType               DcmTagKey(0x0008, 0x0068)
#define DCM_Manufacturer                         DcmTagKey(0x0008, 0x0070)
#define DCM_InstitutionName                      DcmTagKey(0x0008, 0x0080)
#define DCM_InstitutionAddress                   DcmTagKey(0x0008, 0x0081)
#define DCM_InstitutionCodeSequence              DcmTagKey(0x0008, 0x0082)
#define DCM_ReferringPhysicianName               DcmTagKey(0x0008, 0x0090)
#define DCM_ReferringPhysicianAddress            DcmTagKey(0x0008, 0x0092)
#define DCM_ReferringPhysicianTelephoneNumbers   DcmTagKey(0x0008, 0x0094)
#define DCM_ReferringPhysicianIdentificationSequence DcmTagKey(0x0008, 0x0096)
#define DCM_ConsultingPhysicianName              DcmTagKey(0x0008, 0x009c)
#define DCM_ConsultingPhysicianIdentificationSequence DcmTagKey(0x0008, 0x009d)
#define DCM_CodeValue                            DcmTagKey(0x0008, 0x0100)
#define DCM_ExtendedCodeValue                    DcmTagKey(0x0008, 0x0101)
#define DCM_CodingSchemeDesignator               DcmTagKey(0x0008, 0x0102)
#define DCM_CodingSchemeVersion                  DcmTagKey(0x0008, 0x0103)
#define DCM_CodeMeaning                          DcmTagKey(0x0008, 0x0104)
#define DCM_MappingResource                      DcmTagKey(0x0008, 0x0105)
#define DCM_ContextGroupVersion                  DcmTagKey(0x0008, 0x0106)
#define DCM_ContextGroupLocalVersion             DcmTagKey(0x0008, 0x0107)
#define DCM_ExtendedCodeMeaning                  DcmTagKey(0x0008, 0x0108)
#define DCM_CodingSchemeResourcesSequence        DcmTagKey(0x0008, 0x0109)
#define DCM_CodingSchemeURLType                  DcmTagKey(0x0008, 0x010a)
#define DCM_ContextGroupExtensionFlag            DcmTagKey(0x0008, 0x010b)
#define DCM_CodingSchemeUID                      DcmTagKey(0x0008, 0x010c)
#define DCM_ContextGroupExtensionCreatorUID      DcmTagKey(0x0008, 0x010d)
#define DCM_CodingSchemeURL                      DcmTagKey(0x0008, 0x010e)
#define DCM_ContextIdentifier                    DcmTagKey(0x0008, 0x010f)
#define DCM_CodingSchemeIdentificationSequence   DcmTagKey(0x0008, 0x0110)
#define DCM_CodingSchemeRegistry                 DcmTagKey(0x0008, 0x0112)
#define DCM_CodingSchemeExternalID               DcmTagKey(0x0008, 0x0114)
#define DCM_CodingSchemeName                     DcmTagKey(0x0008, 0x0115)
#define DCM_CodingSchemeResponsibleOrganization  DcmTagKey(0x0008, 0x0116)
#define DCM_ContextUID                           DcmTagKey(0x0008, 0x0117)
#define DCM_MappingResourceUID                   DcmTagKey(0x0008, 0x0118)
#define DCM_LongCodeValue                        DcmTagKey(0x0008, 0x0119)
#define DCM_URNCodeValue                         DcmTagKey(0x0008, 0x0120)
#define DCM_EquivalentCodeSequence               DcmTagKey(0x0008, 0x0121)
#define DCM_MappingResourceName                  DcmTagKey(0x0008, 0x0122)
#define DCM_ContextGroupIdentificationSequence   DcmTagKey(0x0008, 0x0123)
#define DCM_MappingResourceIdentificationSequence DcmTagKey(0x0008, 0x0124)
#define DCM_TimezoneOffsetFromUTC                DcmTagKey(0x0008, 0x0201)
#define DCM_ResponsibleGroupCodeSequence         DcmTagKey(0x0008, 0x0220)
#define DCM_EquipmentModality                    DcmTagKey(0x0008, 0x0221)
#define DCM_ManufacturerRelatedModelGroup        DcmTagKey(0x0008, 0x0222)
#define DCM_PrivateDataElementCharacteristicsSequence DcmTagKey(0x0008, 0x0300)
#define DCM_PrivateGroupReference                DcmTagKey(0x0008, 0x0301)
#define DCM_PrivateCreatorReference              DcmTagKey(0x0008, 0x0302)
#define DCM_BlockIdentifyingInformationStatus    DcmTagKey(0x0008, 0x0303)
#define DCM_NonidentifyingPrivateElements        DcmTagKey(0x0008, 0x0304)
#define DCM_DeidentificationActionSequence       DcmTagKey(0x0008, 0x0305)
#define DCM_IdentifyingPrivateElements           DcmTagKey(0x0008, 0x0306)
#define DCM_DeidentificationAction               DcmTagKey(0x0008, 0x0307)
#define DCM_PrivateDataElement                   DcmTagKey(0x0008, 0x0308)
#define DCM_PrivateDataElementValueMultiplicity  DcmTagKey(0x0008, 0x0309)
#define DCM_PrivateDataElementValueRepresentation DcmTagKey(0x0008, 0x030a)
#define DCM_PrivateDataElementNumberOfItems      DcmTagKey(0x0008, 0x030b)
#define DCM_PrivateDataElementName               DcmTagKey(0x0008, 0x030c)
#define DCM_PrivateDataElementKeyword            DcmTagKey(0x0008, 0x030d)
#define DCM_PrivateDataElementDescription        DcmTagKey(0x0008, 0x030e)
#define DCM_PrivateDataElementEncoding           DcmTagKey(0x0008, 0x030f)
#define DCM_PrivateDataElementDefinitionSequence DcmTagKey(0x0008, 0x0310)
#define DCM_ScopeOfInventorySequence             DcmTagKey(0x0008, 0x0400)
#define DCM_InventoryPurpose                     DcmTagKey(0x0008, 0x0401)
#define DCM_InventoryInstanceDescription         DcmTagKey(0x0008, 0x0402)
#define DCM_InventoryLevel                       DcmTagKey(0x0008, 0x0403)
#define DCM_ItemInventoryDateTime                DcmTagKey(0x0008, 0x0404)
#define DCM_RemovedFromOperationalUse            DcmTagKey(0x0008, 0x0405)
#define DCM_ReasonForRemovalCodeSequence         DcmTagKey(0x0008, 0x0406)
#define DCM_StoredInstanceBaseURI                DcmTagKey(0x0008, 0x0407)
#define DCM_FolderAccessURI                      DcmTagKey(0x0008, 0x0408)
#define DCM_FileAccessURI                        DcmTagKey(0x0008, 0x0409)
#define DCM_ContainerFileType                    DcmTagKey(0x0008, 0x040a)
#define DCM_FilenameInContainer                  DcmTagKey(0x0008, 0x040b)
#define DCM_FileOffsetInContainer                DcmTagKey(0x0008, 0x040c)
#define DCM_FileLengthInContainer                DcmTagKey(0x0008, 0x040d)
#define DCM_StoredInstanceTransferSyntaxUID      DcmTagKey(0x0008, 0x040e)
#define DCM_ExtendedMatchingMechanisms           DcmTagKey(0x0008, 0x040f)
#define DCM_RangeMatchingSequence                DcmTagKey(0x0008, 0x0410)
#define DCM_ListOfUIDMatchingSequence            DcmTagKey(0x0008, 0x0411)
#define DCM_EmptyValueMatchingSequence           DcmTagKey(0x0008, 0x0412)
#define DCM_GeneralMatchingSequence              DcmTagKey(0x0008, 0x0413)
#define DCM_RequestedStatusInterval              DcmTagKey(0x0008, 0x0414)
#define DCM_RetainInstances                      DcmTagKey(0x0008, 0x0415)
#define DCM_ExpirationDateTime                   DcmTagKey(0x0008, 0x0416)
#define DCM_TransactionStatus                    DcmTagKey(0x0008, 0x0417)
#define DCM_TransactionStatusComment             DcmTagKey(0x0008, 0x0418)
#define DCM_FileSetAccessSequence                DcmTagKey(0x0008, 0x0419)
#define DCM_FileAccessSequence                   DcmTagKey(0x0008, 0x041a)
#define DCM_RecordKey                            DcmTagKey(0x0008, 0x041b)
#define DCM_PriorRecordKey                       DcmTagKey(0x0008, 0x041c)
#define DCM_MetadataSequence                     DcmTagKey(0x0008, 0x041d)
#define DCM_UpdatedMetadataSequence              DcmTagKey(0x0008, 0x041e)
#define DCM_StudyUpdateDateTime                  DcmTagKey(0x0008, 0x041f)
#define DCM_InventoryAccessEndPointsSequence     DcmTagKey(0x0008, 0x0420)
#define DCM_StudyAccessEndPointsSequence         DcmTagKey(0x0008, 0x0421)
#define DCM_IncorporatedInventoryInstanceSequence DcmTagKey(0x0008, 0x0422)
#define DCM_InventoriedStudiesSequence           DcmTagKey(0x0008, 0x0423)
#define DCM_InventoriedSeriesSequence            DcmTagKey(0x0008, 0x0424)
#define DCM_InventoriedInstancesSequence         DcmTagKey(0x0008, 0x0425)
#define DCM_InventoryCompletionStatus            DcmTagKey(0x0008, 0x0426)
#define DCM_NumberOfStudyRecordsInInstance       DcmTagKey(0x0008, 0x0427)
#define DCM_TotalNumberOfStudyRecords            DcmTagKey(0x0008, 0x0428)
#define DCM_MaximumNumberOfRecords               DcmTagKey(0x0008, 0x0429)
#define DCM_RETIRED_NetworkID                    DcmTagKey(0x0008, 0x1000)
#define DCM_StationName                          DcmTagKey(0x0008, 0x1010)
#define DCM_StudyDescription                     DcmTagKey(0x0008, 0x1030)
#define DCM_ProcedureCodeSequence                DcmTagKey(0x0008, 0x1032)
#define DCM_SeriesDescription                    DcmTagKey(0x0008, 0x103e)
#define DCM_SeriesDescriptionCodeSequence        DcmTagKey(0x0008, 0x103f)
#define DCM_InstitutionalDepartmentName          DcmTagKey(0x0008, 0x1040)
#define DCM_InstitutionalDepartmentTypeCodeSequence DcmTagKey(0x0008, 0x1041)
#define DCM_PhysiciansOfRecord                   DcmTagKey(0x0008, 0x1048)
#define DCM_PhysiciansOfRecordIdentificationSequence DcmTagKey(0x0008, 0x1049)
#define DCM_PerformingPhysicianName              DcmTagKey(0x0008, 0x1050)
#define DCM_PerformingPhysicianIdentificationSequence DcmTagKey(0x0008, 0x1052)
#define DCM_NameOfPhysiciansReadingStudy         DcmTagKey(0x0008, 0x1060)
#define DCM_PhysiciansReadingStudyIdentificationSequence DcmTagKey(0x0008, 0x1062)
#define DCM_OperatorsName                        DcmTagKey(0x0008, 0x1070)
#define DCM_OperatorIdentificationSequence       DcmTagKey(0x0008, 0x1072)
#define DCM_AdmittingDiagnosesDescription        DcmTagKey(0x0008, 0x1080)
#define DCM_AdmittingDiagnosesCodeSequence       DcmTagKey(0x0008, 0x1084)
#define DCM_PyramidDescription                   DcmTagKey(0x0008, 0x1088)
#define DCM_ManufacturerModelName                DcmTagKey(0x0008, 0x1090)
#define DCM_RETIRED_ReferencedResultsSequence    DcmTagKey(0x0008, 0x1100)
#define DCM_ReferencedStudySequence              DcmTagKey(0x0008, 0x1110)
#define DCM_ReferencedPerformedProcedureStepSequence DcmTagKey(0x0008, 0x1111)
#define DCM_ReferencedInstancesBySOPClassSequence DcmTagKey(0x0008, 0x1112)
#define DCM_ReferencedSeriesSequence             DcmTagKey(0x0008, 0x1115)
#define DCM_ReferencedPatientSequence            DcmTagKey(0x0008, 0x1120)
#define DCM_ReferencedVisitSequence              DcmTagKey(0x0008, 0x1125)
#define DCM_RETIRED_ReferencedOverlaySequence    DcmTagKey(0x0008, 0x1130)
#define DCM_ReferencedStereometricInstanceSequence DcmTagKey(0x0008, 0x1134)
#define DCM_ReferencedWaveformSequence           DcmTagKey(0x0008, 0x113a)
#define DCM_ReferencedImageSequence              DcmTagKey(0x0008, 0x1140)
#define DCM_RETIRED_ReferencedCurveSequence      DcmTagKey(0x0008, 0x1145)
#define DCM_ReferencedInstanceSequence           DcmTagKey(0x0008, 0x114a)
#define DCM_ReferencedRealWorldValueMappingInstanceSequence DcmTagKey(0x0008, 0x114b)
#define DCM_ReferencedSegmentationSequence       DcmTagKey(0x0008, 0x114c)
#define DCM_ReferencedSurfaceSegmentationSequence DcmTagKey(0x0008, 0x114d)
#define DCM_ReferencedSOPClassUID                DcmTagKey(0x0008, 0x1150)
#define DCM_ReferencedSOPInstanceUID             DcmTagKey(0x0008, 0x1155)
#define DCM_DefinitionSourceSequence             DcmTagKey(0x0008, 0x1156)
#define DCM_SOPClassesSupported                  DcmTagKey(0x0008, 0x115a)
#define DCM_ReferencedFrameNumber                DcmTagKey(0x0008, 0x1160)
#define DCM_SimpleFrameList                      DcmTagKey(0x0008, 0x1161)
#define DCM_CalculatedFrameList                  DcmTagKey(0x0008, 0x1162)
#define DCM_TimeRange                            DcmTagKey(0x0008, 0x1163)
#define DCM_FrameExtractionSequence              DcmTagKey(0x0008, 0x1164)
#define DCM_MultiFrameSourceSOPInstanceUID       DcmTagKey(0x0008, 0x1167)
#define DCM_RetrieveURL                          DcmTagKey(0x0008, 0x1190)
#define DCM_TransactionUID                       DcmTagKey(0x0008, 0x1195)
#define DCM_WarningReason                        DcmTagKey(0x0008, 0x1196)
#define DCM_FailureReason                        DcmTagKey(0x0008, 0x1197)
#define DCM_FailedSOPSequence                    DcmTagKey(0x0008, 0x1198)
#define DCM_ReferencedSOPSequence                DcmTagKey(0x0008, 0x1199)
#define DCM_OtherFailuresSequence                DcmTagKey(0x0008, 0x119a)
#define DCM_FailedStudySequence                  DcmTagKey(0x0008, 0x119b)
#define DCM_StudiesContainingOtherReferencedInstancesSequence DcmTagKey(0x0008, 0x1200)
#define DCM_RelatedSeriesSequence                DcmTagKey(0x0008, 0x1250)
#define DCM_RETIRED_LossyImageCompressionRetired DcmTagKey(0x0008, 0x2110)
#define DCM_DerivationDescription                DcmTagKey(0x0008, 0x2111)
#define DCM_SourceImageSequence                  DcmTagKey(0x0008, 0x2112)
#define DCM_StageName                            DcmTagKey(0x0008, 0x2120)
#define DCM_StageNumber                          DcmTagKey(0x0008, 0x2122)
#define DCM_NumberOfStages                       DcmTagKey(0x0008, 0x2124)
#define DCM_ViewName                             DcmTagKey(0x0008, 0x2127)
#define DCM_ViewNumber                           DcmTagKey(0x0008, 0x2128)
#define DCM_NumberOfEventTimers                  DcmTagKey(0x0008, 0x2129)
#define DCM_NumberOfViewsInStage                 DcmTagKey(0x0008, 0x212a)
#define DCM_EventElapsedTimes                    DcmTagKey(0x0008, 0x2130)
#define DCM_EventTimerNames                      DcmTagKey(0x0008, 0x2132)
#define DCM_EventTimerSequence                   DcmTagKey(0x0008, 0x2133)
#define DCM_EventTimeOffset                      DcmTagKey(0x0008, 0x2134)
#define DCM_EventCodeSequence                    DcmTagKey(0x0008, 0x2135)
#define DCM_StartTrim                            DcmTagKey(0x0008, 0x2142)
#define DCM_StopTrim                             DcmTagKey(0x0008, 0x2143)
#define DCM_RecommendedDisplayFrameRate          DcmTagKey(0x0008, 0x2144)
#define DCM_RETIRED_TransducerPosition           DcmTagKey(0x0008, 0x2200)
#define DCM_RETIRED_TransducerOrientation        DcmTagKey(0x0008, 0x2204)
#define DCM_RETIRED_AnatomicStructure            DcmTagKey(0x0008, 0x2208)
#define DCM_AnatomicRegionSequence               DcmTagKey(0x0008, 0x2218)
#define DCM_AnatomicRegionModifierSequence       DcmTagKey(0x0008, 0x2220)
#define DCM_PrimaryAnatomicStructureSequence     DcmTagKey(0x0008, 0x2228)
#define DCM_RETIRED_AnatomicStructureSpaceOrRegionSequence DcmTagKey(0x0008, 0x2229)
#define DCM_PrimaryAnatomicStructureModifierSequence DcmTagKey(0x0008, 0x2230)
#define DCM_RETIRED_TransducerPositionSequence   DcmTagKey(0x0008, 0x2240)
#define DCM_RETIRED_TransducerPositionModifierSequence DcmTagKey(0x0008, 0x2242)
#define DCM_RETIRED_TransducerOrientationSequence DcmTagKey(0x0008, 0x2244)
#define DCM_RETIRED_TransducerOrientationModifierSequence DcmTagKey(0x0008, 0x2246)
#define DCM_RETIRED_AnatomicStructureSpaceOrRegionCodeSequenceTrial DcmTagKey(0x0008, 0x2251)
#define DCM_RETIRED_AnatomicPortalOfEntranceCodeSequenceTrial DcmTagKey(0x0008, 0x2253)
#define DCM_RETIRED_AnatomicApproachDirectionCodeSequenceTrial DcmTagKey(0x0008, 0x2255)
#define DCM_RETIRED_AnatomicPerspectiveDescriptionTrial DcmTagKey(0x0008, 0x2256)
#define DCM_RETIRED_AnatomicPerspectiveCodeSequenceTrial DcmTagKey(0x0008, 0x2257)
#define DCM_RETIRED_AnatomicLocationOfExaminingInstrumentDescriptionTrial DcmTagKey(0x0008, 0x2258)
#define DCM_RETIRED_AnatomicLocationOfExaminingInstrumentCodeSequenceTrial DcmTagKey(0x0008, 0x2259)
#define DCM_RETIRED_AnatomicStructureSpaceOrRegionModifierCodeSequenceTrial DcmTagKey(0x0008, 0x225a)
#define DCM_RETIRED_OnAxisBackgroundAnatomicStructureCodeSequenceTrial DcmTagKey(0x0008, 0x225c)
#define DCM_AlternateRepresentationSequence      DcmTagKey(0x0008, 0x3001)
#define DCM_AvailableTransferSyntaxUID           DcmTagKey(0x0008, 0x3002)
#define DCM_IrradiationEventUID                  DcmTagKey(0x0008, 0x3010)
#define DCM_SourceIrradiationEventSequence       DcmTagKey(0x0008, 0x3011)
#define DCM_RadiopharmaceuticalAdministrationEventUID DcmTagKey(0x0008, 0x3012)
#define DCM_RETIRED_IdentifyingComments          DcmTagKey(0x0008, 0x4000)
#define DCM_FrameType                            DcmTagKey(0x0008, 0x9007)
#define DCM_ReferencedImageEvidenceSequence      DcmTagKey(0x0008, 0x9092)
#define DCM_ReferencedRawDataSequence            DcmTagKey(0x0008, 0x9121)
#define DCM_CreatorVersionUID                    DcmTagKey(0x0008, 0x9123)
#define DCM_DerivationImageSequence              DcmTagKey(0x0008, 0x9124)
#define DCM_SourceImageEvidenceSequence          DcmTagKey(0x0008, 0x9154)
#define DCM_PixelPresentation                    DcmTagKey(0x0008, 0x9205)
#define DCM_VolumetricProperties                 DcmTagKey(0x0008, 0x9206)
#define DCM_VolumeBasedCalculationTechnique      DcmTagKey(0x0008, 0x9207)
#define DCM_ComplexImageComponent                DcmTagKey(0x0008, 0x9208)
#define DCM_AcquisitionContrast                  DcmTagKey(0x0008, 0x9209)
#define DCM_DerivationCodeSequence               DcmTagKey(0x0008, 0x9215)
#define DCM_ReferencedPresentationStateSequence  DcmTagKey(0x0008, 0x9237)
#define DCM_ReferencedOtherPlaneSequence         DcmTagKey(0x0008, 0x9410)
#define DCM_FrameDisplaySequence                 DcmTagKey(0x0008, 0x9458)
#define DCM_RecommendedDisplayFrameRateInFloat   DcmTagKey(0x0008, 0x9459)
#define DCM_SkipFrameRangeFlag                   DcmTagKey(0x0008, 0x9460)
#define DCM_PatientName                          DcmTagKey(0x0010, 0x0010)
#define DCM_PatientID                            DcmTagKey(0x0010, 0x0020)
#define DCM_IssuerOfPatientID                    DcmTagKey(0x0010, 0x0021)
#define DCM_TypeOfPatientID                      DcmTagKey(0x0010, 0x0022)
#define DCM_IssuerOfPatientIDQualifiersSequence  DcmTagKey(0x0010, 0x0024)
#define DCM_SourcePatientGroupIdentificationSequence DcmTagKey(0x0010, 0x0026)
#define DCM_GroupOfPatientsIdentificationSequence DcmTagKey(0x0010, 0x0027)
#define DCM_SubjectRelativePositionInImage       DcmTagKey(0x0010, 0x0028)
#define DCM_PatientBirthDate                     DcmTagKey(0x0010, 0x0030)
#define DCM_PatientBirthTime                     DcmTagKey(0x0010, 0x0032)
#define DCM_PatientBirthDateInAlternativeCalendar DcmTagKey(0x0010, 0x0033)
#define DCM_PatientDeathDateInAlternativeCalendar DcmTagKey(0x0010, 0x0034)
#define DCM_PatientAlternativeCalendar           DcmTagKey(0x0010, 0x0035)
#define DCM_PatientSex                           DcmTagKey(0x0010, 0x0040)
#define DCM_PatientInsurancePlanCodeSequence     DcmTagKey(0x0010, 0x0050)
#define DCM_PatientPrimaryLanguageCodeSequence   DcmTagKey(0x0010, 0x0101)
#define DCM_PatientPrimaryLanguageModifierCodeSequence DcmTagKey(0x0010, 0x0102)
#define DCM_QualityControlSubject                DcmTagKey(0x0010, 0x0200)
#define DCM_QualityControlSubjectTypeCodeSequence DcmTagKey(0x0010, 0x0201)
#define DCM_StrainDescription                    DcmTagKey(0x0010, 0x0212)
#define DCM_StrainNomenclature                   DcmTagKey(0x0010, 0x0213)
#define DCM_StrainStockNumber                    DcmTagKey(0x0010, 0x0214)
#define DCM_StrainSourceRegistryCodeSequence     DcmTagKey(0x0010, 0x0215)
#define DCM_StrainStockSequence                  DcmTagKey(0x0010, 0x0216)
#define DCM_StrainSource                         DcmTagKey(0x0010, 0x0217)
#define DCM_StrainAdditionalInformation          DcmTagKey(0x0010, 0x0218)
#define DCM_StrainCodeSequence                   DcmTagKey(0x0010, 0x0219)
#define DCM_GeneticModificationsSequence         DcmTagKey(0x0010, 0x0221)
#define DCM_GeneticModificationsDescription      DcmTagKey(0x0010, 0x0222)
#define DCM_GeneticModificationsNomenclature     DcmTagKey(0x0010, 0x0223)
#define DCM_GeneticModificationsCodeSequence     DcmTagKey(0x0010, 0x0229)
#define DCM_RETIRED_OtherPatientIDs              DcmTagKey(0x0010, 0x1000)
#define DCM_OtherPatientNames                    DcmTagKey(0x0010, 0x1001)
#define DCM_OtherPatientIDsSequence              DcmTagKey(0x0010, 0x1002)
#define DCM_PatientBirthName                     DcmTagKey(0x0010, 0x1005)
#define DCM_PatientAge                           DcmTagKey(0x0010, 0x1010)
#define DCM_PatientSize                          DcmTagKey(0x0010, 0x1020)
#define DCM_PatientSizeCodeSequence              DcmTagKey(0x0010, 0x1021)
#define DCM_PatientBodyMassIndex                 DcmTagKey(0x0010, 0x1022)
#define DCM_MeasuredAPDimension                  DcmTagKey(0x0010, 0x1023)
#define DCM_MeasuredLateralDimension             DcmTagKey(0x0010, 0x1024)
#define DCM_PatientWeight                        DcmTagKey(0x0010, 0x1030)
#define DCM_PatientAddress                       DcmTagKey(0x0010, 0x1040)
#define DCM_RETIRED_InsurancePlanIdentification  DcmTagKey(0x0010, 0x1050)
#define DCM_PatientMotherBirthName               DcmTagKey(0x0010, 0x1060)
#define DCM_MilitaryRank                         DcmTagKey(0x0010, 0x1080)
#define DCM_BranchOfService                      DcmTagKey(0x0010, 0x1081)
#define DCM_RETIRED_MedicalRecordLocator         DcmTagKey(0x0010, 0x1090)
#define DCM_ReferencedPatientPhotoSequence       DcmTagKey(0x0010, 0x1100)
#define DCM_MedicalAlerts                        DcmTagKey(0x0010, 0x2000)
#define DCM_Allergies                            DcmTagKey(0x0010, 0x2110)
#define DCM_CountryOfResidence                   DcmTagKey(0x0010, 0x2150)
#define DCM_RegionOfResidence                    DcmTagKey(0x0010, 0x2152)
#define DCM_PatientTelephoneNumbers              DcmTagKey(0x0010, 0x2154)
#define DCM_PatientTelecomInformation            DcmTagKey(0x0010, 0x2155)
#define DCM_EthnicGroup                          DcmTagKey(0x0010, 0x2160)
#define DCM_EthnicGroupCodeSequence              DcmTagKey(0x0010, 0x2161)
#define DCM_Occupation                           DcmTagKey(0x0010, 0x2180)
#define DCM_SmokingStatus                        DcmTagKey(0x0010, 0x21a0)
#define DCM_AdditionalPatientHistory             DcmTagKey(0x0010, 0x21b0)
#define DCM_PregnancyStatus                      DcmTagKey(0x0010, 0x21c0)
#define DCM_LastMenstrualDate                    DcmTagKey(0x0010, 0x21d0)
#define DCM_PatientReligiousPreference           DcmTagKey(0x0010, 0x21f0)
#define DCM_PatientSpeciesDescription            DcmTagKey(0x0010, 0x2201)
#define DCM_PatientSpeciesCodeSequence           DcmTagKey(0x0010, 0x2202)
#define DCM_PatientSexNeutered                   DcmTagKey(0x0010, 0x2203)
#define DCM_AnatomicalOrientationType            DcmTagKey(0x0010, 0x2210)
#define DCM_PatientBreedDescription              DcmTagKey(0x0010, 0x2292)
#define DCM_PatientBreedCodeSequence             DcmTagKey(0x0010, 0x2293)
#define DCM_BreedRegistrationSequence            DcmTagKey(0x0010, 0x2294)
#define DCM_BreedRegistrationNumber              DcmTagKey(0x0010, 0x2295)
#define DCM_BreedRegistryCodeSequence            DcmTagKey(0x0010, 0x2296)
#define DCM_ResponsiblePerson                    DcmTagKey(0x0010, 0x2297)
#define DCM_ResponsiblePersonRole                DcmTagKey(0x0010, 0x2298)
#define DCM_ResponsibleOrganization              DcmTagKey(0x0010, 0x2299)
#define DCM_PatientComments                      DcmTagKey(0x0010, 0x4000)
#define DCM_ExaminedBodyThickness                DcmTagKey(0x0010, 0x9431)
#define DCM_ClinicalTrialSponsorName             DcmTagKey(0x0012, 0x0010)
#define DCM_ClinicalTrialProtocolID              DcmTagKey(0x0012, 0x0020)
#define DCM_ClinicalTrialProtocolName            DcmTagKey(0x0012, 0x0021)
#define DCM_IssuerOfClinicalTrialProtocolID      DcmTagKey(0x0012, 0x0022)
#define DCM_OtherClinicalTrialProtocolIDsSequence DcmTagKey(0x0012, 0x0023)
#define DCM_ClinicalTrialSiteID                  DcmTagKey(0x0012, 0x0030)
#define DCM_ClinicalTrialSiteName                DcmTagKey(0x0012, 0x0031)
#define DCM_IssuerOfClinicalTrialSiteID          DcmTagKey(0x0012, 0x0032)
#define DCM_ClinicalTrialSubjectID               DcmTagKey(0x0012, 0x0040)
#define DCM_IssuerOfClinicalTrialSubjectID       DcmTagKey(0x0012, 0x0041)
#define DCM_ClinicalTrialSubjectReadingID        DcmTagKey(0x0012, 0x0042)
#define DCM_IssuerOfClinicalTrialSubjectReadingID DcmTagKey(0x0012, 0x0043)
#define DCM_ClinicalTrialTimePointID             DcmTagKey(0x0012, 0x0050)
#define DCM_ClinicalTrialTimePointDescription    DcmTagKey(0x0012, 0x0051)
#define DCM_LongitudinalTemporalOffsetFromEvent  DcmTagKey(0x0012, 0x0052)
#define DCM_LongitudinalTemporalEventType        DcmTagKey(0x0012, 0x0053)
#define DCM_ClinicalTrialTimePointTypeCodeSequence DcmTagKey(0x0012, 0x0054)
#define DCM_IssuerOfClinicalTrialTimePointID     DcmTagKey(0x0012, 0x0055)
#define DCM_ClinicalTrialCoordinatingCenterName  DcmTagKey(0x0012, 0x0060)
#define DCM_PatientIdentityRemoved               DcmTagKey(0x0012, 0x0062)
#define DCM_DeidentificationMethod               DcmTagKey(0x0012, 0x0063)
#define DCM_DeidentificationMethodCodeSequence   DcmTagKey(0x0012, 0x0064)
#define DCM_ClinicalTrialSeriesID                DcmTagKey(0x0012, 0x0071)
#define DCM_ClinicalTrialSeriesDescription       DcmTagKey(0x0012, 0x0072)
#define DCM_IssuerOfClinicalTrialSeriesID        DcmTagKey(0x0012, 0x0073)
#define DCM_ClinicalTrialProtocolEthicsCommitteeName DcmTagKey(0x0012, 0x0081)
#define DCM_ClinicalTrialProtocolEthicsCommitteeApprovalNumber DcmTagKey(0x0012, 0x0082)
#define DCM_ConsentForClinicalTrialUseSequence   DcmTagKey(0x0012, 0x0083)
#define DCM_DistributionType                     DcmTagKey(0x0012, 0x0084)
#define DCM_ConsentForDistributionFlag           DcmTagKey(0x0012, 0x0085)
#define DCM_EthicsCommitteeApprovalEffectivenessStartDate DcmTagKey(0x0012, 0x0086)
#define DCM_EthicsCommitteeApprovalEffectivenessEndDate DcmTagKey(0x0012, 0x0087)
#define DCM_RETIRED_CADFileFormat                DcmTagKey(0x0014, 0x0023)
#define DCM_RETIRED_ComponentReferenceSystem     DcmTagKey(0x0014, 0x0024)
#define DCM_ComponentManufacturingProcedure      DcmTagKey(0x0014, 0x0025)
#define DCM_ComponentManufacturer                DcmTagKey(0x0014, 0x0028)
#define DCM_MaterialThickness                    DcmTagKey(0x0014, 0x0030)
#define DCM_MaterialPipeDiameter                 DcmTagKey(0x0014, 0x0032)
#define DCM_MaterialIsolationDiameter            DcmTagKey(0x0014, 0x0034)
#define DCM_MaterialGrade                        DcmTagKey(0x0014, 0x0042)
#define DCM_MaterialPropertiesDescription        DcmTagKey(0x0014, 0x0044)
#define DCM_RETIRED_MaterialPropertiesFileFormatRetired DcmTagKey(0x0014, 0x0045)
#define DCM_MaterialNotes                        DcmTagKey(0x0014, 0x0046)
#define DCM_ComponentShape                       DcmTagKey(0x0014, 0x0050)
#define DCM_CurvatureType                        DcmTagKey(0x0014, 0x0052)
#define DCM_OuterDiameter                        DcmTagKey(0x0014, 0x0054)
#define DCM_InnerDiameter                        DcmTagKey(0x0014, 0x0056)
#define DCM_ComponentWelderIDs                   DcmTagKey(0x0014, 0x0100)
#define DCM_SecondaryApprovalStatus              DcmTagKey(0x0014, 0x0101)
#define DCM_SecondaryReviewDate                  DcmTagKey(0x0014, 0x0102)
#define DCM_SecondaryReviewTime                  DcmTagKey(0x0014, 0x0103)
#define DCM_SecondaryReviewerName                DcmTagKey(0x0014, 0x0104)
#define DCM_RepairID                             DcmTagKey(0x0014, 0x0105)
#define DCM_MultipleComponentApprovalSequence    DcmTagKey(0x0014, 0x0106)
#define DCM_OtherApprovalStatus                  DcmTagKey(0x0014, 0x0107)
#define DCM_OtherSecondaryApprovalStatus         DcmTagKey(0x0014, 0x0108)
#define DCM_DataElementLabelSequence             DcmTagKey(0x0014, 0x0200)
#define DCM_DataElementLabelItemSequence         DcmTagKey(0x0014, 0x0201)
#define DCM_DataElement                          DcmTagKey(0x0014, 0x0202)
#define DCM_DataElementName                      DcmTagKey(0x0014, 0x0203)
#define DCM_DataElementDescription               DcmTagKey(0x0014, 0x0204)
#define DCM_DataElementConditionality            DcmTagKey(0x0014, 0x0205)
#define DCM_DataElementMinimumCharacters         DcmTagKey(0x0014, 0x0206)
#define DCM_DataElementMaximumCharacters         DcmTagKey(0x0014, 0x0207)
#define DCM_ActualEnvironmentalConditions        DcmTagKey(0x0014, 0x1010)
#define DCM_ExpiryDate                           DcmTagKey(0x0014, 0x1020)
#define DCM_EnvironmentalConditions              DcmTagKey(0x0014, 0x1040)
#define DCM_EvaluatorSequence                    DcmTagKey(0x0014, 0x2002)
#define DCM_EvaluatorNumber                      DcmTagKey(0x0014, 0x2004)
#define DCM_EvaluatorName                        DcmTagKey(0x0014, 0x2006)
#define DCM_EvaluationAttempt                    DcmTagKey(0x0014, 0x2008)
#define DCM_IndicationSequence                   DcmTagKey(0x0014, 0x2012)
#define DCM_IndicationNumber                     DcmTagKey(0x0014, 0x2014)
#define DCM_IndicationLabel                      DcmTagKey(0x0014, 0x2016)
#define DCM_IndicationDescription                DcmTagKey(0x0014, 0x2018)
#define DCM_IndicationType                       DcmTagKey(0x0014, 0x201a)
#define DCM_IndicationDisposition                DcmTagKey(0x0014, 0x201c)
#define DCM_IndicationROISequence                DcmTagKey(0x0014, 0x201e)
#define DCM_IndicationPhysicalPropertySequence   DcmTagKey(0x0014, 0x2030)
#define DCM_PropertyLabel                        DcmTagKey(0x0014, 0x2032)
#define DCM_CoordinateSystemNumberOfAxes         DcmTagKey(0x0014, 0x2202)
#define DCM_CoordinateSystemAxesSequence         DcmTagKey(0x0014, 0x2204)
#define DCM_CoordinateSystemAxisDescription      DcmTagKey(0x0014, 0x2206)
#define DCM_CoordinateSystemDataSetMapping       DcmTagKey(0x0014, 0x2208)
#define DCM_CoordinateSystemAxisNumber           DcmTagKey(0x0014, 0x220a)
#define DCM_CoordinateSystemAxisType             DcmTagKey(0x0014, 0x220c)
#define DCM_CoordinateSystemAxisUnits            DcmTagKey(0x0014, 0x220e)
#define DCM_CoordinateSystemAxisValues           DcmTagKey(0x0014, 0x2210)
#define DCM_CoordinateSystemTransformSequence    DcmTagKey(0x0014, 0x2220)
#define DCM_TransformDescription                 DcmTagKey(0x0014, 0x2222)
#define DCM_TransformNumberOfAxes                DcmTagKey(0x0014, 0x2224)
#define DCM_TransformOrderOfAxes                 DcmTagKey(0x0014, 0x2226)
#define DCM_TransformedAxisUnits                 DcmTagKey(0x0014, 0x2228)
#define DCM_CoordinateSystemTransformRotationAndScaleMatrix DcmTagKey(0x0014, 0x222a)
#define DCM_CoordinateSystemTransformTranslationMatrix DcmTagKey(0x0014, 0x222c)
#define DCM_InternalDetectorFrameTime            DcmTagKey(0x0014, 0x3011)
#define DCM_NumberOfFramesIntegrated             DcmTagKey(0x0014, 0x3012)
#define DCM_DetectorTemperatureSequence          DcmTagKey(0x0014, 0x3020)
#define DCM_SensorName                           DcmTagKey(0x0014, 0x3022)
#define DCM_HorizontalOffsetOfSensor             DcmTagKey(0x0014, 0x3024)
#define DCM_VerticalOffsetOfSensor               DcmTagKey(0x0014, 0x3026)
#define DCM_SensorTemperature                    DcmTagKey(0x0014, 0x3028)
#define DCM_DarkCurrentSequence                  DcmTagKey(0x0014, 0x3040)
#define DCM_DarkCurrentCounts                    DcmTagKey(0x0014, 0x3050)
#define DCM_GainCorrectionReferenceSequence      DcmTagKey(0x0014, 0x3060)
#define DCM_AirCounts                            DcmTagKey(0x0014, 0x3070)
#define DCM_KVUsedInGainCalibration              DcmTagKey(0x0014, 0x3071)
#define DCM_MAUsedInGainCalibration              DcmTagKey(0x0014, 0x3072)
#define DCM_NumberOfFramesUsedForIntegration     DcmTagKey(0x0014, 0x3073)
#define DCM_FilterMaterialUsedInGainCalibration  DcmTagKey(0x0014, 0x3074)
#define DCM_FilterThicknessUsedInGainCalibration DcmTagKey(0x0014, 0x3075)
#define DCM_DateOfGainCalibration                DcmTagKey(0x0014, 0x3076)
#define DCM_TimeOfGainCalibration                DcmTagKey(0x0014, 0x3077)
#define DCM_BadPixelImage                        DcmTagKey(0x0014, 0x3080)
#define DCM_CalibrationNotes                     DcmTagKey(0x0014, 0x3099)
#define DCM_LinearityCorrectionTechnique         DcmTagKey(0x0014, 0x3100)
#define DCM_BeamHardeningCorrectionTechnique     DcmTagKey(0x0014, 0x3101)
#define DCM_PulserEquipmentSequence              DcmTagKey(0x0014, 0x4002)
#define DCM_PulserType                           DcmTagKey(0x0014, 0x4004)
#define DCM_PulserNotes                          DcmTagKey(0x0014, 0x4006)
#define DCM_ReceiverEquipmentSequence            DcmTagKey(0x0014, 0x4008)
#define DCM_AmplifierType                        DcmTagKey(0x0014, 0x400a)
#define DCM_ReceiverNotes                        DcmTagKey(0x0014, 0x400c)
#define DCM_PreAmplifierEquipmentSequence        DcmTagKey(0x0014, 0x400e)
#define DCM_PreAmplifierNotes                    DcmTagKey(0x0014, 0x400f)
#define DCM_TransmitTransducerSequence           DcmTagKey(0x0014, 0x4010)
#define DCM_ReceiveTransducerSequence            DcmTagKey(0x0014, 0x4011)
#define DCM_NumberOfElements                     DcmTagKey(0x0014, 0x4012)
#define DCM_ElementShape                         DcmTagKey(0x0014, 0x4013)
#define DCM_ElementDimensionA                    DcmTagKey(0x0014, 0x4014)
#define DCM_ElementDimensionB                    DcmTagKey(0x0014, 0x4015)
#define DCM_ElementPitchA                        DcmTagKey(0x0014, 0x4016)
#define DCM_MeasuredBeamDimensionA               DcmTagKey(0x0014, 0x4017)
#define DCM_MeasuredBeamDimensionB               DcmTagKey(0x0014, 0x4018)
#define DCM_LocationOfMeasuredBeamDiameter       DcmTagKey(0x0014, 0x4019)
#define DCM_NominalFrequency                     DcmTagKey(0x0014, 0x401a)
#define DCM_MeasuredCenterFrequency              DcmTagKey(0x0014, 0x401b)
#define DCM_MeasuredBandwidth                    DcmTagKey(0x0014, 0x401c)
#define DCM_ElementPitchB                        DcmTagKey(0x0014, 0x401d)
#define DCM_PulserSettingsSequence               DcmTagKey(0x0014, 0x4020)
#define DCM_PulseWidth                           DcmTagKey(0x0014, 0x4022)
#define DCM_ExcitationFrequency                  DcmTagKey(0x0014, 0x4024)
#define DCM_ModulationType                       DcmTagKey(0x0014, 0x4026)
#define DCM_Damping                              DcmTagKey(0x0014, 0x4028)
#define DCM_ReceiverSettingsSequence             DcmTagKey(0x0014, 0x4030)
#define DCM_AcquiredSoundpathLength              DcmTagKey(0x0014, 0x4031)
#define DCM_AcquisitionCompressionType           DcmTagKey(0x0014, 0x4032)
#define DCM_AcquisitionSampleSize                DcmTagKey(0x0014, 0x4033)
#define DCM_RectifierSmoothing                   DcmTagKey(0x0014, 0x4034)
#define DCM_DACSequence                          DcmTagKey(0x0014, 0x4035)
#define DCM_DACType                              DcmTagKey(0x0014, 0x4036)
#define DCM_DACGainPoints                        DcmTagKey(0x0014, 0x4038)
#define DCM_DACTimePoints                        DcmTagKey(0x0014, 0x403a)
#define DCM_DACAmplitude                         DcmTagKey(0x0014, 0x403c)
#define DCM_PreAmplifierSettingsSequence         DcmTagKey(0x0014, 0x4040)
#define DCM_TransmitTransducerSettingsSequence   DcmTagKey(0x0014, 0x4050)
#define DCM_ReceiveTransducerSettingsSequence    DcmTagKey(0x0014, 0x4051)
#define DCM_IncidentAngle                        DcmTagKey(0x0014, 0x4052)
#define DCM_CouplingTechnique                    DcmTagKey(0x0014, 0x4054)
#define DCM_CouplingMedium                       DcmTagKey(0x0014, 0x4056)
#define DCM_CouplingVelocity                     DcmTagKey(0x0014, 0x4057)
#define DCM_ProbeCenterLocationX                 DcmTagKey(0x0014, 0x4058)
#define DCM_ProbeCenterLocationZ                 DcmTagKey(0x0014, 0x4059)
#define DCM_SoundPathLength                      DcmTagKey(0x0014, 0x405a)
#define DCM_DelayLawIdentifier                   DcmTagKey(0x0014, 0x405c)
#define DCM_GateSettingsSequence                 DcmTagKey(0x0014, 0x4060)
#define DCM_GateThreshold                        DcmTagKey(0x0014, 0x4062)
#define DCM_VelocityOfSound                      DcmTagKey(0x0014, 0x4064)
#define DCM_CalibrationSettingsSequence          DcmTagKey(0x0014, 0x4070)
#define DCM_CalibrationProcedure                 DcmTagKey(0x0014, 0x4072)
#define DCM_ProcedureVersion                     DcmTagKey(0x0014, 0x4074)
#define DCM_ProcedureCreationDate                DcmTagKey(0x0014, 0x4076)
#define DCM_ProcedureExpirationDate              DcmTagKey(0x0014, 0x4078)
#define DCM_ProcedureLastModifiedDate            DcmTagKey(0x0014, 0x407a)
#define DCM_CalibrationTime                      DcmTagKey(0x0014, 0x407c)
#define DCM_CalibrationDate                      DcmTagKey(0x0014, 0x407e)
#define DCM_ProbeDriveEquipmentSequence          DcmTagKey(0x0014, 0x4080)
#define DCM_DriveType                            DcmTagKey(0x0014, 0x4081)
#define DCM_ProbeDriveNotes                      DcmTagKey(0x0014, 0x4082)
#define DCM_DriveProbeSequence                   DcmTagKey(0x0014, 0x4083)
#define DCM_ProbeInductance                      DcmTagKey(0x0014, 0x4084)
#define DCM_ProbeResistance                      DcmTagKey(0x0014, 0x4085)
#define DCM_ReceiveProbeSequence                 DcmTagKey(0x0014, 0x4086)
#define DCM_ProbeDriveSettingsSequence           DcmTagKey(0x0014, 0x4087)
#define DCM_BridgeResistors                      DcmTagKey(0x0014, 0x4088)
#define DCM_ProbeOrientationAngle                DcmTagKey(0x0014, 0x4089)
#define DCM_UserSelectedGainY                    DcmTagKey(0x0014, 0x408b)
#define DCM_UserSelectedPhase                    DcmTagKey(0x0014, 0x408c)
#define DCM_UserSelectedOffsetX                  DcmTagKey(0x0014, 0x408d)
#define DCM_UserSelectedOffsetY                  DcmTagKey(0x0014, 0x408e)
#define DCM_ChannelSettingsSequence              DcmTagKey(0x0014, 0x4091)
#define DCM_ChannelThreshold                     DcmTagKey(0x0014, 0x4092)
#define DCM_ScannerSettingsSequence              DcmTagKey(0x0014, 0x409a)
#define DCM_ScanProcedure                        DcmTagKey(0x0014, 0x409b)
#define DCM_TranslationRateX                     DcmTagKey(0x0014, 0x409c)
#define DCM_TranslationRateY                     DcmTagKey(0x0014, 0x409d)
#define DCM_ChannelOverlap                       DcmTagKey(0x0014, 0x409f)
#define DCM_ImageQualityIndicatorType            DcmTagKey(0x0014, 0x40a0)
#define DCM_ImageQualityIndicatorMaterial        DcmTagKey(0x0014, 0x40a1)
#define DCM_ImageQualityIndicatorSize            DcmTagKey(0x0014, 0x40a2)
#define DCM_LINACEnergy                          DcmTagKey(0x0014, 0x5002)
#define DCM_LINACOutput                          DcmTagKey(0x0014, 0x5004)
#define DCM_ActiveAperture                       DcmTagKey(0x0014, 0x5100)
#define DCM_TotalAperture                        DcmTagKey(0x0014, 0x5101)
#define DCM_ApertureElevation                    DcmTagKey(0x0014, 0x5102)
#define DCM_MainLobeAngle                        DcmTagKey(0x0014, 0x5103)
#define DCM_MainRoofAngle                        DcmTagKey(0x0014, 0x5104)
#define DCM_ConnectorType                        DcmTagKey(0x0014, 0x5105)
#define DCM_WedgeModelNumber                     DcmTagKey(0x0014, 0x5106)
#define DCM_WedgeAngleFloat                      DcmTagKey(0x0014, 0x5107)
#define DCM_WedgeRoofAngle                       DcmTagKey(0x0014, 0x5108)
#define DCM_WedgeElement1Position                DcmTagKey(0x0014, 0x5109)
#define DCM_WedgeMaterialVelocity                DcmTagKey(0x0014, 0x510a)
#define DCM_WedgeMaterial                        DcmTagKey(0x0014, 0x510b)
#define DCM_WedgeOffsetZ                         DcmTagKey(0x0014, 0x510c)
#define DCM_WedgeOriginOffsetX                   DcmTagKey(0x0014, 0x510d)
#define DCM_WedgeTimeDelay                       DcmTagKey(0x0014, 0x510e)
#define DCM_WedgeName                            DcmTagKey(0x0014, 0x510f)
#define DCM_WedgeManufacturerName                DcmTagKey(0x0014, 0x5110)
#define DCM_WedgeDescription                     DcmTagKey(0x0014, 0x5111)
#define DCM_NominalBeamAngle                     DcmTagKey(0x0014, 0x5112)
#define DCM_WedgeOffsetX                         DcmTagKey(0x0014, 0x5113)
#define DCM_WedgeOffsetY                         DcmTagKey(0x0014, 0x5114)
#define DCM_WedgeTotalLength                     DcmTagKey(0x0014, 0x5115)
#define DCM_WedgeInContactLength                 DcmTagKey(0x0014, 0x5116)
#define DCM_WedgeFrontGap                        DcmTagKey(0x0014, 0x5117)
#define DCM_WedgeTotalHeight                     DcmTagKey(0x0014, 0x5118)
#define DCM_WedgeFrontHeight                     DcmTagKey(0x0014, 0x5119)
#define DCM_WedgeRearHeight                      DcmTagKey(0x0014, 0x511a)
#define DCM_WedgeTotalWidth                      DcmTagKey(0x0014, 0x511b)
#define DCM_WedgeInContactWidth                  DcmTagKey(0x0014, 0x511c)
#define DCM_WedgeChamferHeight                   DcmTagKey(0x0014, 0x511d)
#define DCM_WedgeCurve                           DcmTagKey(0x0014, 0x511e)
#define DCM_RadiusAlongWedge                     DcmTagKey(0x0014, 0x511f)
#define DCM_ThermalCameraSettingsSequence        DcmTagKey(0x0014, 0x6001)
#define DCM_AcquisitionFrameRate                 DcmTagKey(0x0014, 0x6002)
#define DCM_IntegrationTime                      DcmTagKey(0x0014, 0x6003)
#define DCM_NumberOfCalibrationFrames            DcmTagKey(0x0014, 0x6004)
#define DCM_NumberOfRowsInFullAcquisitionImage   DcmTagKey(0x0014, 0x6005)
#define DCM_NumberOfColumnsInFullAcquisitionImage DcmTagKey(0x0014, 0x6006)
#define DCM_ThermalSourceSettingsSequence        DcmTagKey(0x0014, 0x6007)
#define DCM_SourceHorizontalPitch                DcmTagKey(0x0014, 0x6008)
#define DCM_SourceVerticalPitch                  DcmTagKey(0x0014, 0x6009)
#define DCM_SourceHorizontalScanSpeed            DcmTagKey(0x0014, 0x600a)
#define DCM_ThermalSourceModulationFrequency     DcmTagKey(0x0014, 0x600b)
#define DCM_InductionSourceSettingSequence       DcmTagKey(0x0014, 0x600c)
#define DCM_CoilFrequency                        DcmTagKey(0x0014, 0x600d)
#define DCM_CurrentAmplitudeAcrossCoil           DcmTagKey(0x0014, 0x600e)
#define DCM_FlashSourceSettingSequence           DcmTagKey(0x0014, 0x600f)
#define DCM_FlashDuration                        DcmTagKey(0x0014, 0x6010)
#define DCM_FlashFrameNumber                     DcmTagKey(0x0014, 0x6011)
#define DCM_LaserSourceSettingSequence           DcmTagKey(0x0014, 0x6012)
#define DCM_HorizontalLaserSpotDimension         DcmTagKey(0x0014, 0x6013)
#define DCM_VerticalLaserSpotDimension           DcmTagKey(0x0014, 0x6014)
#define DCM_LaserWavelength                      DcmTagKey(0x0014, 0x6015)
#define DCM_LaserPower                           DcmTagKey(0x0014, 0x6016)
#define DCM_ForcedGasSettingSequence             DcmTagKey(0x0014, 0x6017)
#define DCM_VibrationSourceSettingSequence       DcmTagKey(0x0014, 0x6018)
#define DCM_VibrationExcitationFrequency         DcmTagKey(0x0014, 0x6019)
#define DCM_VibrationExcitationVoltage           DcmTagKey(0x0014, 0x601a)
#define DCM_ThermographyDataCaptureMethod        DcmTagKey(0x0014, 0x601b)
#define DCM_ThermalTechnique                     DcmTagKey(0x0014, 0x601c)
#define DCM_ThermalCameraCoreSequence            DcmTagKey(0x0014, 0x601d)
#define DCM_DetectorWavelengthRange              DcmTagKey(0x0014, 0x601e)
#define DCM_ThermalCameraCalibrationType         DcmTagKey(0x0014, 0x601f)
#define DCM_AcquisitionImageCounter              DcmTagKey(0x0014, 0x6020)
#define DCM_FrontPanelTemperature                DcmTagKey(0x0014, 0x6021)
#define DCM_AirGapTemperature                    DcmTagKey(0x0014, 0x6022)
#define DCM_VerticalPixelSize                    DcmTagKey(0x0014, 0x6023)
#define DCM_HorizontalPixelSize                  DcmTagKey(0x0014, 0x6024)
#define DCM_DataStreamingProtocol                DcmTagKey(0x0014, 0x6025)
#define DCM_LensSequence                         DcmTagKey(0x0014, 0x6026)
#define DCM_FieldOfView                          DcmTagKey(0x0014, 0x6027)
#define DCM_LensFilterManufacturer               DcmTagKey(0x0014, 0x6028)
#define DCM_CutoffFilterType                     DcmTagKey(0x0014, 0x6029)
#define DCM_LensFilterCutOffWavelength           DcmTagKey(0x0014, 0x602a)
#define DCM_ThermalSourceSequence                DcmTagKey(0x0014, 0x602b)
#define DCM_ThermalSourceMotionState             DcmTagKey(0x0014, 0x602c)
#define DCM_ThermalSourceMotionType              DcmTagKey(0x0014, 0x602d)
#define DCM_InductionHeatingSequence             DcmTagKey(0x0014, 0x602e)
#define DCM_CoilConfigurationID                  DcmTagKey(0x0014, 0x602f)
#define DCM_NumberOfTurnsInCoil                  DcmTagKey(0x0014, 0x6030)
#define DCM_ShapeOfIndividualTurn                DcmTagKey(0x0014, 0x6031)
#define DCM_SizeOfIndividualTurn                 DcmTagKey(0x0014, 0x6032)
#define DCM_DistanceBetweenTurns                 DcmTagKey(0x0014, 0x6033)
#define DCM_FlashHeatingSequence                 DcmTagKey(0x0014, 0x6034)
#define DCM_NumberOfLamps                        DcmTagKey(0x0014, 0x6035)
#define DCM_FlashSynchronizationProtocol         DcmTagKey(0x0014, 0x6036)
#define DCM_FlashModificationStatus              DcmTagKey(0x0014, 0x6037)
#define DCM_LaserHeatingSequence                 DcmTagKey(0x0014, 0x6038)
#define DCM_LaserManufacturer                    DcmTagKey(0x0014, 0x6039)
#define DCM_LaserModelNumber                     DcmTagKey(0x0014, 0x603a)
#define DCM_LaserTypeDescription                 DcmTagKey(0x0014, 0x603b)
#define DCM_ForcedGasHeatingSequence             DcmTagKey(0x0014, 0x603c)
#define DCM_GasUsedForHeatingCoolingPart         DcmTagKey(0x0014, 0x603d)
#define DCM_VibrationSonicHeatingSequence        DcmTagKey(0x0014, 0x603e)
#define DCM_ProbeManufacturer                    DcmTagKey(0x0014, 0x603f)
#define DCM_ProbeModelNumber                     DcmTagKey(0x0014, 0x6040)
#define DCM_ApertureSize                         DcmTagKey(0x0014, 0x6041)
#define DCM_ProbeResonantFrequency               DcmTagKey(0x0014, 0x6042)
#define DCM_HeatSourceDescription                DcmTagKey(0x0014, 0x6043)
#define DCM_SurfacePreparationWithOpticalCoating DcmTagKey(0x0014, 0x6044)
#define DCM_OpticalCoatingType                   DcmTagKey(0x0014, 0x6045)
#define DCM_ThermalConductivityOfExposedSurface  DcmTagKey(0x0014, 0x6046)
#define DCM_MaterialDensity                      DcmTagKey(0x0014, 0x6047)
#define DCM_SpecificHeatOfInspectionSurface      DcmTagKey(0x0014, 0x6048)
#define DCM_EmissivityOfInspectionSurface        DcmTagKey(0x0014, 0x6049)
#define DCM_ElectromagneticClassificationOfInspectionSurface DcmTagKey(0x0014, 0x604a)
#define DCM_MovingWindowSize                     DcmTagKey(0x0014, 0x604c)
#define DCM_MovingWindowType                     DcmTagKey(0x0014, 0x604d)
#define DCM_MovingWindowWeights                  DcmTagKey(0x0014, 0x604e)
#define DCM_MovingWindowPitch                    DcmTagKey(0x0014, 0x604f)
#define DCM_MovingWindowPaddingScheme            DcmTagKey(0x0014, 0x6050)
#define DCM_MovingWindowPaddingLength            DcmTagKey(0x0014, 0x6051)
#define DCM_SpatialFilteringParametersSequence   DcmTagKey(0x0014, 0x6052)
#define DCM_SpatialFilteringScheme               DcmTagKey(0x0014, 0x6053)
#define DCM_HorizontalMovingWindowSize           DcmTagKey(0x0014, 0x6056)
#define DCM_VerticalMovingWindowSize             DcmTagKey(0x0014, 0x6057)
#define DCM_PolynomialFittingSequence            DcmTagKey(0x0014, 0x6059)
#define DCM_FittingDataType                      DcmTagKey(0x0014, 0x605a)
#define DCM_OperationOnTimeAxisBeforeFitting     DcmTagKey(0x0014, 0x605b)
#define DCM_OperationOnPixelIntensityBeforeFitting DcmTagKey(0x0014, 0x605c)
#define DCM_OrderOfPolynomial                    DcmTagKey(0x0014, 0x605d)
#define DCM_IndependentVariableForPolynomialFit  DcmTagKey(0x0014, 0x605e)
#define DCM_PolynomialCoefficients               DcmTagKey(0x0014, 0x605f)
#define DCM_ThermographyPixelDataUnit            DcmTagKey(0x0014, 0x6060)
#define DCM_WhitePoint                           DcmTagKey(0x0016, 0x0001)
#define DCM_PrimaryChromaticities                DcmTagKey(0x0016, 0x0002)
#define DCM_BatteryLevel                         DcmTagKey(0x0016, 0x0003)
#define DCM_ExposureTimeInSeconds                DcmTagKey(0x0016, 0x0004)
#define DCM_FNumber                              DcmTagKey(0x0016, 0x0005)
#define DCM_OECFRows                             DcmTagKey(0x0016, 0x0006)
#define DCM_OECFColumns                          DcmTagKey(0x0016, 0x0007)
#define DCM_OECFColumnNames                      DcmTagKey(0x0016, 0x0008)
#define DCM_OECFValues                           DcmTagKey(0x0016, 0x0009)
#define DCM_SpatialFrequencyResponseRows         DcmTagKey(0x0016, 0x000a)
#define DCM_SpatialFrequencyResponseColumns      DcmTagKey(0x0016, 0x000b)
#define DCM_SpatialFrequencyResponseColumnNames  DcmTagKey(0x0016, 0x000c)
#define DCM_SpatialFrequencyResponseValues       DcmTagKey(0x0016, 0x000d)
#define DCM_ColorFilterArrayPatternRows          DcmTagKey(0x0016, 0x000e)
#define DCM_ColorFilterArrayPatternColumns       DcmTagKey(0x0016, 0x000f)
#define DCM_ColorFilterArrayPatternValues        DcmTagKey(0x0016, 0x0010)
#define DCM_FlashFiringStatus                    DcmTagKey(0x0016, 0x0011)
#define DCM_FlashReturnStatus                    DcmTagKey(0x0016, 0x0012)
#define DCM_FlashMode                            DcmTagKey(0x0016, 0x0013)
#define DCM_FlashFunctionPresent                 DcmTagKey(0x0016, 0x0014)
#define DCM_FlashRedEyeMode                      DcmTagKey(0x0016, 0x0015)
#define DCM_ExposureProgram                      DcmTagKey(0x0016, 0x0016)
#define DCM_SpectralSensitivity                  DcmTagKey(0x0016, 0x0017)
#define DCM_PhotographicSensitivity              DcmTagKey(0x0016, 0x0018)
#define DCM_SelfTimerMode                        DcmTagKey(0x0016, 0x0019)
#define DCM_SensitivityType                      DcmTagKey(0x0016, 0x001a)
#define DCM_StandardOutputSensitivity            DcmTagKey(0x0016, 0x001b)
#define DCM_RecommendedExposureIndex             DcmTagKey(0x0016, 0x001c)
#define DCM_ISOSpeed                             DcmTagKey(0x0016, 0x001d)
#define DCM_ISOSpeedLatitudeyyy                  DcmTagKey(0x0016, 0x001e)
#define DCM_ISOSpeedLatitudezzz                  DcmTagKey(0x0016, 0x001f)
#define DCM_EXIFVersion                          DcmTagKey(0x0016, 0x0020)
#define DCM_ShutterSpeedValue                    DcmTagKey(0x0016, 0x0021)
#define DCM_ApertureValue                        DcmTagKey(0x0016, 0x0022)
#define DCM_BrightnessValue                      DcmTagKey(0x0016, 0x0023)
#define DCM_ExposureBiasValue                    DcmTagKey(0x0016, 0x0024)
#define DCM_MaxApertureValue                     DcmTagKey(0x0016, 0x0025)
#define DCM_SubjectDistance                      DcmTagKey(0x0016, 0x0026)
#define DCM_MeteringMode                         DcmTagKey(0x0016, 0x0027)
#define DCM_LightSource                          DcmTagKey(0x0016, 0x0028)
#define DCM_FocalLength                          DcmTagKey(0x0016, 0x0029)
#define DCM_SubjectArea                          DcmTagKey(0x0016, 0x002a)
#define DCM_MakerNote                            DcmTagKey(0x0016, 0x002b)
#define DCM_Temperature                          DcmTagKey(0x0016, 0x0030)
#define DCM_Humidity                             DcmTagKey(0x0016, 0x0031)
#define DCM_Pressure                             DcmTagKey(0x0016, 0x0032)
#define DCM_WaterDepth                           DcmTagKey(0x0016, 0x0033)
#define DCM_Acceleration                         DcmTagKey(0x0016, 0x0034)
#define DCM_CameraElevationAngle                 DcmTagKey(0x0016, 0x0035)
#define DCM_FlashEnergy                          DcmTagKey(0x0016, 0x0036)
#define DCM_SubjectLocation                      DcmTagKey(0x0016, 0x0037)
#define DCM_PhotographicExposureIndex            DcmTagKey(0x0016, 0x0038)
#define DCM_SensingMethod                        DcmTagKey(0x0016, 0x0039)
#define DCM_FileSource                           DcmTagKey(0x0016, 0x003a)
#define DCM_SceneType                            DcmTagKey(0x0016, 0x003b)
#define DCM_CustomRendered                       DcmTagKey(0x0016, 0x0041)
#define DCM_ExposureMode                         DcmTagKey(0x0016, 0x0042)
#define DCM_WhiteBalance                         DcmTagKey(0x0016, 0x0043)
#define DCM_DigitalZoomRatio                     DcmTagKey(0x0016, 0x0044)
#define DCM_FocalLengthIn35mmFilm                DcmTagKey(0x0016, 0x0045)
#define DCM_SceneCaptureType                     DcmTagKey(0x0016, 0x0046)
#define DCM_GainControl                          DcmTagKey(0x0016, 0x0047)
#define DCM_Contrast                             DcmTagKey(0x0016, 0x0048)
#define DCM_Saturation                           DcmTagKey(0x0016, 0x0049)
#define DCM_Sharpness                            DcmTagKey(0x0016, 0x004a)
#define DCM_DeviceSettingDescription             DcmTagKey(0x0016, 0x004b)
#define DCM_SubjectDistanceRange                 DcmTagKey(0x0016, 0x004c)
#define DCM_CameraOwnerName                      DcmTagKey(0x0016, 0x004d)
#define DCM_LensSpecification                    DcmTagKey(0x0016, 0x004e)
#define DCM_LensMake                             DcmTagKey(0x0016, 0x004f)
#define DCM_LensModel                            DcmTagKey(0x0016, 0x0050)
#define DCM_LensSerialNumber                     DcmTagKey(0x0016, 0x0051)
#define DCM_InteroperabilityIndex                DcmTagKey(0x0016, 0x0061)
#define DCM_InteroperabilityVersion              DcmTagKey(0x0016, 0x0062)
#define DCM_GPSVersionID                         DcmTagKey(0x0016, 0x0070)
#define DCM_GPSLatitudeRef                       DcmTagKey(0x0016, 0x0071)
#define DCM_GPSLatitude                          DcmTagKey(0x0016, 0x0072)
#define DCM_GPSLongitudeRef                      DcmTagKey(0x0016, 0x0073)
#define DCM_GPSLongitude                         DcmTagKey(0x0016, 0x0074)
#define DCM_GPSAltitudeRef                       DcmTagKey(0x0016, 0x0075)
#define DCM_GPSAltitude                          DcmTagKey(0x0016, 0x0076)
#define DCM_GPSTimeStamp                         DcmTagKey(0x0016, 0x0077)
#define DCM_GPSSatellites                        DcmTagKey(0x0016, 0x0078)
#define DCM_GPSStatus                            DcmTagKey(0x0016, 0x0079)
#define DCM_GPSMeasureMode                       DcmTagKey(0x0016, 0x007a)
#define DCM_GPSDOP                               DcmTagKey(0x0016, 0x007b)
#define DCM_GPSSpeedRef                          DcmTagKey(0x0016, 0x007c)
#define DCM_GPSSpeed                             DcmTagKey(0x0016, 0x007d)
#define DCM_GPSTrackRef                          DcmTagKey(0x0016, 0x007e)
#define DCM_GPSTrack                             DcmTagKey(0x0016, 0x007f)
#define DCM_GPSImgDirectionRef                   DcmTagKey(0x0016, 0x0080)
#define DCM_GPSImgDirection                      DcmTagKey(0x0016, 0x0081)
#define DCM_GPSMapDatum                          DcmTagKey(0x0016, 0x0082)
#define DCM_GPSDestLatitudeRef                   DcmTagKey(0x0016, 0x0083)
#define DCM_GPSDestLatitude                      DcmTagKey(0x0016, 0x0084)
#define DCM_GPSDestLongitudeRef                  DcmTagKey(0x0016, 0x0085)
#define DCM_GPSDestLongitude                     DcmTagKey(0x0016, 0x0086)
#define DCM_GPSDestBearingRef                    DcmTagKey(0x0016, 0x0087)
#define DCM_GPSDestBearing                       DcmTagKey(0x0016, 0x0088)
#define DCM_GPSDestDistanceRef                   DcmTagKey(0x0016, 0x0089)
#define DCM_GPSDestDistance                      DcmTagKey(0x0016, 0x008a)
#define DCM_GPSProcessingMethod                  DcmTagKey(0x0016, 0x008b)
#define DCM_GPSAreaInformation                   DcmTagKey(0x0016, 0x008c)
#define DCM_GPSDateStamp                         DcmTagKey(0x0016, 0x008d)
#define DCM_GPSDifferential                      DcmTagKey(0x0016, 0x008e)
#define DCM_LightSourcePolarization              DcmTagKey(0x0016, 0x1001)
#define DCM_EmitterColorTemperature              DcmTagKey(0x0016, 0x1002)
#define DCM_ContactMethod                        DcmTagKey(0x0016, 0x1003)
#define DCM_ImmersionMedia                       DcmTagKey(0x0016, 0x1004)
#define DCM_OpticalMagnificationFactor           DcmTagKey(0x0016, 0x1005)
#define DCM_ContrastBolusAgent                   DcmTagKey(0x0018, 0x0010)
#define DCM_ContrastBolusAgentSequence           DcmTagKey(0x0018, 0x0012)
#define DCM_ContrastBolusT1Relaxivity            DcmTagKey(0x0018, 0x0013)
#define DCM_ContrastBolusAdministrationRouteSequence DcmTagKey(0x0018, 0x0014)
#define DCM_BodyPartExamined                     DcmTagKey(0x0018, 0x0015)
#define DCM_ScanningSequence                     DcmTagKey(0x0018, 0x0020)
#define DCM_SequenceVariant                      DcmTagKey(0x0018, 0x0021)
#define DCM_ScanOptions                          DcmTagKey(0x0018, 0x0022)
#define DCM_MRAcquisitionType                    DcmTagKey(0x0018, 0x0023)
#define DCM_SequenceName                         DcmTagKey(0x0018, 0x0024)
#define DCM_AngioFlag                            DcmTagKey(0x0018, 0x0025)
#define DCM_InterventionDrugInformationSequence  DcmTagKey(0x0018, 0x0026)
#define DCM_InterventionDrugStopTime             DcmTagKey(0x0018, 0x0027)
#define DCM_InterventionDrugDose                 DcmTagKey(0x0018, 0x0028)
#define DCM_InterventionDrugCodeSequence         DcmTagKey(0x0018, 0x0029)
#define DCM_AdditionalDrugSequence               DcmTagKey(0x0018, 0x002a)
#define DCM_RETIRED_Radionuclide                 DcmTagKey(0x0018, 0x0030)
#define DCM_Radiopharmaceutical                  DcmTagKey(0x0018, 0x0031)
#define DCM_RETIRED_EnergyWindowCenterline       DcmTagKey(0x0018, 0x0032)
#define DCM_RETIRED_EnergyWindowTotalWidth       DcmTagKey(0x0018, 0x0033)
#define DCM_InterventionDrugName                 DcmTagKey(0x0018, 0x0034)
#define DCM_InterventionDrugStartTime            DcmTagKey(0x0018, 0x0035)
#define DCM_InterventionSequence                 DcmTagKey(0x0018, 0x0036)
#define DCM_RETIRED_TherapyType                  DcmTagKey(0x0018, 0x0037)
#define DCM_InterventionStatus                   DcmTagKey(0x0018, 0x0038)
#define DCM_RETIRED_TherapyDescription           DcmTagKey(0x0018, 0x0039)
#define DCM_InterventionDescription              DcmTagKey(0x0018, 0x003a)
#define DCM_CineRate                             DcmTagKey(0x0018, 0x0040)
#define DCM_InitialCineRunState                  DcmTagKey(0x0018, 0x0042)
#define DCM_SliceThickness                       DcmTagKey(0x0018, 0x0050)
#define DCM_KVP                                  DcmTagKey(0x0018, 0x0060)
#define DCM_CountsAccumulated                    DcmTagKey(0x0018, 0x0070)
#define DCM_AcquisitionTerminationCondition      DcmTagKey(0x0018, 0x0071)
#define DCM_EffectiveDuration                    DcmTagKey(0x0018, 0x0072)
#define DCM_AcquisitionStartCondition            DcmTagKey(0x0018, 0x0073)
#define DCM_AcquisitionStartConditionData        DcmTagKey(0x0018, 0x0074)
#define DCM_AcquisitionTerminationConditionData  DcmTagKey(0x0018, 0x0075)
#define DCM_RepetitionTime                       DcmTagKey(0x0018, 0x0080)
#define DCM_EchoTime                             DcmTagKey(0x0018, 0x0081)
#define DCM_InversionTime                        DcmTagKey(0x0018, 0x0082)
#define DCM_NumberOfAverages                     DcmTagKey(0x0018, 0x0083)
#define DCM_ImagingFrequency                     DcmTagKey(0x0018, 0x0084)
#define DCM_ImagedNucleus                        DcmTagKey(0x0018, 0x0085)
#define DCM_EchoNumbers                          DcmTagKey(0x0018, 0x0086)
#define DCM_MagneticFieldStrength                DcmTagKey(0x0018, 0x0087)
#define DCM_SpacingBetweenSlices                 DcmTagKey(0x0018, 0x0088)
#define DCM_NumberOfPhaseEncodingSteps           DcmTagKey(0x0018, 0x0089)
#define DCM_DataCollectionDiameter               DcmTagKey(0x0018, 0x0090)
#define DCM_EchoTrainLength                      DcmTagKey(0x0018, 0x0091)
#define DCM_PercentSampling                      DcmTagKey(0x0018, 0x0093)
#define DCM_PercentPhaseFieldOfView              DcmTagKey(0x0018, 0x0094)
#define DCM_PixelBandwidth                       DcmTagKey(0x0018, 0x0095)
#define DCM_DeviceSerialNumber                   DcmTagKey(0x0018, 0x1000)
#define DCM_DeviceUID                            DcmTagKey(0x0018, 0x1002)
#define DCM_DeviceID                             DcmTagKey(0x0018, 0x1003)
#define DCM_PlateID                              DcmTagKey(0x0018, 0x1004)
#define DCM_GeneratorID                          DcmTagKey(0x0018, 0x1005)
#define DCM_GridID                               DcmTagKey(0x0018, 0x1006)
#define DCM_CassetteID                           DcmTagKey(0x0018, 0x1007)
#define DCM_GantryID                             DcmTagKey(0x0018, 0x1008)
#define DCM_UniqueDeviceIdentifier               DcmTagKey(0x0018, 0x1009)
#define DCM_UDISequence                          DcmTagKey(0x0018, 0x100a)
#define DCM_ManufacturerDeviceClassUID           DcmTagKey(0x0018, 0x100b)
#define DCM_SecondaryCaptureDeviceID             DcmTagKey(0x0018, 0x1010)
#define DCM_RETIRED_HardcopyCreationDeviceID     DcmTagKey(0x0018, 0x1011)
#define DCM_DateOfSecondaryCapture               DcmTagKey(0x0018, 0x1012)
#define DCM_TimeOfSecondaryCapture               DcmTagKey(0x0018, 0x1014)
#define DCM_SecondaryCaptureDeviceManufacturer   DcmTagKey(0x0018, 0x1016)
#define DCM_RETIRED_HardcopyDeviceManufacturer   DcmTagKey(0x0018, 0x1017)
#define DCM_SecondaryCaptureDeviceManufacturerModelName DcmTagKey(0x0018, 0x1018)
#define DCM_SecondaryCaptureDeviceSoftwareVersions DcmTagKey(0x0018, 0x1019)
#define DCM_RETIRED_HardcopyDeviceSoftwareVersion DcmTagKey(0x0018, 0x101a)
#define DCM_RETIRED_HardcopyDeviceManufacturerModelName DcmTagKey(0x0018, 0x101b)
#define DCM_SoftwareVersions                     DcmTagKey(0x0018, 0x1020)
#define DCM_VideoImageFormatAcquired             DcmTagKey(0x0018, 0x1022)
#define DCM_DigitalImageFormatAcquired           DcmTagKey(0x0018, 0x1023)
#define DCM_ProtocolName                         DcmTagKey(0x0018, 0x1030)
#define DCM_ContrastBolusRoute                   DcmTagKey(0x0018, 0x1040)
#define DCM_ContrastBolusVolume                  DcmTagKey(0x0018, 0x1041)
#define DCM_ContrastBolusStartTime               DcmTagKey(0x0018, 0x1042)
#define DCM_ContrastBolusStopTime                DcmTagKey(0x0018, 0x1043)
#define DCM_ContrastBolusTotalDose               DcmTagKey(0x0018, 0x1044)
#define DCM_SyringeCounts                        DcmTagKey(0x0018, 0x1045)
#define DCM_ContrastFlowRate                     DcmTagKey(0x0018, 0x1046)
#define DCM_ContrastFlowDuration                 DcmTagKey(0x0018, 0x1047)
#define DCM_ContrastBolusIngredient              DcmTagKey(0x0018, 0x1048)
#define DCM_ContrastBolusIngredientConcentration DcmTagKey(0x0018, 0x1049)
#define DCM_SpatialResolution                    DcmTagKey(0x0018, 0x1050)
#define DCM_TriggerTime                          DcmTagKey(0x0018, 0x1060)
#define DCM_TriggerSourceOrType                  DcmTagKey(0x0018, 0x1061)
#define DCM_NominalInterval                      DcmTagKey(0x0018, 0x1062)
#define DCM_FrameTime                            DcmTagKey(0x0018, 0x1063)
#define DCM_CardiacFramingType                   DcmTagKey(0x0018, 0x1064)
#define DCM_FrameTimeVector                      DcmTagKey(0x0018, 0x1065)
#define DCM_FrameDelay                           DcmTagKey(0x0018, 0x1066)
#define DCM_ImageTriggerDelay                    DcmTagKey(0x0018, 0x1067)
#define DCM_MultiplexGroupTimeOffset             DcmTagKey(0x0018, 0x1068)
#define DCM_TriggerTimeOffset                    DcmTagKey(0x0018, 0x1069)
#define DCM_SynchronizationTrigger               DcmTagKey(0x0018, 0x106a)
#define DCM_SynchronizationChannel               DcmTagKey(0x0018, 0x106c)
#define DCM_TriggerSamplePosition                DcmTagKey(0x0018, 0x106e)
#define DCM_RadiopharmaceuticalRoute             DcmTagKey(0x0018, 0x1070)
#define DCM_RadiopharmaceuticalVolume            DcmTagKey(0x0018, 0x1071)
#define DCM_RadiopharmaceuticalStartTime         DcmTagKey(0x0018, 0x1072)
#define DCM_RadiopharmaceuticalStopTime          DcmTagKey(0x0018, 0x1073)
#define DCM_RadionuclideTotalDose                DcmTagKey(0x0018, 0x1074)
#define DCM_RadionuclideHalfLife                 DcmTagKey(0x0018, 0x1075)
#define DCM_RadionuclidePositronFraction         DcmTagKey(0x0018, 0x1076)
#define DCM_RadiopharmaceuticalSpecificActivity  DcmTagKey(0x0018, 0x1077)
#define DCM_RadiopharmaceuticalStartDateTime     DcmTagKey(0x0018, 0x1078)
#define DCM_RadiopharmaceuticalStopDateTime      DcmTagKey(0x0018, 0x1079)
#define DCM_BeatRejectionFlag                    DcmTagKey(0x0018, 0x1080)
#define DCM_LowRRValue                           DcmTagKey(0x0018, 0x1081)
#define DCM_HighRRValue                          DcmTagKey(0x0018, 0x1082)
#define DCM_IntervalsAcquired                    DcmTagKey(0x0018, 0x1083)
#define DCM_IntervalsRejected                    DcmTagKey(0x0018, 0x1084)
#define DCM_PVCRejection                         DcmTagKey(0x0018, 0x1085)
#define DCM_SkipBeats                            DcmTagKey(0x0018, 0x1086)
#define DCM_HeartRate                            DcmTagKey(0x0018, 0x1088)
#define DCM_CardiacNumberOfImages                DcmTagKey(0x0018, 0x1090)
#define DCM_TriggerWindow                        DcmTagKey(0x0018, 0x1094)
#define DCM_ReconstructionDiameter               DcmTagKey(0x0018, 0x1100)
#define DCM_DistanceSourceToDetector             DcmTagKey(0x0018, 0x1110)
#define DCM_DistanceSourceToPatient              DcmTagKey(0x0018, 0x1111)
#define DCM_EstimatedRadiographicMagnificationFactor DcmTagKey(0x0018, 0x1114)
#define DCM_GantryDetectorTilt                   DcmTagKey(0x0018, 0x1120)
#define DCM_GantryDetectorSlew                   DcmTagKey(0x0018, 0x1121)
#define DCM_TableHeight                          DcmTagKey(0x0018, 0x1130)
#define DCM_TableTraverse                        DcmTagKey(0x0018, 0x1131)
#define DCM_TableMotion                          DcmTagKey(0x0018, 0x1134)
#define DCM_TableVerticalIncrement               DcmTagKey(0x0018, 0x1135)
#define DCM_TableLateralIncrement                DcmTagKey(0x0018, 0x1136)
#define DCM_TableLongitudinalIncrement           DcmTagKey(0x0018, 0x1137)
#define DCM_TableAngle                           DcmTagKey(0x0018, 0x1138)
#define DCM_TableType                            DcmTagKey(0x0018, 0x113a)
#define DCM_RotationDirection                    DcmTagKey(0x0018, 0x1140)
#define DCM_RETIRED_AngularPosition              DcmTagKey(0x0018, 0x1141)
#define DCM_RadialPosition                       DcmTagKey(0x0018, 0x1142)
#define DCM_ScanArc                              DcmTagKey(0x0018, 0x1143)
#define DCM_AngularStep                          DcmTagKey(0x0018, 0x1144)
#define DCM_CenterOfRotationOffset               DcmTagKey(0x0018, 0x1145)
#define DCM_RETIRED_RotationOffset               DcmTagKey(0x0018, 0x1146)
#define DCM_FieldOfViewShape                     DcmTagKey(0x0018, 0x1147)
#define DCM_FieldOfViewDimensions                DcmTagKey(0x0018, 0x1149)
#define DCM_ExposureTime                         DcmTagKey(0x0018, 0x1150)
#define DCM_XRayTubeCurrent                      DcmTagKey(0x0018, 0x1151)
#define DCM_Exposure                             DcmTagKey(0x0018, 0x1152)
#define DCM_ExposureInuAs                        DcmTagKey(0x0018, 0x1153)
#define DCM_AveragePulseWidth                    DcmTagKey(0x0018, 0x1154)
#define DCM_RadiationSetting                     DcmTagKey(0x0018, 0x1155)
#define DCM_RectificationType                    DcmTagKey(0x0018, 0x1156)
#define DCM_RadiationMode                        DcmTagKey(0x0018, 0x115a)
#define DCM_ImageAndFluoroscopyAreaDoseProduct   DcmTagKey(0x0018, 0x115e)
#define DCM_FilterType                           DcmTagKey(0x0018, 0x1160)
#define DCM_TypeOfFilters                        DcmTagKey(0x0018, 0x1161)
#define DCM_IntensifierSize                      DcmTagKey(0x0018, 0x1162)
#define DCM_ImagerPixelSpacing                   DcmTagKey(0x0018, 0x1164)
#define DCM_Grid                                 DcmTagKey(0x0018, 0x1166)
#define DCM_GeneratorPower                       DcmTagKey(0x0018, 0x1170)
#define DCM_CollimatorGridName                   DcmTagKey(0x0018, 0x1180)
#define DCM_CollimatorType                       DcmTagKey(0x0018, 0x1181)
#define DCM_FocalDistance                        DcmTagKey(0x0018, 0x1182)
#define DCM_XFocusCenter                         DcmTagKey(0x0018, 0x1183)
#define DCM_YFocusCenter                         DcmTagKey(0x0018, 0x1184)
#define DCM_FocalSpots                           DcmTagKey(0x0018, 0x1190)
#define DCM_AnodeTargetMaterial                  DcmTagKey(0x0018, 0x1191)
#define DCM_BodyPartThickness                    DcmTagKey(0x0018, 0x11a0)
#define DCM_CompressionForce                     DcmTagKey(0x0018, 0x11a2)
#define DCM_CompressionPressure                  DcmTagKey(0x0018, 0x11a3)
#define DCM_PaddleDescription                    DcmTagKey(0x0018, 0x11a4)
#define DCM_CompressionContactArea               DcmTagKey(0x0018, 0x11a5)
#define DCM_AcquisitionMode                      DcmTagKey(0x0018, 0x11b0)
#define DCM_DoseModeName                         DcmTagKey(0x0018, 0x11b1)
#define DCM_AcquiredSubtractionMaskFlag          DcmTagKey(0x0018, 0x11b2)
#define DCM_FluoroscopyPersistenceFlag           DcmTagKey(0x0018, 0x11b3)
#define DCM_FluoroscopyLastImageHoldPersistenceFlag DcmTagKey(0x0018, 0x11b4)
#define DCM_UpperLimitNumberOfPersistentFluoroscopyFrames DcmTagKey(0x0018, 0x11b5)
#define DCM_ContrastBolusAutoInjectionTriggerFlag DcmTagKey(0x0018, 0x11b6)
#define DCM_ContrastBolusInjectionDelay          DcmTagKey(0x0018, 0x11b7)
#define DCM_XAAcquisitionPhaseDetailsSequence    DcmTagKey(0x0018, 0x11b8)
#define DCM_XAAcquisitionFrameRate               DcmTagKey(0x0018, 0x11b9)
#define DCM_XAPlaneDetailsSequence               DcmTagKey(0x0018, 0x11ba)
#define DCM_AcquisitionFieldOfViewLabel          DcmTagKey(0x0018, 0x11bb)
#define DCM_XRayFilterDetailsSequence            DcmTagKey(0x0018, 0x11bc)
#define DCM_XAAcquisitionDuration                DcmTagKey(0x0018, 0x11bd)
#define DCM_ReconstructionPipelineType           DcmTagKey(0x0018, 0x11be)
#define DCM_ImageFilterDetailsSequence           DcmTagKey(0x0018, 0x11bf)
#define DCM_AppliedMaskSubtractionFlag           DcmTagKey(0x0018, 0x11c0)
#define DCM_RequestedSeriesDescriptionCodeSequence DcmTagKey(0x0018, 0x11c1)
#define DCM_DateOfLastCalibration                DcmTagKey(0x0018, 0x1200)
#define DCM_TimeOfLastCalibration                DcmTagKey(0x0018, 0x1201)
#define DCM_DateTimeOfLastCalibration            DcmTagKey(0x0018, 0x1202)
#define DCM_CalibrationDateTime                  DcmTagKey(0x0018, 0x1203)
#define DCM_DateOfManufacture                    DcmTagKey(0x0018, 0x1204)
#define DCM_DateOfInstallation                   DcmTagKey(0x0018, 0x1205)
#define DCM_ConvolutionKernel                    DcmTagKey(0x0018, 0x1210)
#define DCM_RETIRED_UpperLowerPixelValues        DcmTagKey(0x0018, 0x1240)
#define DCM_ActualFrameDuration                  DcmTagKey(0x0018, 0x1242)
#define DCM_CountRate                            DcmTagKey(0x0018, 0x1243)
#define DCM_PreferredPlaybackSequencing          DcmTagKey(0x0018, 0x1244)
#define DCM_ReceiveCoilName                      DcmTagKey(0x0018, 0x1250)
#define DCM_TransmitCoilName                     DcmTagKey(0x0018, 0x1251)
#define DCM_PlateType                            DcmTagKey(0x0018, 0x1260)
#define DCM_PhosphorType                         DcmTagKey(0x0018, 0x1261)
#define DCM_WaterEquivalentDiameter              DcmTagKey(0x0018, 0x1271)
#define DCM_WaterEquivalentDiameterCalculationMethodCodeSequence DcmTagKey(0x0018, 0x1272)
#define DCM_ScanVelocity                         DcmTagKey(0x0018, 0x1300)
#define DCM_WholeBodyTechnique                   DcmTagKey(0x0018, 0x1301)
#define DCM_ScanLength                           DcmTagKey(0x0018, 0x1302)
#define DCM_AcquisitionMatrix                    DcmTagKey(0x0018, 0x1310)
#define DCM_InPlanePhaseEncodingDirection        DcmTagKey(0x0018, 0x1312)
#define DCM_FlipAngle                            DcmTagKey(0x0018, 0x1314)
#define DCM_VariableFlipAngleFlag                DcmTagKey(0x0018, 0x1315)
#define DCM_SAR                                  DcmTagKey(0x0018, 0x1316)
#define DCM_dBdt                                 DcmTagKey(0x0018, 0x1318)
#define DCM_B1rms                                DcmTagKey(0x0018, 0x1320)
#define DCM_AcquisitionDeviceProcessingDescription DcmTagKey(0x0018, 0x1400)
#define DCM_AcquisitionDeviceProcessingCode      DcmTagKey(0x0018, 0x1401)
#define DCM_CassetteOrientation                  DcmTagKey(0x0018, 0x1402)
#define DCM_CassetteSize                         DcmTagKey(0x0018, 0x1403)
#define DCM_ExposuresOnPlate                     DcmTagKey(0x0018, 0x1404)
#define DCM_RelativeXRayExposure                 DcmTagKey(0x0018, 0x1405)
#define DCM_ExposureIndex                        DcmTagKey(0x0018, 0x1411)
#define DCM_TargetExposureIndex                  DcmTagKey(0x0018, 0x1412)
#define DCM_DeviationIndex                       DcmTagKey(0x0018, 0x1413)
#define DCM_ColumnAngulation                     DcmTagKey(0x0018, 0x1450)
#define DCM_TomoLayerHeight                      DcmTagKey(0x0018, 0x1460)
#define DCM_TomoAngle                            DcmTagKey(0x0018, 0x1470)
#define DCM_TomoTime                             DcmTagKey(0x0018, 0x1480)
#define DCM_TomoType                             DcmTagKey(0x0018, 0x1490)
#define DCM_TomoClass                            DcmTagKey(0x0018, 0x1491)
#define DCM_NumberOfTomosynthesisSourceImages    DcmTagKey(0x0018, 0x1495)
#define DCM_PositionerMotion                     DcmTagKey(0x0018, 0x1500)
#define DCM_PositionerType                       DcmTagKey(0x0018, 0x1508)
#define DCM_PositionerPrimaryAngle               DcmTagKey(0x0018, 0x1510)
#define DCM_PositionerSecondaryAngle             DcmTagKey(0x0018, 0x1511)
#define DCM_PositionerPrimaryAngleIncrement      DcmTagKey(0x0018, 0x1520)
#define DCM_PositionerSecondaryAngleIncrement    DcmTagKey(0x0018, 0x1521)
#define DCM_DetectorPrimaryAngle                 DcmTagKey(0x0018, 0x1530)
#define DCM_DetectorSecondaryAngle               DcmTagKey(0x0018, 0x1531)
#define DCM_ShutterShape                         DcmTagKey(0x0018, 0x1600)
#define DCM_ShutterLeftVerticalEdge              DcmTagKey(0x0018, 0x1602)
#define DCM_ShutterRightVerticalEdge             DcmTagKey(0x0018, 0x1604)
#define DCM_ShutterUpperHorizontalEdge           DcmTagKey(0x0018, 0x1606)
#define DCM_ShutterLowerHorizontalEdge           DcmTagKey(0x0018, 0x1608)
#define DCM_CenterOfCircularShutter              DcmTagKey(0x0018, 0x1610)
#define DCM_RadiusOfCircularShutter              DcmTagKey(0x0018, 0x1612)
#define DCM_VerticesOfThePolygonalShutter        DcmTagKey(0x0018, 0x1620)
#define DCM_ShutterPresentationValue             DcmTagKey(0x0018, 0x1622)
#define DCM_ShutterOverlayGroup                  DcmTagKey(0x0018, 0x1623)
#define DCM_ShutterPresentationColorCIELabValue  DcmTagKey(0x0018, 0x1624)
#define DCM_OutlineShapeType                     DcmTagKey(0x0018, 0x1630)
#define DCM_OutlineLeftVerticalEdge              DcmTagKey(0x0018, 0x1631)
#define DCM_OutlineRightVerticalEdge             DcmTagKey(0x0018, 0x1632)
#define DCM_OutlineUpperHorizontalEdge           DcmTagKey(0x0018, 0x1633)
#define DCM_OutlineLowerHorizontalEdge           DcmTagKey(0x0018, 0x1634)
#define DCM_CenterOfCircularOutline              DcmTagKey(0x0018, 0x1635)
#define DCM_DiameterOfCircularOutline            DcmTagKey(0x0018, 0x1636)
#define DCM_NumberOfPolygonalVertices            DcmTagKey(0x0018, 0x1637)
#define DCM_VerticesOfThePolygonalOutline        DcmTagKey(0x0018, 0x1638)
#define DCM_CollimatorShape                      DcmTagKey(0x0018, 0x1700)
#define DCM_CollimatorLeftVerticalEdge           DcmTagKey(0x0018, 0x1702)
#define DCM_CollimatorRightVerticalEdge          DcmTagKey(0x0018, 0x1704)
#define DCM_CollimatorUpperHorizontalEdge        DcmTagKey(0x0018, 0x1706)
#define DCM_CollimatorLowerHorizontalEdge        DcmTagKey(0x0018, 0x1708)
#define DCM_CenterOfCircularCollimator           DcmTagKey(0x0018, 0x1710)
#define DCM_RadiusOfCircularCollimator           DcmTagKey(0x0018, 0x1712)
#define DCM_VerticesOfThePolygonalCollimator     DcmTagKey(0x0018, 0x1720)
#define DCM_AcquisitionTimeSynchronized          DcmTagKey(0x0018, 0x1800)
#define DCM_TimeSource                           DcmTagKey(0x0018, 0x1801)
#define DCM_TimeDistributionProtocol             DcmTagKey(0x0018, 0x1802)
#define DCM_NTPSourceAddress                     DcmTagKey(0x0018, 0x1803)
#define DCM_PageNumberVector                     DcmTagKey(0x0018, 0x2001)
#define DCM_FrameLabelVector                     DcmTagKey(0x0018, 0x2002)
#define DCM_FramePrimaryAngleVector              DcmTagKey(0x0018, 0x2003)
#define DCM_FrameSecondaryAngleVector            DcmTagKey(0x0018, 0x2004)
#define DCM_SliceLocationVector                  DcmTagKey(0x0018, 0x2005)
#define DCM_DisplayWindowLabelVector             DcmTagKey(0x0018, 0x2006)
#define DCM_NominalScannedPixelSpacing           DcmTagKey(0x0018, 0x2010)
#define DCM_DigitizingDeviceTransportDirection   DcmTagKey(0x0018, 0x2020)
#define DCM_RotationOfScannedFilm                DcmTagKey(0x0018, 0x2030)
#define DCM_BiopsyTargetSequence                 DcmTagKey(0x0018, 0x2041)
#define DCM_TargetUID                            DcmTagKey(0x0018, 0x2042)
#define DCM_LocalizingCursorPosition             DcmTagKey(0x0018, 0x2043)
#define DCM_CalculatedTargetPosition             DcmTagKey(0x0018, 0x2044)
#define DCM_TargetLabel                          DcmTagKey(0x0018, 0x2045)
#define DCM_DisplayedZValue                      DcmTagKey(0x0018, 0x2046)
#define DCM_IVUSAcquisition                      DcmTagKey(0x0018, 0x3100)
#define DCM_IVUSPullbackRate                     DcmTagKey(0x0018, 0x3101)
#define DCM_IVUSGatedRate                        DcmTagKey(0x0018, 0x3102)
#define DCM_IVUSPullbackStartFrameNumber         DcmTagKey(0x0018, 0x3103)
#define DCM_IVUSPullbackStopFrameNumber          DcmTagKey(0x0018, 0x3104)
#define DCM_LesionNumber                         DcmTagKey(0x0018, 0x3105)
#define DCM_RETIRED_AcquisitionComments          DcmTagKey(0x0018, 0x4000)
#define DCM_OutputPower                          DcmTagKey(0x0018, 0x5000)
#define DCM_TransducerData                       DcmTagKey(0x0018, 0x5010)
#define DCM_TransducerIdentificationSequence     DcmTagKey(0x0018, 0x5011)
#define DCM_FocusDepth                           DcmTagKey(0x0018, 0x5012)
#define DCM_ProcessingFunction                   DcmTagKey(0x0018, 0x5020)
#define DCM_RETIRED_PostprocessingFunction       DcmTagKey(0x0018, 0x5021)
#define DCM_MechanicalIndex                      DcmTagKey(0x0018, 0x5022)
#define DCM_BoneThermalIndex                     DcmTagKey(0x0018, 0x5024)
#define DCM_CranialThermalIndex                  DcmTagKey(0x0018, 0x5026)
#define DCM_SoftTissueThermalIndex               DcmTagKey(0x0018, 0x5027)
#define DCM_SoftTissueFocusThermalIndex          DcmTagKey(0x0018, 0x5028)
#define DCM_SoftTissueSurfaceThermalIndex        DcmTagKey(0x0018, 0x5029)
#define DCM_RETIRED_DynamicRange                 DcmTagKey(0x0018, 0x5030)
#define DCM_RETIRED_TotalGain                    DcmTagKey(0x0018, 0x5040)
#define DCM_DepthOfScanField                     DcmTagKey(0x0018, 0x5050)
#define DCM_PatientPosition                      DcmTagKey(0x0018, 0x5100)
#define DCM_ViewPosition                         DcmTagKey(0x0018, 0x5101)
#define DCM_ProjectionEponymousNameCodeSequence  DcmTagKey(0x0018, 0x5104)
#define DCM_RETIRED_ImageTransformationMatrix    DcmTagKey(0x0018, 0x5210)
#define DCM_RETIRED_ImageTranslationVector       DcmTagKey(0x0018, 0x5212)
#define DCM_Sensitivity                          DcmTagKey(0x0018, 0x6000)
#define DCM_SequenceOfUltrasoundRegions          DcmTagKey(0x0018, 0x6011)
#define DCM_RegionSpatialFormat                  DcmTagKey(0x0018, 0x6012)
#define DCM_RegionDataType                       DcmTagKey(0x0018, 0x6014)
#define DCM_RegionFlags                          DcmTagKey(0x0018, 0x6016)
#define DCM_RegionLocationMinX0                  DcmTagKey(0x0018, 0x6018)
#define DCM_RegionLocationMinY0                  DcmTagKey(0x0018, 0x601a)
#define DCM_RegionLocationMaxX1                  DcmTagKey(0x0018, 0x601c)
#define DCM_RegionLocationMaxY1                  DcmTagKey(0x0018, 0x601e)
#define DCM_ReferencePixelX0                     DcmTagKey(0x0018, 0x6020)
#define DCM_ReferencePixelY0                     DcmTagKey(0x0018, 0x6022)
#define DCM_PhysicalUnitsXDirection              DcmTagKey(0x0018, 0x6024)
#define DCM_PhysicalUnitsYDirection              DcmTagKey(0x0018, 0x6026)
#define DCM_ReferencePixelPhysicalValueX         DcmTagKey(0x0018, 0x6028)
#define DCM_ReferencePixelPhysicalValueY         DcmTagKey(0x0018, 0x602a)
#define DCM_PhysicalDeltaX                       DcmTagKey(0x0018, 0x602c)
#define DCM_PhysicalDeltaY                       DcmTagKey(0x0018, 0x602e)
#define DCM_TransducerFrequency                  DcmTagKey(0x0018, 0x6030)
#define DCM_TransducerType                       DcmTagKey(0x0018, 0x6031)
#define DCM_PulseRepetitionFrequency             DcmTagKey(0x0018, 0x6032)
#define DCM_DopplerCorrectionAngle               DcmTagKey(0x0018, 0x6034)
#define DCM_SteeringAngle                        DcmTagKey(0x0018, 0x6036)
#define DCM_RETIRED_DopplerSampleVolumeXPositionRetired DcmTagKey(0x0018, 0x6038)
#define DCM_DopplerSampleVolumeXPosition         DcmTagKey(0x0018, 0x6039)
#define DCM_RETIRED_DopplerSampleVolumeYPositionRetired DcmTagKey(0x0018, 0x603a)
#define DCM_DopplerSampleVolumeYPosition         DcmTagKey(0x0018, 0x603b)
#define DCM_RETIRED_TMLinePositionX0Retired      DcmTagKey(0x0018, 0x603c)
#define DCM_TMLinePositionX0                     DcmTagKey(0x0018, 0x603d)
#define DCM_RETIRED_TMLinePositionY0Retired      DcmTagKey(0x0018, 0x603e)
#define DCM_TMLinePositionY0                     DcmTagKey(0x0018, 0x603f)
#define DCM_RETIRED_TMLinePositionX1Retired      DcmTagKey(0x0018, 0x6040)
#define DCM_TMLinePositionX1                     DcmTagKey(0x0018, 0x6041)
#define DCM_RETIRED_TMLinePositionY1Retired      DcmTagKey(0x0018, 0x6042)
#define DCM_TMLinePositionY1                     DcmTagKey(0x0018, 0x6043)
#define DCM_PixelComponentOrganization           DcmTagKey(0x0018, 0x6044)
#define DCM_PixelComponentMask                   DcmTagKey(0x0018, 0x6046)
#define DCM_PixelComponentRangeStart             DcmTagKey(0x0018, 0x6048)
#define DCM_PixelComponentRangeStop              DcmTagKey(0x0018, 0x604a)
#define DCM_PixelComponentPhysicalUnits          DcmTagKey(0x0018, 0x604c)
#define DCM_PixelComponentDataType               DcmTagKey(0x0018, 0x604e)
#define DCM_NumberOfTableBreakPoints             DcmTagKey(0x0018, 0x6050)
#define DCM_TableOfXBreakPoints                  DcmTagKey(0x0018, 0x6052)
#define DCM_TableOfYBreakPoints                  DcmTagKey(0x0018, 0x6054)
#define DCM_NumberOfTableEntries                 DcmTagKey(0x0018, 0x6056)
#define DCM_TableOfPixelValues                   DcmTagKey(0x0018, 0x6058)
#define DCM_TableOfParameterValues               DcmTagKey(0x0018, 0x605a)
#define DCM_RWaveTimeVector                      DcmTagKey(0x0018, 0x6060)
#define DCM_ActiveImageAreaOverlayGroup          DcmTagKey(0x0018, 0x6070)
#define DCM_DetectorConditionsNominalFlag        DcmTagKey(0x0018, 0x7000)
#define DCM_DetectorTemperature                  DcmTagKey(0x0018, 0x7001)
#define DCM_DetectorType                         DcmTagKey(0x0018, 0x7004)
#define DCM_DetectorConfiguration                DcmTagKey(0x0018, 0x7005)
#define DCM_DetectorDescription                  DcmTagKey(0x0018, 0x7006)
#define DCM_DetectorMode                         DcmTagKey(0x0018, 0x7008)
#define DCM_DetectorID                           DcmTagKey(0x0018, 0x700a)
#define DCM_DateOfLastDetectorCalibration        DcmTagKey(0x0018, 0x700c)
#define DCM_TimeOfLastDetectorCalibration        DcmTagKey(0x0018, 0x700e)
#define DCM_ExposuresOnDetectorSinceLastCalibration DcmTagKey(0x0018, 0x7010)
#define DCM_ExposuresOnDetectorSinceManufactured DcmTagKey(0x0018, 0x7011)
#define DCM_DetectorTimeSinceLastExposure        DcmTagKey(0x0018, 0x7012)
#define DCM_DetectorActiveTime                   DcmTagKey(0x0018, 0x7014)
#define DCM_DetectorActivationOffsetFromExposure DcmTagKey(0x0018, 0x7016)
#define DCM_DetectorBinning                      DcmTagKey(0x0018, 0x701a)
#define DCM_DetectorElementPhysicalSize          DcmTagKey(0x0018, 0x7020)
#define DCM_DetectorElementSpacing               DcmTagKey(0x0018, 0x7022)
#define DCM_DetectorActiveShape                  DcmTagKey(0x0018, 0x7024)
#define DCM_DetectorActiveDimensions             DcmTagKey(0x0018, 0x7026)
#define DCM_DetectorActiveOrigin                 DcmTagKey(0x0018, 0x7028)
#define DCM_DetectorManufacturerName             DcmTagKey(0x0018, 0x702a)
#define DCM_DetectorManufacturerModelName        DcmTagKey(0x0018, 0x702b)
#define DCM_FieldOfViewOrigin                    DcmTagKey(0x0018, 0x7030)
#define DCM_FieldOfViewRotation                  DcmTagKey(0x0018, 0x7032)
#define DCM_FieldOfViewHorizontalFlip            DcmTagKey(0x0018, 0x7034)
#define DCM_PixelDataAreaOriginRelativeToFOV     DcmTagKey(0x0018, 0x7036)
#define DCM_PixelDataAreaRotationAngleRelativeToFOV DcmTagKey(0x0018, 0x7038)
#define DCM_GridAbsorbingMaterial                DcmTagKey(0x0018, 0x7040)
#define DCM_GridSpacingMaterial                  DcmTagKey(0x0018, 0x7041)
#define DCM_GridThickness                        DcmTagKey(0x0018, 0x7042)
#define DCM_GridPitch                            DcmTagKey(0x0018, 0x7044)
#define DCM_GridAspectRatio                      DcmTagKey(0x0018, 0x7046)
#define DCM_GridPeriod                           DcmTagKey(0x0018, 0x7048)
#define DCM_GridFocalDistance                    DcmTagKey(0x0018, 0x704c)
#define DCM_FilterMaterial                       DcmTagKey(0x0018, 0x7050)
#define DCM_FilterThicknessMinimum               DcmTagKey(0x0018, 0x7052)
#define DCM_FilterThicknessMaximum               DcmTagKey(0x0018, 0x7054)
#define DCM_FilterBeamPathLengthMinimum          DcmTagKey(0x0018, 0x7056)
#define DCM_FilterBeamPathLengthMaximum          DcmTagKey(0x0018, 0x7058)
#define DCM_ExposureControlMode                  DcmTagKey(0x0018, 0x7060)
#define DCM_ExposureControlModeDescription       DcmTagKey(0x0018, 0x7062)
#define DCM_ExposureStatus                       DcmTagKey(0x0018, 0x7064)
#define DCM_PhototimerSetting                    DcmTagKey(0x0018, 0x7065)
#define DCM_ExposureTimeInuS                     DcmTagKey(0x0018, 0x8150)
#define DCM_XRayTubeCurrentInuA                  DcmTagKey(0x0018, 0x8151)
#define DCM_ContentQualification                 DcmTagKey(0x0018, 0x9004)
#define DCM_PulseSequenceName                    DcmTagKey(0x0018, 0x9005)
#define DCM_MRImagingModifierSequence            DcmTagKey(0x0018, 0x9006)
#define DCM_EchoPulseSequence                    DcmTagKey(0x0018, 0x9008)
#define DCM_InversionRecovery                    DcmTagKey(0x0018, 0x9009)
#define DCM_FlowCompensation                     DcmTagKey(0x0018, 0x9010)
#define DCM_MultipleSpinEcho                     DcmTagKey(0x0018, 0x9011)
#define DCM_MultiPlanarExcitation                DcmTagKey(0x0018, 0x9012)
#define DCM_PhaseContrast                        DcmTagKey(0x0018, 0x9014)
#define DCM_TimeOfFlightContrast                 DcmTagKey(0x0018, 0x9015)
#define DCM_Spoiling                             DcmTagKey(0x0018, 0x9016)
#define DCM_SteadyStatePulseSequence             DcmTagKey(0x0018, 0x9017)
#define DCM_EchoPlanarPulseSequence              DcmTagKey(0x0018, 0x9018)
#define DCM_TagAngleFirstAxis                    DcmTagKey(0x0018, 0x9019)
#define DCM_MagnetizationTransfer                DcmTagKey(0x0018, 0x9020)
#define DCM_T2Preparation                        DcmTagKey(0x0018, 0x9021)
#define DCM_BloodSignalNulling                   DcmTagKey(0x0018, 0x9022)
#define DCM_SaturationRecovery                   DcmTagKey(0x0018, 0x9024)
#define DCM_SpectrallySelectedSuppression        DcmTagKey(0x0018, 0x9025)
#define DCM_SpectrallySelectedExcitation         DcmTagKey(0x0018, 0x9026)
#define DCM_SpatialPresaturation                 DcmTagKey(0x0018, 0x9027)
#define DCM_Tagging                              DcmTagKey(0x0018, 0x9028)
#define DCM_OversamplingPhase                    DcmTagKey(0x0018, 0x9029)
#define DCM_TagSpacingFirstDimension             DcmTagKey(0x0018, 0x9030)
#define DCM_GeometryOfKSpaceTraversal            DcmTagKey(0x0018, 0x9032)
#define DCM_SegmentedKSpaceTraversal             DcmTagKey(0x0018, 0x9033)
#define DCM_RectilinearPhaseEncodeReordering     DcmTagKey(0x0018, 0x9034)
#define DCM_TagThickness                         DcmTagKey(0x0018, 0x9035)
#define DCM_PartialFourierDirection              DcmTagKey(0x0018, 0x9036)
#define DCM_CardiacSynchronizationTechnique      DcmTagKey(0x0018, 0x9037)
#define DCM_ReceiveCoilManufacturerName          DcmTagKey(0x0018, 0x9041)
#define DCM_MRReceiveCoilSequence                DcmTagKey(0x0018, 0x9042)
#define DCM_ReceiveCoilType                      DcmTagKey(0x0018, 0x9043)
#define DCM_QuadratureReceiveCoil                DcmTagKey(0x0018, 0x9044)
#define DCM_MultiCoilDefinitionSequence          DcmTagKey(0x0018, 0x9045)
#define DCM_MultiCoilConfiguration               DcmTagKey(0x0018, 0x9046)
#define DCM_MultiCoilElementName                 DcmTagKey(0x0018, 0x9047)
#define DCM_MultiCoilElementUsed                 DcmTagKey(0x0018, 0x9048)
#define DCM_MRTransmitCoilSequence               DcmTagKey(0x0018, 0x9049)
#define DCM_TransmitCoilManufacturerName         DcmTagKey(0x0018, 0x9050)
#define DCM_TransmitCoilType                     DcmTagKey(0x0018, 0x9051)
#define DCM_SpectralWidth                        DcmTagKey(0x0018, 0x9052)
#define DCM_ChemicalShiftReference               DcmTagKey(0x0018, 0x9053)
#define DCM_VolumeLocalizationTechnique          DcmTagKey(0x0018, 0x9054)
#define DCM_MRAcquisitionFrequencyEncodingSteps  DcmTagKey(0x0018, 0x9058)
#define DCM_Decoupling                           DcmTagKey(0x0018, 0x9059)
#define DCM_DecoupledNucleus                     DcmTagKey(0x0018, 0x9060)
#define DCM_DecouplingFrequency                  DcmTagKey(0x0018, 0x9061)
#define DCM_DecouplingMethod                     DcmTagKey(0x0018, 0x9062)
#define DCM_DecouplingChemicalShiftReference     DcmTagKey(0x0018, 0x9063)
#define DCM_KSpaceFiltering                      DcmTagKey(0x0018, 0x9064)
#define DCM_TimeDomainFiltering                  DcmTagKey(0x0018, 0x9065)
#define DCM_NumberOfZeroFills                    DcmTagKey(0x0018, 0x9066)
#define DCM_BaselineCorrection                   DcmTagKey(0x0018, 0x9067)
#define DCM_ParallelReductionFactorInPlane       DcmTagKey(0x0018, 0x9069)
#define DCM_CardiacRRIntervalSpecified           DcmTagKey(0x0018, 0x9070)
#define DCM_AcquisitionDuration                  DcmTagKey(0x0018, 0x9073)
#define DCM_FrameAcquisitionDateTime             DcmTagKey(0x0018, 0x9074)
#define DCM_DiffusionDirectionality              DcmTagKey(0x0018, 0x9075)
#define DCM_DiffusionGradientDirectionSequence   DcmTagKey(0x0018, 0x9076)
#define DCM_ParallelAcquisition                  DcmTagKey(0x0018, 0x9077)
#define DCM_ParallelAcquisitionTechnique         DcmTagKey(0x0018, 0x9078)
#define DCM_InversionTimes                       DcmTagKey(0x0018, 0x9079)
#define DCM_MetaboliteMapDescription             DcmTagKey(0x0018, 0x9080)
#define DCM_PartialFourier                       DcmTagKey(0x0018, 0x9081)
#define DCM_EffectiveEchoTime                    DcmTagKey(0x0018, 0x9082)
#define DCM_MetaboliteMapCodeSequence            DcmTagKey(0x0018, 0x9083)
#define DCM_ChemicalShiftSequence                DcmTagKey(0x0018, 0x9084)
#define DCM_CardiacSignalSource                  DcmTagKey(0x0018, 0x9085)
#define DCM_DiffusionBValue                      DcmTagKey(0x0018, 0x9087)
#define DCM_DiffusionGradientOrientation         DcmTagKey(0x0018, 0x9089)
#define DCM_VelocityEncodingDirection            DcmTagKey(0x0018, 0x9090)
#define DCM_VelocityEncodingMinimumValue         DcmTagKey(0x0018, 0x9091)
#define DCM_VelocityEncodingAcquisitionSequence  DcmTagKey(0x0018, 0x9092)
#define DCM_NumberOfKSpaceTrajectories           DcmTagKey(0x0018, 0x9093)
#define DCM_CoverageOfKSpace                     DcmTagKey(0x0018, 0x9094)
#define DCM_SpectroscopyAcquisitionPhaseRows     DcmTagKey(0x0018, 0x9095)
#define DCM_RETIRED_ParallelReductionFactorInPlaneRetired DcmTagKey(0x0018, 0x9096)
#define DCM_TransmitterFrequency                 DcmTagKey(0x0018, 0x9098)
#define DCM_ResonantNucleus                      DcmTagKey(0x0018, 0x9100)
#define DCM_FrequencyCorrection                  DcmTagKey(0x0018, 0x9101)
#define DCM_MRSpectroscopyFOVGeometrySequence    DcmTagKey(0x0018, 0x9103)
#define DCM_SlabThickness                        DcmTagKey(0x0018, 0x9104)
#define DCM_SlabOrientation                      DcmTagKey(0x0018, 0x9105)
#define DCM_MidSlabPosition                      DcmTagKey(0x0018, 0x9106)
#define DCM_MRSpatialSaturationSequence          DcmTagKey(0x0018, 0x9107)
#define DCM_MRTimingAndRelatedParametersSequence DcmTagKey(0x0018, 0x9112)
#define DCM_MREchoSequence                       DcmTagKey(0x0018, 0x9114)
#define DCM_MRModifierSequence                   DcmTagKey(0x0018, 0x9115)
#define DCM_MRDiffusionSequence                  DcmTagKey(0x0018, 0x9117)
#define DCM_CardiacSynchronizationSequence       DcmTagKey(0x0018, 0x9118)
#define DCM_MRAveragesSequence                   DcmTagKey(0x0018, 0x9119)
#define DCM_MRFOVGeometrySequence                DcmTagKey(0x0018, 0x9125)
#define DCM_VolumeLocalizationSequence           DcmTagKey(0x0018, 0x9126)
#define DCM_SpectroscopyAcquisitionDataColumns   DcmTagKey(0x0018, 0x9127)
#define DCM_DiffusionAnisotropyType              DcmTagKey(0x0018, 0x9147)
#define DCM_FrameReferenceDateTime               DcmTagKey(0x0018, 0x9151)
#define DCM_MRMetaboliteMapSequence              DcmTagKey(0x0018, 0x9152)
#define DCM_ParallelReductionFactorOutOfPlane    DcmTagKey(0x0018, 0x9155)
#define DCM_SpectroscopyAcquisitionOutOfPlanePhaseSteps DcmTagKey(0x0018, 0x9159)
#define DCM_RETIRED_BulkMotionStatus             DcmTagKey(0x0018, 0x9166)
#define DCM_ParallelReductionFactorSecondInPlane DcmTagKey(0x0018, 0x9168)
#define DCM_CardiacBeatRejectionTechnique        DcmTagKey(0x0018, 0x9169)
#define DCM_RespiratoryMotionCompensationTechnique DcmTagKey(0x0018, 0x9170)
#define DCM_RespiratorySignalSource              DcmTagKey(0x0018, 0x9171)
#define DCM_BulkMotionCompensationTechnique      DcmTagKey(0x0018, 0x9172)
#define DCM_BulkMotionSignalSource               DcmTagKey(0x0018, 0x9173)
#define DCM_ApplicableSafetyStandardAgency       DcmTagKey(0x0018, 0x9174)
#define DCM_ApplicableSafetyStandardDescription  DcmTagKey(0x0018, 0x9175)
#define DCM_OperatingModeSequence                DcmTagKey(0x0018, 0x9176)
#define DCM_OperatingModeType                    DcmTagKey(0x0018, 0x9177)
#define DCM_OperatingMode                        DcmTagKey(0x0018, 0x9178)
#define DCM_SpecificAbsorptionRateDefinition     DcmTagKey(0x0018, 0x9179)
#define DCM_GradientOutputType                   DcmTagKey(0x0018, 0x9180)
#define DCM_SpecificAbsorptionRateValue          DcmTagKey(0x0018, 0x9181)
#define DCM_GradientOutput                       DcmTagKey(0x0018, 0x9182)
#define DCM_FlowCompensationDirection            DcmTagKey(0x0018, 0x9183)
#define DCM_TaggingDelay                         DcmTagKey(0x0018, 0x9184)
#define DCM_RespiratoryMotionCompensationTechniqueDescription DcmTagKey(0x0018, 0x9185)
#define DCM_RespiratorySignalSourceID            DcmTagKey(0x0018, 0x9186)
#define DCM_RETIRED_ChemicalShiftMinimumIntegrationLimitInHz DcmTagKey(0x0018, 0x9195)
#define DCM_RETIRED_ChemicalShiftMaximumIntegrationLimitInHz DcmTagKey(0x0018, 0x9196)
#define DCM_MRVelocityEncodingSequence           DcmTagKey(0x0018, 0x9197)
#define DCM_FirstOrderPhaseCorrection            DcmTagKey(0x0018, 0x9198)
#define DCM_WaterReferencedPhaseCorrection       DcmTagKey(0x0018, 0x9199)
#define DCM_MRSpectroscopyAcquisitionType        DcmTagKey(0x0018, 0x9200)
#define DCM_RespiratoryCyclePosition             DcmTagKey(0x0018, 0x9214)
#define DCM_VelocityEncodingMaximumValue         DcmTagKey(0x0018, 0x9217)
#define DCM_TagSpacingSecondDimension            DcmTagKey(0x0018, 0x9218)
#define DCM_TagAngleSecondAxis                   DcmTagKey(0x0018, 0x9219)
#define DCM_FrameAcquisitionDuration             DcmTagKey(0x0018, 0x9220)
#define DCM_MRImageFrameTypeSequence             DcmTagKey(0x0018, 0x9226)
#define DCM_MRSpectroscopyFrameTypeSequence      DcmTagKey(0x0018, 0x9227)
#define DCM_MRAcquisitionPhaseEncodingStepsInPlane DcmTagKey(0x0018, 0x9231)
#define DCM_MRAcquisitionPhaseEncodingStepsOutOfPlane DcmTagKey(0x0018, 0x9232)
#define DCM_SpectroscopyAcquisitionPhaseColumns  DcmTagKey(0x0018, 0x9234)
#define DCM_CardiacCyclePosition                 DcmTagKey(0x0018, 0x9236)
#define DCM_SpecificAbsorptionRateSequence       DcmTagKey(0x0018, 0x9239)
#define DCM_RFEchoTrainLength                    DcmTagKey(0x0018, 0x9240)
#define DCM_GradientEchoTrainLength              DcmTagKey(0x0018, 0x9241)
#define DCM_ArterialSpinLabelingContrast         DcmTagKey(0x0018, 0x9250)
#define DCM_MRArterialSpinLabelingSequence       DcmTagKey(0x0018, 0x9251)
#define DCM_ASLTechniqueDescription              DcmTagKey(0x0018, 0x9252)
#define DCM_ASLSlabNumber                        DcmTagKey(0x0018, 0x9253)
#define DCM_ASLSlabThickness                     DcmTagKey(0x0018, 0x9254)
#define DCM_ASLSlabOrientation                   DcmTagKey(0x0018, 0x9255)
#define DCM_ASLMidSlabPosition                   DcmTagKey(0x0018, 0x9256)
#define DCM_ASLContext                           DcmTagKey(0x0018, 0x9257)
#define DCM_ASLPulseTrainDuration                DcmTagKey(0x0018, 0x9258)
#define DCM_ASLCrusherFlag                       DcmTagKey(0x0018, 0x9259)
#define DCM_ASLCrusherFlowLimit                  DcmTagKey(0x0018, 0x925a)
#define DCM_ASLCrusherDescription                DcmTagKey(0x0018, 0x925b)
#define DCM_ASLBolusCutoffFlag                   DcmTagKey(0x0018, 0x925c)
#define DCM_ASLBolusCutoffTimingSequence         DcmTagKey(0x0018, 0x925d)
#define DCM_ASLBolusCutoffTechnique              DcmTagKey(0x0018, 0x925e)
#define DCM_ASLBolusCutoffDelayTime              DcmTagKey(0x0018, 0x925f)
#define DCM_ASLSlabSequence                      DcmTagKey(0x0018, 0x9260)
#define DCM_ChemicalShiftMinimumIntegrationLimitInppm DcmTagKey(0x0018, 0x9295)
#define DCM_ChemicalShiftMaximumIntegrationLimitInppm DcmTagKey(0x0018, 0x9296)
#define DCM_WaterReferenceAcquisition            DcmTagKey(0x0018, 0x9297)
#define DCM_EchoPeakPosition                     DcmTagKey(0x0018, 0x9298)
#define DCM_CTAcquisitionTypeSequence            DcmTagKey(0x0018, 0x9301)
#define DCM_AcquisitionType                      DcmTagKey(0x0018, 0x9302)
#define DCM_TubeAngle                            DcmTagKey(0x0018, 0x9303)
#define DCM_CTAcquisitionDetailsSequence         DcmTagKey(0x0018, 0x9304)
#define DCM_RevolutionTime                       DcmTagKey(0x0018, 0x9305)
#define DCM_SingleCollimationWidth               DcmTagKey(0x0018, 0x9306)
#define DCM_TotalCollimationWidth                DcmTagKey(0x0018, 0x9307)
#define DCM_CTTableDynamicsSequence              DcmTagKey(0x0018, 0x9308)
#define DCM_TableSpeed                           DcmTagKey(0x0018, 0x9309)
#define DCM_TableFeedPerRotation                 DcmTagKey(0x0018, 0x9310)
#define DCM_SpiralPitchFactor                    DcmTagKey(0x0018, 0x9311)
#define DCM_CTGeometrySequence                   DcmTagKey(0x0018, 0x9312)
#define DCM_DataCollectionCenterPatient          DcmTagKey(0x0018, 0x9313)
#define DCM_CTReconstructionSequence             DcmTagKey(0x0018, 0x9314)
#define DCM_ReconstructionAlgorithm              DcmTagKey(0x0018, 0x9315)
#define DCM_ConvolutionKernelGroup               DcmTagKey(0x0018, 0x9316)
#define DCM_ReconstructionFieldOfView            DcmTagKey(0x0018, 0x9317)
#define DCM_ReconstructionTargetCenterPatient    DcmTagKey(0x0018, 0x9318)
#define DCM_ReconstructionAngle                  DcmTagKey(0x0018, 0x9319)
#define DCM_ImageFilter                          DcmTagKey(0x0018, 0x9320)
#define DCM_CTExposureSequence                   DcmTagKey(0x0018, 0x9321)
#define DCM_ReconstructionPixelSpacing           DcmTagKey(0x0018, 0x9322)
#define DCM_ExposureModulationType               DcmTagKey(0x0018, 0x9323)
#define DCM_RETIRED_EstimatedDoseSaving          DcmTagKey(0x0018, 0x9324)
#define DCM_CTXRayDetailsSequence                DcmTagKey(0x0018, 0x9325)
#define DCM_CTPositionSequence                   DcmTagKey(0x0018, 0x9326)
#define DCM_TablePosition                        DcmTagKey(0x0018, 0x9327)
#define DCM_ExposureTimeInms                     DcmTagKey(0x0018, 0x9328)
#define DCM_CTImageFrameTypeSequence             DcmTagKey(0x0018, 0x9329)
#define DCM_XRayTubeCurrentInmA                  DcmTagKey(0x0018, 0x9330)
#define DCM_ExposureInmAs                        DcmTagKey(0x0018, 0x9332)
#define DCM_ConstantVolumeFlag                   DcmTagKey(0x0018, 0x9333)
#define DCM_FluoroscopyFlag                      DcmTagKey(0x0018, 0x9334)
#define DCM_DistanceSourceToDataCollectionCenter DcmTagKey(0x0018, 0x9335)
#define DCM_ContrastBolusAgentNumber             DcmTagKey(0x0018, 0x9337)
#define DCM_ContrastBolusIngredientCodeSequence  DcmTagKey(0x0018, 0x9338)
#define DCM_ContrastAdministrationProfileSequence DcmTagKey(0x0018, 0x9340)
#define DCM_ContrastBolusUsageSequence           DcmTagKey(0x0018, 0x9341)
#define DCM_ContrastBolusAgentAdministered       DcmTagKey(0x0018, 0x9342)
#define DCM_ContrastBolusAgentDetected           DcmTagKey(0x0018, 0x9343)
#define DCM_ContrastBolusAgentPhase              DcmTagKey(0x0018, 0x9344)
#define DCM_CTDIvol                              DcmTagKey(0x0018, 0x9345)
#define DCM_CTDIPhantomTypeCodeSequence          DcmTagKey(0x0018, 0x9346)
#define DCM_CalciumScoringMassFactorPatient      DcmTagKey(0x0018, 0x9351)
#define DCM_CalciumScoringMassFactorDevice       DcmTagKey(0x0018, 0x9352)
#define DCM_EnergyWeightingFactor                DcmTagKey(0x0018, 0x9353)
#define DCM_CTAdditionalXRaySourceSequence       DcmTagKey(0x0018, 0x9360)
#define DCM_MultienergyCTAcquisition             DcmTagKey(0x0018, 0x9361)
#define DCM_MultienergyCTAcquisitionSequence     DcmTagKey(0x0018, 0x9362)
#define DCM_MultienergyCTProcessingSequence      DcmTagKey(0x0018, 0x9363)
#define DCM_MultienergyCTCharacteristicsSequence DcmTagKey(0x0018, 0x9364)
#define DCM_MultienergyCTXRaySourceSequence      DcmTagKey(0x0018, 0x9365)
#define DCM_XRaySourceIndex                      DcmTagKey(0x0018, 0x9366)
#define DCM_XRaySourceID                         DcmTagKey(0x0018, 0x9367)
#define DCM_MultienergySourceTechnique           DcmTagKey(0x0018, 0x9368)
#define DCM_SourceStartDateTime                  DcmTagKey(0x0018, 0x9369)
#define DCM_SourceEndDateTime                    DcmTagKey(0x0018, 0x936a)
#define DCM_SwitchingPhaseNumber                 DcmTagKey(0x0018, 0x936b)
#define DCM_SwitchingPhaseNominalDuration        DcmTagKey(0x0018, 0x936c)
#define DCM_SwitchingPhaseTransitionDuration     DcmTagKey(0x0018, 0x936d)
#define DCM_EffectiveBinEnergy                   DcmTagKey(0x0018, 0x936e)
#define DCM_MultienergyCTXRayDetectorSequence    DcmTagKey(0x0018, 0x936f)
#define DCM_XRayDetectorIndex                    DcmTagKey(0x0018, 0x9370)
#define DCM_XRayDetectorID                       DcmTagKey(0x0018, 0x9371)
#define DCM_MultienergyDetectorType              DcmTagKey(0x0018, 0x9372)
#define DCM_XRayDetectorLabel                    DcmTagKey(0x0018, 0x9373)
#define DCM_NominalMaxEnergy                     DcmTagKey(0x0018, 0x9374)
#define DCM_NominalMinEnergy                     DcmTagKey(0x0018, 0x9375)
#define DCM_ReferencedXRayDetectorIndex          DcmTagKey(0x0018, 0x9376)
#define DCM_ReferencedXRaySourceIndex            DcmTagKey(0x0018, 0x9377)
#define DCM_ReferencedPathIndex                  DcmTagKey(0x0018, 0x9378)
#define DCM_MultienergyCTPathSequence            DcmTagKey(0x0018, 0x9379)
#define DCM_MultienergyCTPathIndex               DcmTagKey(0x0018, 0x937a)
#define DCM_MultienergyAcquisitionDescription    DcmTagKey(0x0018, 0x937b)
#define DCM_MonoenergeticEnergyEquivalent        DcmTagKey(0x0018, 0x937c)
#define DCM_MaterialCodeSequence                 DcmTagKey(0x0018, 0x937d)
#define DCM_DecompositionMethod                  DcmTagKey(0x0018, 0x937e)
#define DCM_DecompositionDescription             DcmTagKey(0x0018, 0x937f)
#define DCM_DecompositionAlgorithmIdentificationSequence DcmTagKey(0x0018, 0x9380)
#define DCM_DecompositionMaterialSequence        DcmTagKey(0x0018, 0x9381)
#define DCM_MaterialAttenuationSequence          DcmTagKey(0x0018, 0x9382)
#define DCM_PhotonEnergy                         DcmTagKey(0x0018, 0x9383)
#define DCM_XRayMassAttenuationCoefficient       DcmTagKey(0x0018, 0x9384)
#define DCM_ProjectionPixelCalibrationSequence   DcmTagKey(0x0018, 0x9401)
#define DCM_DistanceSourceToIsocenter            DcmTagKey(0x0018, 0x9402)
#define DCM_DistanceObjectToTableTop             DcmTagKey(0x0018, 0x9403)
#define DCM_ObjectPixelSpacingInCenterOfBeam     DcmTagKey(0x0018, 0x9404)
#define DCM_PositionerPositionSequence           DcmTagKey(0x0018, 0x9405)
#define DCM_TablePositionSequence                DcmTagKey(0x0018, 0x9406)
#define DCM_CollimatorShapeSequence              DcmTagKey(0x0018, 0x9407)
#define DCM_PlanesInAcquisition                  DcmTagKey(0x0018, 0x9410)
#define DCM_XAXRFFrameCharacteristicsSequence    DcmTagKey(0x0018, 0x9412)
#define DCM_FrameAcquisitionSequence             DcmTagKey(0x0018, 0x9417)
#define DCM_XRayReceptorType                     DcmTagKey(0x0018, 0x9420)
#define DCM_AcquisitionProtocolName              DcmTagKey(0x0018, 0x9423)
#define DCM_AcquisitionProtocolDescription       DcmTagKey(0x0018, 0x9424)
#define DCM_ContrastBolusIngredientOpaque        DcmTagKey(0x0018, 0x9425)
#define DCM_DistanceReceptorPlaneToDetectorHousing DcmTagKey(0x0018, 0x9426)
#define DCM_IntensifierActiveShape               DcmTagKey(0x0018, 0x9427)
#define DCM_IntensifierActiveDimensions          DcmTagKey(0x0018, 0x9428)
#define DCM_PhysicalDetectorSize                 DcmTagKey(0x0018, 0x9429)
#define DCM_PositionOfIsocenterProjection        DcmTagKey(0x0018, 0x9430)
#define DCM_FieldOfViewSequence                  DcmTagKey(0x0018, 0x9432)
#define DCM_FieldOfViewDescription               DcmTagKey(0x0018, 0x9433)
#define DCM_ExposureControlSensingRegionsSequence DcmTagKey(0x0018, 0x9434)
#define DCM_ExposureControlSensingRegionShape    DcmTagKey(0x0018, 0x9435)
#define DCM_ExposureControlSensingRegionLeftVerticalEdge DcmTagKey(0x0018, 0x9436)
#define DCM_ExposureControlSensingRegionRightVerticalEdge DcmTagKey(0x0018, 0x9437)
#define DCM_ExposureControlSensingRegionUpperHorizontalEdge DcmTagKey(0x0018, 0x9438)
#define DCM_ExposureControlSensingRegionLowerHorizontalEdge DcmTagKey(0x0018, 0x9439)
#define DCM_CenterOfCircularExposureControlSensingRegion DcmTagKey(0x0018, 0x9440)
#define DCM_RadiusOfCircularExposureControlSensingRegion DcmTagKey(0x0018, 0x9441)
#define DCM_VerticesOfThePolygonalExposureControlSensingRegion DcmTagKey(0x0018, 0x9442)
#define DCM_ColumnAngulationPatient              DcmTagKey(0x0018, 0x9447)
#define DCM_BeamAngle                            DcmTagKey(0x0018, 0x9449)
#define DCM_FrameDetectorParametersSequence      DcmTagKey(0x0018, 0x9451)
#define DCM_CalculatedAnatomyThickness           DcmTagKey(0x0018, 0x9452)
#define DCM_CalibrationSequence                  DcmTagKey(0x0018, 0x9455)
#define DCM_ObjectThicknessSequence              DcmTagKey(0x0018, 0x9456)
#define DCM_PlaneIdentification                  DcmTagKey(0x0018, 0x9457)
#define DCM_FieldOfViewDimensionsInFloat         DcmTagKey(0x0018, 0x9461)
#define DCM_IsocenterReferenceSystemSequence     DcmTagKey(0x0018, 0x9462)
#define DCM_PositionerIsocenterPrimaryAngle      DcmTagKey(0x0018, 0x9463)
#define DCM_PositionerIsocenterSecondaryAngle    DcmTagKey(0x0018, 0x9464)
#define DCM_PositionerIsocenterDetectorRotationAngle DcmTagKey(0x0018, 0x9465)
#define DCM_TableXPositionToIsocenter            DcmTagKey(0x0018, 0x9466)
#define DCM_TableYPositionToIsocenter            DcmTagKey(0x0018, 0x9467)
#define DCM_TableZPositionToIsocenter            DcmTagKey(0x0018, 0x9468)
#define DCM_TableHorizontalRotationAngle         DcmTagKey(0x0018, 0x9469)
#define DCM_TableHeadTiltAngle                   DcmTagKey(0x0018, 0x9470)
#define DCM_TableCradleTiltAngle                 DcmTagKey(0x0018, 0x9471)
#define DCM_FrameDisplayShutterSequence          DcmTagKey(0x0018, 0x9472)
#define DCM_AcquiredImageAreaDoseProduct         DcmTagKey(0x0018, 0x9473)
#define DCM_CArmPositionerTabletopRelationship   DcmTagKey(0x0018, 0x9474)
#define DCM_XRayGeometrySequence                 DcmTagKey(0x0018, 0x9476)
#define DCM_IrradiationEventIdentificationSequence DcmTagKey(0x0018, 0x9477)
#define DCM_XRay3DFrameTypeSequence              DcmTagKey(0x0018, 0x9504)
#define DCM_ContributingSourcesSequence          DcmTagKey(0x0018, 0x9506)
#define DCM_XRay3DAcquisitionSequence            DcmTagKey(0x0018, 0x9507)
#define DCM_PrimaryPositionerScanArc             DcmTagKey(0x0018, 0x9508)
#define DCM_SecondaryPositionerScanArc           DcmTagKey(0x0018, 0x9509)
#define DCM_PrimaryPositionerScanStartAngle      DcmTagKey(0x0018, 0x9510)
#define DCM_SecondaryPositionerScanStartAngle    DcmTagKey(0x0018, 0x9511)
#define DCM_PrimaryPositionerIncrement           DcmTagKey(0x0018, 0x9514)
#define DCM_SecondaryPositionerIncrement         DcmTagKey(0x0018, 0x9515)
#define DCM_StartAcquisitionDateTime             DcmTagKey(0x0018, 0x9516)
#define DCM_EndAcquisitionDateTime               DcmTagKey(0x0018, 0x9517)
#define DCM_PrimaryPositionerIncrementSign       DcmTagKey(0x0018, 0x9518)
#define DCM_SecondaryPositionerIncrementSign     DcmTagKey(0x0018, 0x9519)
#define DCM_ApplicationName                      DcmTagKey(0x0018, 0x9524)
#define DCM_ApplicationVersion                   DcmTagKey(0x0018, 0x9525)
#define DCM_ApplicationManufacturer              DcmTagKey(0x0018, 0x9526)
#define DCM_AlgorithmType                        DcmTagKey(0x0018, 0x9527)
#define DCM_AlgorithmDescription                 DcmTagKey(0x0018, 0x9528)
#define DCM_XRay3DReconstructionSequence         DcmTagKey(0x0018, 0x9530)
#define DCM_ReconstructionDescription            DcmTagKey(0x0018, 0x9531)
#define DCM_PerProjectionAcquisitionSequence     DcmTagKey(0x0018, 0x9538)
#define DCM_DetectorPositionSequence             DcmTagKey(0x0018, 0x9541)
#define DCM_XRayAcquisitionDoseSequence          DcmTagKey(0x0018, 0x9542)
#define DCM_XRaySourceIsocenterPrimaryAngle      DcmTagKey(0x0018, 0x9543)
#define DCM_XRaySourceIsocenterSecondaryAngle    DcmTagKey(0x0018, 0x9544)
#define DCM_BreastSupportIsocenterPrimaryAngle   DcmTagKey(0x0018, 0x9545)
#define DCM_BreastSupportIsocenterSecondaryAngle DcmTagKey(0x0018, 0x9546)
#define DCM_BreastSupportXPositionToIsocenter    DcmTagKey(0x0018, 0x9547)
#define DCM_BreastSupportYPositionToIsocenter    DcmTagKey(0x0018, 0x9548)
#define DCM_BreastSupportZPositionToIsocenter    DcmTagKey(0x0018, 0x9549)
#define DCM_DetectorIsocenterPrimaryAngle        DcmTagKey(0x0018, 0x9550)
#define DCM_DetectorIsocenterSecondaryAngle      DcmTagKey(0x0018, 0x9551)
#define DCM_DetectorXPositionToIsocenter         DcmTagKey(0x0018, 0x9552)
#define DCM_DetectorYPositionToIsocenter         DcmTagKey(0x0018, 0x9553)
#define DCM_DetectorZPositionToIsocenter         DcmTagKey(0x0018, 0x9554)
#define DCM_XRayGridSequence                     DcmTagKey(0x0018, 0x9555)
#define DCM_XRayFilterSequence                   DcmTagKey(0x0018, 0x9556)
#define DCM_DetectorActiveAreaTLHCPosition       DcmTagKey(0x0018, 0x9557)
#define DCM_DetectorActiveAreaOrientation        DcmTagKey(0x0018, 0x9558)
#define DCM_PositionerPrimaryAngleDirection      DcmTagKey(0x0018, 0x9559)
#define DCM_DiffusionBMatrixSequence             DcmTagKey(0x0018, 0x9601)
#define DCM_DiffusionBValueXX                    DcmTagKey(0x0018, 0x9602)
#define DCM_DiffusionBValueXY                    DcmTagKey(0x0018, 0x9603)
#define DCM_DiffusionBValueXZ                    DcmTagKey(0x0018, 0x9604)
#define DCM_DiffusionBValueYY                    DcmTagKey(0x0018, 0x9605)
#define DCM_DiffusionBValueYZ                    DcmTagKey(0x0018, 0x9606)
#define DCM_DiffusionBValueZZ                    DcmTagKey(0x0018, 0x9607)
#define DCM_FunctionalMRSequence                 DcmTagKey(0x0018, 0x9621)
#define DCM_FunctionalSettlingPhaseFramesPresent DcmTagKey(0x0018, 0x9622)
#define DCM_FunctionalSyncPulse                  DcmTagKey(0x0018, 0x9623)
#define DCM_SettlingPhaseFrame                   DcmTagKey(0x0018, 0x9624)
#define DCM_DecayCorrectionDateTime              DcmTagKey(0x0018, 0x9701)
#define DCM_StartDensityThreshold                DcmTagKey(0x0018, 0x9715)
#define DCM_StartRelativeDensityDifferenceThreshold DcmTagKey(0x0018, 0x9716)
#define DCM_StartCardiacTriggerCountThreshold    DcmTagKey(0x0018, 0x9717)
#define DCM_StartRespiratoryTriggerCountThreshold DcmTagKey(0x0018, 0x9718)
#define DCM_TerminationCountsThreshold           DcmTagKey(0x0018, 0x9719)
#define DCM_TerminationDensityThreshold          DcmTagKey(0x0018, 0x9720)
#define DCM_TerminationRelativeDensityThreshold  DcmTagKey(0x0018, 0x9721)
#define DCM_TerminationTimeThreshold             DcmTagKey(0x0018, 0x9722)
#define DCM_TerminationCardiacTriggerCountThreshold DcmTagKey(0x0018, 0x9723)
#define DCM_TerminationRespiratoryTriggerCountThreshold DcmTagKey(0x0018, 0x9724)
#define DCM_DetectorGeometry                     DcmTagKey(0x0018, 0x9725)
#define DCM_TransverseDetectorSeparation         DcmTagKey(0x0018, 0x9726)
#define DCM_AxialDetectorDimension               DcmTagKey(0x0018, 0x9727)
#define DCM_RadiopharmaceuticalAgentNumber       DcmTagKey(0x0018, 0x9729)
#define DCM_PETFrameAcquisitionSequence          DcmTagKey(0x0018, 0x9732)
#define DCM_PETDetectorMotionDetailsSequence     DcmTagKey(0x0018, 0x9733)
#define DCM_PETTableDynamicsSequence             DcmTagKey(0x0018, 0x9734)
#define DCM_PETPositionSequence                  DcmTagKey(0x0018, 0x9735)
#define DCM_PETFrameCorrectionFactorsSequence    DcmTagKey(0x0018, 0x9736)
#define DCM_RadiopharmaceuticalUsageSequence     DcmTagKey(0x0018, 0x9737)
#define DCM_AttenuationCorrectionSource          DcmTagKey(0x0018, 0x9738)
#define DCM_NumberOfIterations                   DcmTagKey(0x0018, 0x9739)
#define DCM_NumberOfSubsets                      DcmTagKey(0x0018, 0x9740)
#define DCM_PETReconstructionSequence            DcmTagKey(0x0018, 0x9749)
#define DCM_PETFrameTypeSequence                 DcmTagKey(0x0018, 0x9751)
#define DCM_TimeOfFlightInformationUsed          DcmTagKey(0x0018, 0x9755)
#define DCM_ReconstructionType                   DcmTagKey(0x0018, 0x9756)
#define DCM_DecayCorrected                       DcmTagKey(0x0018, 0x9758)
#define DCM_AttenuationCorrected                 DcmTagKey(0x0018, 0x9759)
#define DCM_ScatterCorrected                     DcmTagKey(0x0018, 0x9760)
#define DCM_DeadTimeCorrected                    DcmTagKey(0x0018, 0x9761)
#define DCM_GantryMotionCorrected                DcmTagKey(0x0018, 0x9762)
#define DCM_PatientMotionCorrected               DcmTagKey(0x0018, 0x9763)
#define DCM_CountLossNormalizationCorrected      DcmTagKey(0x0018, 0x9764)
#define DCM_RandomsCorrected                     DcmTagKey(0x0018, 0x9765)
#define DCM_NonUniformRadialSamplingCorrected    DcmTagKey(0x0018, 0x9766)
#define DCM_SensitivityCalibrated                DcmTagKey(0x0018, 0x9767)
#define DCM_DetectorNormalizationCorrection      DcmTagKey(0x0018, 0x9768)
#define DCM_IterativeReconstructionMethod        DcmTagKey(0x0018, 0x9769)
#define DCM_AttenuationCorrectionTemporalRelationship DcmTagKey(0x0018, 0x9770)
#define DCM_PatientPhysiologicalStateSequence    DcmTagKey(0x0018, 0x9771)
#define DCM_PatientPhysiologicalStateCodeSequence DcmTagKey(0x0018, 0x9772)
#define DCM_DepthsOfFocus                        DcmTagKey(0x0018, 0x9801)
#define DCM_ExcludedIntervalsSequence            DcmTagKey(0x0018, 0x9803)
#define DCM_ExclusionStartDateTime               DcmTagKey(0x0018, 0x9804)
#define DCM_ExclusionDuration                    DcmTagKey(0x0018, 0x9805)
#define DCM_USImageDescriptionSequence           DcmTagKey(0x0018, 0x9806)
#define DCM_ImageDataTypeSequence                DcmTagKey(0x0018, 0x9807)
#define DCM_DataType                             DcmTagKey(0x0018, 0x9808)
#define DCM_TransducerScanPatternCodeSequence    DcmTagKey(0x0018, 0x9809)
#define DCM_AliasedDataType                      DcmTagKey(0x0018, 0x980b)
#define DCM_PositionMeasuringDeviceUsed          DcmTagKey(0x0018, 0x980c)
#define DCM_TransducerGeometryCodeSequence       DcmTagKey(0x0018, 0x980d)
#define DCM_TransducerBeamSteeringCodeSequence   DcmTagKey(0x0018, 0x980e)
#define DCM_TransducerApplicationCodeSequence    DcmTagKey(0x0018, 0x980f)
#define DCM_ZeroVelocityPixelValue               DcmTagKey(0x0018, 0x9810)
#define DCM_PhotoacousticExcitationCharacteristicsSequence DcmTagKey(0x0018, 0x9821)
#define DCM_ExcitationSpectralWidth              DcmTagKey(0x0018, 0x9822)
#define DCM_ExcitationEnergy                     DcmTagKey(0x0018, 0x9823)
#define DCM_ExcitationPulseDuration              DcmTagKey(0x0018, 0x9824)
#define DCM_ExcitationWavelengthSequence         DcmTagKey(0x0018, 0x9825)
#define DCM_ExcitationWavelength                 DcmTagKey(0x0018, 0x9826)
#define DCM_IlluminationTranslationFlag          DcmTagKey(0x0018, 0x9828)
#define DCM_AcousticCouplingMediumFlag           DcmTagKey(0x0018, 0x9829)
#define DCM_AcousticCouplingMediumCodeSequence   DcmTagKey(0x0018, 0x982a)
#define DCM_AcousticCouplingMediumTemperature    DcmTagKey(0x0018, 0x982b)
#define DCM_TransducerResponseSequence           DcmTagKey(0x0018, 0x982c)
#define DCM_CenterFrequency                      DcmTagKey(0x0018, 0x982d)
#define DCM_FractionalBandwidth                  DcmTagKey(0x0018, 0x982e)
#define DCM_LowerCutoffFrequency                 DcmTagKey(0x0018, 0x982f)
#define DCM_UpperCutoffFrequency                 DcmTagKey(0x0018, 0x9830)
#define DCM_TransducerTechnologySequence         DcmTagKey(0x0018, 0x9831)
#define DCM_SoundSpeedCorrectionMechanismCodeSequence DcmTagKey(0x0018, 0x9832)
#define DCM_ObjectSoundSpeed                     DcmTagKey(0x0018, 0x9833)
#define DCM_AcousticCouplingMediumSoundSpeed     DcmTagKey(0x0018, 0x9834)
#define DCM_PhotoacousticImageFrameTypeSequence  DcmTagKey(0x0018, 0x9835)
#define DCM_ImageDataTypeCodeSequence            DcmTagKey(0x0018, 0x9836)
#define DCM_ReferenceLocationLabel               DcmTagKey(0x0018, 0x9900)
#define DCM_ReferenceLocationDescription         DcmTagKey(0x0018, 0x9901)
#define DCM_ReferenceBasisCodeSequence           DcmTagKey(0x0018, 0x9902)
#define DCM_ReferenceGeometryCodeSequence        DcmTagKey(0x0018, 0x9903)
#define DCM_OffsetDistance                       DcmTagKey(0x0018, 0x9904)
#define DCM_OffsetDirection                      DcmTagKey(0x0018, 0x9905)
#define DCM_PotentialScheduledProtocolCodeSequence DcmTagKey(0x0018, 0x9906)
#define DCM_PotentialRequestedProcedureCodeSequence DcmTagKey(0x0018, 0x9907)
#define DCM_PotentialReasonsForProcedure         DcmTagKey(0x0018, 0x9908)
#define DCM_PotentialReasonsForProcedureCodeSequence DcmTagKey(0x0018, 0x9909)
#define DCM_PotentialDiagnosticTasks             DcmTagKey(0x0018, 0x990a)
#define DCM_ContraindicationsCodeSequence        DcmTagKey(0x0018, 0x990b)
#define DCM_ReferencedDefinedProtocolSequence    DcmTagKey(0x0018, 0x990c)
#define DCM_ReferencedPerformedProtocolSequence  DcmTagKey(0x0018, 0x990d)
#define DCM_PredecessorProtocolSequence          DcmTagKey(0x0018, 0x990e)
#define DCM_ProtocolPlanningInformation          DcmTagKey(0x0018, 0x990f)
#define DCM_ProtocolDesignRationale              DcmTagKey(0x0018, 0x9910)
#define DCM_PatientSpecificationSequence         DcmTagKey(0x0018, 0x9911)
#define DCM_ModelSpecificationSequence           DcmTagKey(0x0018, 0x9912)
#define DCM_ParametersSpecificationSequence      DcmTagKey(0x0018, 0x9913)
#define DCM_InstructionSequence                  DcmTagKey(0x0018, 0x9914)
#define DCM_InstructionIndex                     DcmTagKey(0x0018, 0x9915)
#define DCM_InstructionText                      DcmTagKey(0x0018, 0x9916)
#define DCM_InstructionDescription               DcmTagKey(0x0018, 0x9917)
#define DCM_InstructionPerformedFlag             DcmTagKey(0x0018, 0x9918)
#define DCM_InstructionPerformedDateTime         DcmTagKey(0x0018, 0x9919)
#define DCM_InstructionPerformanceComment        DcmTagKey(0x0018, 0x991a)
#define DCM_PatientPositioningInstructionSequence DcmTagKey(0x0018, 0x991b)
#define DCM_PositioningMethodCodeSequence        DcmTagKey(0x0018, 0x991c)
#define DCM_PositioningLandmarkSequence          DcmTagKey(0x0018, 0x991d)
#define DCM_TargetFrameOfReferenceUID            DcmTagKey(0x0018, 0x991e)
#define DCM_AcquisitionProtocolElementSpecificationSequence DcmTagKey(0x0018, 0x991f)
#define DCM_AcquisitionProtocolElementSequence   DcmTagKey(0x0018, 0x9920)
#define DCM_ProtocolElementNumber                DcmTagKey(0x0018, 0x9921)
#define DCM_ProtocolElementName                  DcmTagKey(0x0018, 0x9922)
#define DCM_ProtocolElementCharacteristicsSummary DcmTagKey(0x0018, 0x9923)
#define DCM_ProtocolElementPurpose               DcmTagKey(0x0018, 0x9924)
#define DCM_AcquisitionMotion                    DcmTagKey(0x0018, 0x9930)
#define DCM_AcquisitionStartLocationSequence     DcmTagKey(0x0018, 0x9931)
#define DCM_AcquisitionEndLocationSequence       DcmTagKey(0x0018, 0x9932)
#define DCM_ReconstructionProtocolElementSpecificationSequence DcmTagKey(0x0018, 0x9933)
#define DCM_ReconstructionProtocolElementSequence DcmTagKey(0x0018, 0x9934)
#define DCM_StorageProtocolElementSpecificationSequence DcmTagKey(0x0018, 0x9935)
#define DCM_StorageProtocolElementSequence       DcmTagKey(0x0018, 0x9936)
#define DCM_RequestedSeriesDescription           DcmTagKey(0x0018, 0x9937)
#define DCM_SourceAcquisitionProtocolElementNumber DcmTagKey(0x0018, 0x9938)
#define DCM_SourceAcquisitionBeamNumber          DcmTagKey(0x0018, 0x9939)
#define DCM_SourceReconstructionProtocolElementNumber DcmTagKey(0x0018, 0x993a)
#define DCM_ReconstructionStartLocationSequence  DcmTagKey(0x0018, 0x993b)
#define DCM_ReconstructionEndLocationSequence    DcmTagKey(0x0018, 0x993c)
#define DCM_ReconstructionAlgorithmSequence      DcmTagKey(0x0018, 0x993d)
#define DCM_ReconstructionTargetCenterLocationSequence DcmTagKey(0x0018, 0x993e)
#define DCM_ImageFilterDescription               DcmTagKey(0x0018, 0x9941)
#define DCM_CTDIvolNotificationTrigger           DcmTagKey(0x0018, 0x9942)
#define DCM_DLPNotificationTrigger               DcmTagKey(0x0018, 0x9943)
#define DCM_AutoKVPSelectionType                 DcmTagKey(0x0018, 0x9944)
#define DCM_AutoKVPUpperBound                    DcmTagKey(0x0018, 0x9945)
#define DCM_AutoKVPLowerBound                    DcmTagKey(0x0018, 0x9946)
#define DCM_ProtocolDefinedPatientPosition       DcmTagKey(0x0018, 0x9947)
#define DCM_ContributingEquipmentSequence        DcmTagKey(0x0018, 0xa001)
#define DCM_ContributionDateTime                 DcmTagKey(0x0018, 0xa002)
#define DCM_ContributionDescription              DcmTagKey(0x0018, 0xa003)
#define DCM_StudyInstanceUID                     DcmTagKey(0x0020, 0x000d)
#define DCM_SeriesInstanceUID                    DcmTagKey(0x0020, 0x000e)
#define DCM_StudyID                              DcmTagKey(0x0020, 0x0010)
#define DCM_SeriesNumber                         DcmTagKey(0x0020, 0x0011)
#define DCM_AcquisitionNumber                    DcmTagKey(0x0020, 0x0012)
#define DCM_InstanceNumber                       DcmTagKey(0x0020, 0x0013)
#define DCM_RETIRED_IsotopeNumber                DcmTagKey(0x0020, 0x0014)
#define DCM_RETIRED_PhaseNumber                  DcmTagKey(0x0020, 0x0015)
#define DCM_RETIRED_IntervalNumber               DcmTagKey(0x0020, 0x0016)
#define DCM_RETIRED_TimeSlotNumber               DcmTagKey(0x0020, 0x0017)
#define DCM_RETIRED_AngleNumber                  DcmTagKey(0x0020, 0x0018)
#define DCM_ItemNumber                           DcmTagKey(0x0020, 0x0019)
#define DCM_PatientOrientation                   DcmTagKey(0x0020, 0x0020)
#define DCM_RETIRED_OverlayNumber                DcmTagKey(0x0020, 0x0022)
#define DCM_RETIRED_CurveNumber                  DcmTagKey(0x0020, 0x0024)
#define DCM_RETIRED_LUTNumber                    DcmTagKey(0x0020, 0x0026)
#define DCM_PyramidLabel                         DcmTagKey(0x0020, 0x0027)
#define DCM_RETIRED_ImagePosition                DcmTagKey(0x0020, 0x0030)
#define DCM_ImagePositionPatient                 DcmTagKey(0x0020, 0x0032)
#define DCM_RETIRED_ImageOrientation             DcmTagKey(0x0020, 0x0035)
#define DCM_ImageOrientationPatient              DcmTagKey(0x0020, 0x0037)
#define DCM_RETIRED_Location                     DcmTagKey(0x0020, 0x0050)
#define DCM_FrameOfReferenceUID                  DcmTagKey(0x0020, 0x0052)
#define DCM_Laterality                           DcmTagKey(0x0020, 0x0060)
#define DCM_ImageLaterality                      DcmTagKey(0x0020, 0x0062)
#define DCM_RETIRED_ImageGeometryType            DcmTagKey(0x0020, 0x0070)
#define DCM_RETIRED_MaskingImage                 DcmTagKey(0x0020, 0x0080)
#define DCM_RETIRED_ReportNumber                 DcmTagKey(0x0020, 0x00aa)
#define DCM_TemporalPositionIdentifier           DcmTagKey(0x0020, 0x0100)
#define DCM_NumberOfTemporalPositions            DcmTagKey(0x0020, 0x0105)
#define DCM_TemporalResolution                   DcmTagKey(0x0020, 0x0110)
#define DCM_SynchronizationFrameOfReferenceUID   DcmTagKey(0x0020, 0x0200)
#define DCM_SOPInstanceUIDOfConcatenationSource  DcmTagKey(0x0020, 0x0242)
#define DCM_RETIRED_SeriesInStudy                DcmTagKey(0x0020, 0x1000)
#define DCM_RETIRED_AcquisitionsInSeries         DcmTagKey(0x0020, 0x1001)
#define DCM_ImagesInAcquisition                  DcmTagKey(0x0020, 0x1002)
#define DCM_RETIRED_ImagesInSeries               DcmTagKey(0x0020, 0x1003)
#define DCM_RETIRED_AcquisitionsInStudy          DcmTagKey(0x0020, 0x1004)
#define DCM_RETIRED_ImagesInStudy                DcmTagKey(0x0020, 0x1005)
#define DCM_RETIRED_Reference                    DcmTagKey(0x0020, 0x1020)
#define DCM_TargetPositionReferenceIndicator     DcmTagKey(0x0020, 0x103f)
#define DCM_PositionReferenceIndicator           DcmTagKey(0x0020, 0x1040)
#define DCM_SliceLocation                        DcmTagKey(0x0020, 0x1041)
#define DCM_RETIRED_OtherStudyNumbers            DcmTagKey(0x0020, 0x1070)
#define DCM_NumberOfPatientRelatedStudies        DcmTagKey(0x0020, 0x1200)
#define DCM_NumberOfPatientRelatedSeries         DcmTagKey(0x0020, 0x1202)
#define DCM_NumberOfPatientRelatedInstances      DcmTagKey(0x0020, 0x1204)
#define DCM_NumberOfStudyRelatedSeries           DcmTagKey(0x0020, 0x1206)
#define DCM_NumberOfStudyRelatedInstances        DcmTagKey(0x0020, 0x1208)
#define DCM_NumberOfSeriesRelatedInstances       DcmTagKey(0x0020, 0x1209)
#define DCM_RETIRED_ModifyingDeviceID            DcmTagKey(0x0020, 0x3401)
#define DCM_RETIRED_ModifiedImageID              DcmTagKey(0x0020, 0x3402)
#define DCM_RETIRED_ModifiedImageDate            DcmTagKey(0x0020, 0x3403)
#define DCM_RETIRED_ModifyingDeviceManufacturer  DcmTagKey(0x0020, 0x3404)
#define DCM_RETIRED_ModifiedImageTime            DcmTagKey(0x0020, 0x3405)
#define DCM_RETIRED_ModifiedImageDescription     DcmTagKey(0x0020, 0x3406)
#define DCM_ImageComments                        DcmTagKey(0x0020, 0x4000)
#define DCM_RETIRED_OriginalImageIdentification  DcmTagKey(0x0020, 0x5000)
#define DCM_RETIRED_OriginalImageIdentificationNomenclature DcmTagKey(0x0020, 0x5002)
#define DCM_StackID                              DcmTagKey(0x0020, 0x9056)
#define DCM_InStackPositionNumber                DcmTagKey(0x0020, 0x9057)
#define DCM_FrameAnatomySequence                 DcmTagKey(0x0020, 0x9071)
#define DCM_FrameLaterality                      DcmTagKey(0x0020, 0x9072)
#define DCM_FrameContentSequence                 DcmTagKey(0x0020, 0x9111)
#define DCM_PlanePositionSequence                DcmTagKey(0x0020, 0x9113)
#define DCM_PlaneOrientationSequence             DcmTagKey(0x0020, 0x9116)
#define DCM_TemporalPositionIndex                DcmTagKey(0x0020, 0x9128)
#define DCM_NominalCardiacTriggerDelayTime       DcmTagKey(0x0020, 0x9153)
#define DCM_NominalCardiacTriggerTimePriorToRPeak DcmTagKey(0x0020, 0x9154)
#define DCM_ActualCardiacTriggerTimePriorToRPeak DcmTagKey(0x0020, 0x9155)
#define DCM_FrameAcquisitionNumber               DcmTagKey(0x0020, 0x9156)
#define DCM_DimensionIndexValues                 DcmTagKey(0x0020, 0x9157)
#define DCM_FrameComments                        DcmTagKey(0x0020, 0x9158)
#define DCM_ConcatenationUID                     DcmTagKey(0x0020, 0x9161)
#define DCM_InConcatenationNumber                DcmTagKey(0x0020, 0x9162)
#define DCM_InConcatenationTotalNumber           DcmTagKey(0x0020, 0x9163)
#define DCM_DimensionOrganizationUID             DcmTagKey(0x0020, 0x9164)
#define DCM_DimensionIndexPointer                DcmTagKey(0x0020, 0x9165)
#define DCM_FunctionalGroupPointer               DcmTagKey(0x0020, 0x9167)
#define DCM_UnassignedSharedConvertedAttributesSequence DcmTagKey(0x0020, 0x9170)
#define DCM_UnassignedPerFrameConvertedAttributesSequence DcmTagKey(0x0020, 0x9171)
#define DCM_ConversionSourceAttributesSequence   DcmTagKey(0x0020, 0x9172)
#define DCM_DimensionIndexPrivateCreator         DcmTagKey(0x0020, 0x9213)
#define DCM_DimensionOrganizationSequence        DcmTagKey(0x0020, 0x9221)
#define DCM_DimensionIndexSequence               DcmTagKey(0x0020, 0x9222)
#define DCM_ConcatenationFrameOffsetNumber       DcmTagKey(0x0020, 0x9228)
#define DCM_FunctionalGroupPrivateCreator        DcmTagKey(0x0020, 0x9238)
#define DCM_NominalPercentageOfCardiacPhase      DcmTagKey(0x0020, 0x9241)
#define DCM_NominalPercentageOfRespiratoryPhase  DcmTagKey(0x0020, 0x9245)
#define DCM_StartingRespiratoryAmplitude         DcmTagKey(0x0020, 0x9246)
#define DCM_StartingRespiratoryPhase             DcmTagKey(0x0020, 0x9247)
#define DCM_EndingRespiratoryAmplitude           DcmTagKey(0x0020, 0x9248)
#define DCM_EndingRespiratoryPhase               DcmTagKey(0x0020, 0x9249)
#define DCM_RespiratoryTriggerType               DcmTagKey(0x0020, 0x9250)
#define DCM_RRIntervalTimeNominal                DcmTagKey(0x0020, 0x9251)
#define DCM_ActualCardiacTriggerDelayTime        DcmTagKey(0x0020, 0x9252)
#define DCM_RespiratorySynchronizationSequence   DcmTagKey(0x0020, 0x9253)
#define DCM_RespiratoryIntervalTime              DcmTagKey(0x0020, 0x9254)
#define DCM_NominalRespiratoryTriggerDelayTime   DcmTagKey(0x0020, 0x9255)
#define DCM_RespiratoryTriggerDelayThreshold     DcmTagKey(0x0020, 0x9256)
#define DCM_ActualRespiratoryTriggerDelayTime    DcmTagKey(0x0020, 0x9257)
#define DCM_ImagePositionVolume                  DcmTagKey(0x0020, 0x9301)
#define DCM_ImageOrientationVolume               DcmTagKey(0x0020, 0x9302)
#define DCM_UltrasoundAcquisitionGeometry        DcmTagKey(0x0020, 0x9307)
#define DCM_ApexPosition                         DcmTagKey(0x0020, 0x9308)
#define DCM_VolumeToTransducerMappingMatrix      DcmTagKey(0x0020, 0x9309)
#define DCM_VolumeToTableMappingMatrix           DcmTagKey(0x0020, 0x930a)
#define DCM_VolumeToTransducerRelationship       DcmTagKey(0x0020, 0x930b)
#define DCM_PatientFrameOfReferenceSource        DcmTagKey(0x0020, 0x930c)
#define DCM_TemporalPositionTimeOffset           DcmTagKey(0x0020, 0x930d)
#define DCM_PlanePositionVolumeSequence          DcmTagKey(0x0020, 0x930e)
#define DCM_PlaneOrientationVolumeSequence       DcmTagKey(0x0020, 0x930f)
#define DCM_TemporalPositionSequence             DcmTagKey(0x0020, 0x9310)
#define DCM_DimensionOrganizationType            DcmTagKey(0x0020, 0x9311)
#define DCM_VolumeFrameOfReferenceUID            DcmTagKey(0x0020, 0x9312)
#define DCM_TableFrameOfReferenceUID             DcmTagKey(0x0020, 0x9313)
#define DCM_DimensionDescriptionLabel            DcmTagKey(0x0020, 0x9421)
#define DCM_PatientOrientationInFrameSequence    DcmTagKey(0x0020, 0x9450)
#define DCM_FrameLabel                           DcmTagKey(0x0020, 0x9453)
#define DCM_AcquisitionIndex                     DcmTagKey(0x0020, 0x9518)
#define DCM_ContributingSOPInstancesReferenceSequence DcmTagKey(0x0020, 0x9529)
#define DCM_ReconstructionIndex                  DcmTagKey(0x0020, 0x9536)
#define DCM_LightPathFilterPassThroughWavelength DcmTagKey(0x0022, 0x0001)
#define DCM_LightPathFilterPassBand              DcmTagKey(0x0022, 0x0002)
#define DCM_ImagePathFilterPassThroughWavelength DcmTagKey(0x0022, 0x0003)
#define DCM_ImagePathFilterPassBand              DcmTagKey(0x0022, 0x0004)
#define DCM_PatientEyeMovementCommanded          DcmTagKey(0x0022, 0x0005)
#define DCM_PatientEyeMovementCommandCodeSequence DcmTagKey(0x0022, 0x0006)
#define DCM_SphericalLensPower                   DcmTagKey(0x0022, 0x0007)
#define DCM_CylinderLensPower                    DcmTagKey(0x0022, 0x0008)
#define DCM_CylinderAxis                         DcmTagKey(0x0022, 0x0009)
#define DCM_EmmetropicMagnification              DcmTagKey(0x0022, 0x000a)
#define DCM_IntraOcularPressure                  DcmTagKey(0x0022, 0x000b)
#define DCM_HorizontalFieldOfView                DcmTagKey(0x0022, 0x000c)
#define DCM_PupilDilated                         DcmTagKey(0x0022, 0x000d)
#define DCM_DegreeOfDilation                     DcmTagKey(0x0022, 0x000e)
#define DCM_VertexDistance                       DcmTagKey(0x0022, 0x000f)
#define DCM_StereoBaselineAngle                  DcmTagKey(0x0022, 0x0010)
#define DCM_StereoBaselineDisplacement           DcmTagKey(0x0022, 0x0011)
#define DCM_StereoHorizontalPixelOffset          DcmTagKey(0x0022, 0x0012)
#define DCM_StereoVerticalPixelOffset            DcmTagKey(0x0022, 0x0013)
#define DCM_StereoRotation                       DcmTagKey(0x0022, 0x0014)
#define DCM_AcquisitionDeviceTypeCodeSequence    DcmTagKey(0x0022, 0x0015)
#define DCM_IlluminationTypeCodeSequence         DcmTagKey(0x0022, 0x0016)
#define DCM_LightPathFilterTypeStackCodeSequence DcmTagKey(0x0022, 0x0017)
#define DCM_ImagePathFilterTypeStackCodeSequence DcmTagKey(0x0022, 0x0018)
#define DCM_LensesCodeSequence                   DcmTagKey(0x0022, 0x0019)
#define DCM_ChannelDescriptionCodeSequence       DcmTagKey(0x0022, 0x001a)
#define DCM_RefractiveStateSequence              DcmTagKey(0x0022, 0x001b)
#define DCM_MydriaticAgentCodeSequence           DcmTagKey(0x0022, 0x001c)
#define DCM_RelativeImagePositionCodeSequence    DcmTagKey(0x0022, 0x001d)
#define DCM_CameraAngleOfView                    DcmTagKey(0x0022, 0x001e)
#define DCM_StereoPairsSequence                  DcmTagKey(0x0022, 0x0020)
#define DCM_LeftImageSequence                    DcmTagKey(0x0022, 0x0021)
#define DCM_RightImageSequence                   DcmTagKey(0x0022, 0x0022)
#define DCM_StereoPairsPresent                   DcmTagKey(0x0022, 0x0028)
#define DCM_AxialLengthOfTheEye                  DcmTagKey(0x0022, 0x0030)
#define DCM_OphthalmicFrameLocationSequence      DcmTagKey(0x0022, 0x0031)
#define DCM_ReferenceCoordinates                 DcmTagKey(0x0022, 0x0032)
#define DCM_DepthSpatialResolution               DcmTagKey(0x0022, 0x0035)
#define DCM_MaximumDepthDistortion               DcmTagKey(0x0022, 0x0036)
#define DCM_AlongScanSpatialResolution           DcmTagKey(0x0022, 0x0037)
#define DCM_MaximumAlongScanDistortion           DcmTagKey(0x0022, 0x0038)
#define DCM_OphthalmicImageOrientation           DcmTagKey(0x0022, 0x0039)
#define DCM_DepthOfTransverseImage               DcmTagKey(0x0022, 0x0041)
#define DCM_MydriaticAgentConcentrationUnitsSequence DcmTagKey(0x0022, 0x0042)
#define DCM_AcrossScanSpatialResolution          DcmTagKey(0x0022, 0x0048)
#define DCM_MaximumAcrossScanDistortion          DcmTagKey(0x0022, 0x0049)
#define DCM_MydriaticAgentConcentration          DcmTagKey(0x0022, 0x004e)
#define DCM_IlluminationWaveLength               DcmTagKey(0x0022, 0x0055)
#define DCM_IlluminationPower                    DcmTagKey(0x0022, 0x0056)
#define DCM_IlluminationBandwidth                DcmTagKey(0x0022, 0x0057)
#define DCM_MydriaticAgentSequence               DcmTagKey(0x0022, 0x0058)
#define DCM_OphthalmicAxialMeasurementsRightEyeSequence DcmTagKey(0x0022, 0x1007)
#define DCM_OphthalmicAxialMeasurementsLeftEyeSequence DcmTagKey(0x0022, 0x1008)
#define DCM_OphthalmicAxialMeasurementsDeviceType DcmTagKey(0x0022, 0x1009)
#define DCM_OphthalmicAxialLengthMeasurementsType DcmTagKey(0x0022, 0x1010)
#define DCM_OphthalmicAxialLengthSequence        DcmTagKey(0x0022, 0x1012)
#define DCM_OphthalmicAxialLength                DcmTagKey(0x0022, 0x1019)
#define DCM_LensStatusCodeSequence               DcmTagKey(0x0022, 0x1024)
#define DCM_VitreousStatusCodeSequence           DcmTagKey(0x0022, 0x1025)
#define DCM_IOLFormulaCodeSequence               DcmTagKey(0x0022, 0x1028)
#define DCM_IOLFormulaDetail                     DcmTagKey(0x0022, 0x1029)
#define DCM_KeratometerIndex                     DcmTagKey(0x0022, 0x1033)
#define DCM_SourceOfOphthalmicAxialLengthCodeSequence DcmTagKey(0x0022, 0x1035)
#define DCM_SourceOfCornealSizeDataCodeSequence  DcmTagKey(0x0022, 0x1036)
#define DCM_TargetRefraction                     DcmTagKey(0x0022, 0x1037)
#define DCM_RefractiveProcedureOccurred          DcmTagKey(0x0022, 0x1039)
#define DCM_RefractiveSurgeryTypeCodeSequence    DcmTagKey(0x0022, 0x1040)
#define DCM_OphthalmicUltrasoundMethodCodeSequence DcmTagKey(0x0022, 0x1044)
#define DCM_SurgicallyInducedAstigmatismSequence DcmTagKey(0x0022, 0x1045)
#define DCM_TypeOfOpticalCorrection              DcmTagKey(0x0022, 0x1046)
#define DCM_ToricIOLPowerSequence                DcmTagKey(0x0022, 0x1047)
#define DCM_PredictedToricErrorSequence          DcmTagKey(0x0022, 0x1048)
#define DCM_PreSelectedForImplantation           DcmTagKey(0x0022, 0x1049)
#define DCM_ToricIOLPowerForExactEmmetropiaSequence DcmTagKey(0x0022, 0x104a)
#define DCM_ToricIOLPowerForExactTargetRefractionSequence DcmTagKey(0x0022, 0x104b)
#define DCM_OphthalmicAxialLengthMeasurementsSequence DcmTagKey(0x0022, 0x1050)
#define DCM_IOLPower                             DcmTagKey(0x0022, 0x1053)
#define DCM_PredictedRefractiveError             DcmTagKey(0x0022, 0x1054)
#define DCM_OphthalmicAxialLengthVelocity        DcmTagKey(0x0022, 0x1059)
#define DCM_LensStatusDescription                DcmTagKey(0x0022, 0x1065)
#define DCM_VitreousStatusDescription            DcmTagKey(0x0022, 0x1066)
#define DCM_IOLPowerSequence                     DcmTagKey(0x0022, 0x1090)
#define DCM_LensConstantSequence                 DcmTagKey(0x0022, 0x1092)
#define DCM_IOLManufacturer                      DcmTagKey(0x0022, 0x1093)
#define DCM_RETIRED_LensConstantDescription      DcmTagKey(0x0022, 0x1094)
#define DCM_ImplantName                          DcmTagKey(0x0022, 0x1095)
#define DCM_KeratometryMeasurementTypeCodeSequence DcmTagKey(0x0022, 0x1096)
#define DCM_ImplantPartNumber                    DcmTagKey(0x0022, 0x1097)
#define DCM_ReferencedOphthalmicAxialMeasurementsSequence DcmTagKey(0x0022, 0x1100)
#define DCM_OphthalmicAxialLengthMeasurementsSegmentNameCodeSequence DcmTagKey(0x0022, 0x1101)
#define DCM_RefractiveErrorBeforeRefractiveSurgeryCodeSequence DcmTagKey(0x0022, 0x1103)
#define DCM_IOLPowerForExactEmmetropia           DcmTagKey(0x0022, 0x1121)
#define DCM_IOLPowerForExactTargetRefraction     DcmTagKey(0x0022, 0x1122)
#define DCM_AnteriorChamberDepthDefinitionCodeSequence DcmTagKey(0x0022, 0x1125)
#define DCM_LensThicknessSequence                DcmTagKey(0x0022, 0x1127)
#define DCM_AnteriorChamberDepthSequence         DcmTagKey(0x0022, 0x1128)
#define DCM_CalculationCommentSequence           DcmTagKey(0x0022, 0x112a)
#define DCM_CalculationCommentType               DcmTagKey(0x0022, 0x112b)
#define DCM_CalculationComment                   DcmTagKey(0x0022, 0x112c)
#define DCM_LensThickness                        DcmTagKey(0x0022, 0x1130)
#define DCM_AnteriorChamberDepth                 DcmTagKey(0x0022, 0x1131)
#define DCM_SourceOfLensThicknessDataCodeSequence DcmTagKey(0x0022, 0x1132)
#define DCM_SourceOfAnteriorChamberDepthDataCodeSequence DcmTagKey(0x0022, 0x1133)
#define DCM_SourceOfRefractiveMeasurementsSequence DcmTagKey(0x0022, 0x1134)
#define DCM_SourceOfRefractiveMeasurementsCodeSequence DcmTagKey(0x0022, 0x1135)
#define DCM_OphthalmicAxialLengthMeasurementModified DcmTagKey(0x0022, 0x1140)
#define DCM_OphthalmicAxialLengthDataSourceCodeSequence DcmTagKey(0x0022, 0x1150)
#define DCM_RETIRED_OphthalmicAxialLengthAcquisitionMethodCodeSequence DcmTagKey(0x0022, 0x1153)
#define DCM_SignalToNoiseRatio                   DcmTagKey(0x0022, 0x1155)
#define DCM_OphthalmicAxialLengthDataSourceDescription DcmTagKey(0x0022, 0x1159)
#define DCM_OphthalmicAxialLengthMeasurementsTotalLengthSequence DcmTagKey(0x0022, 0x1210)
#define DCM_OphthalmicAxialLengthMeasurementsSegmentalLengthSequence DcmTagKey(0x0022, 0x1211)
#define DCM_OphthalmicAxialLengthMeasurementsLengthSummationSequence DcmTagKey(0x0022, 0x1212)
#define DCM_UltrasoundOphthalmicAxialLengthMeasurementsSequence DcmTagKey(0x0022, 0x1220)
#define DCM_OpticalOphthalmicAxialLengthMeasurementsSequence DcmTagKey(0x0022, 0x1225)
#define DCM_UltrasoundSelectedOphthalmicAxialLengthSequence DcmTagKey(0x0022, 0x1230)
#define DCM_OphthalmicAxialLengthSelectionMethodCodeSequence DcmTagKey(0x0022, 0x1250)
#define DCM_OpticalSelectedOphthalmicAxialLengthSequence DcmTagKey(0x0022, 0x1255)
#define DCM_SelectedSegmentalOphthalmicAxialLengthSequence DcmTagKey(0x0022, 0x1257)
#define DCM_SelectedTotalOphthalmicAxialLengthSequence DcmTagKey(0x0022, 0x1260)
#define DCM_OphthalmicAxialLengthQualityMetricSequence DcmTagKey(0x0022, 0x1262)
#define DCM_RETIRED_OphthalmicAxialLengthQualityMetricTypeCodeSequence DcmTagKey(0x0022, 0x1265)
#define DCM_RETIRED_OphthalmicAxialLengthQualityMetricTypeDescription DcmTagKey(0x0022, 0x1273)
#define DCM_IntraocularLensCalculationsRightEyeSequence DcmTagKey(0x0022, 0x1300)
#define DCM_IntraocularLensCalculationsLeftEyeSequence DcmTagKey(0x0022, 0x1310)
#define DCM_ReferencedOphthalmicAxialLengthMeasurementQCImageSequence DcmTagKey(0x0022, 0x1330)
#define DCM_OphthalmicMappingDeviceType          DcmTagKey(0x0022, 0x1415)
#define DCM_AcquisitionMethodCodeSequence        DcmTagKey(0x0022, 0x1420)
#define DCM_AcquisitionMethodAlgorithmSequence   DcmTagKey(0x0022, 0x1423)
#define DCM_OphthalmicThicknessMapTypeCodeSequence DcmTagKey(0x0022, 0x1436)
#define DCM_OphthalmicThicknessMappingNormalsSequence DcmTagKey(0x0022, 0x1443)
#define DCM_RetinalThicknessDefinitionCodeSequence DcmTagKey(0x0022, 0x1445)
#define DCM_PixelValueMappingToCodedConceptSequence DcmTagKey(0x0022, 0x1450)
#define DCM_MappedPixelValue                     DcmTagKey(0x0022, 0x1452)
#define DCM_PixelValueMappingExplanation         DcmTagKey(0x0022, 0x1454)
#define DCM_OphthalmicThicknessMapQualityThresholdSequence DcmTagKey(0x0022, 0x1458)
#define DCM_OphthalmicThicknessMapThresholdQualityRating DcmTagKey(0x0022, 0x1460)
#define DCM_AnatomicStructureReferencePoint      DcmTagKey(0x0022, 0x1463)
#define DCM_RegistrationToLocalizerSequence      DcmTagKey(0x0022, 0x1465)
#define DCM_RegisteredLocalizerUnits             DcmTagKey(0x0022, 0x1466)
#define DCM_RegisteredLocalizerTopLeftHandCorner DcmTagKey(0x0022, 0x1467)
#define DCM_RegisteredLocalizerBottomRightHandCorner DcmTagKey(0x0022, 0x1468)
#define DCM_OphthalmicThicknessMapQualityRatingSequence DcmTagKey(0x0022, 0x1470)
#define DCM_RelevantOPTAttributesSequence        DcmTagKey(0x0022, 0x1472)
#define DCM_TransformationMethodCodeSequence     DcmTagKey(0x0022, 0x1512)
#define DCM_TransformationAlgorithmSequence      DcmTagKey(0x0022, 0x1513)
#define DCM_OphthalmicAxialLengthMethod          DcmTagKey(0x0022, 0x1515)
#define DCM_OphthalmicFOV                        DcmTagKey(0x0022, 0x1517)
#define DCM_TwoDimensionalToThreeDimensionalMapSequence DcmTagKey(0x0022, 0x1518)
#define DCM_WideFieldOphthalmicPhotographyQualityRatingSequence DcmTagKey(0x0022, 0x1525)
#define DCM_WideFieldOphthalmicPhotographyQualityThresholdSequence DcmTagKey(0x0022, 0x1526)
#define DCM_WideFieldOphthalmicPhotographyThresholdQualityRating DcmTagKey(0x0022, 0x1527)
#define DCM_XCoordinatesCenterPixelViewAngle     DcmTagKey(0x0022, 0x1528)
#define DCM_YCoordinatesCenterPixelViewAngle     DcmTagKey(0x0022, 0x1529)
#define DCM_NumberOfMapPoints                    DcmTagKey(0x0022, 0x1530)
#define DCM_TwoDimensionalToThreeDimensionalMapData DcmTagKey(0x0022, 0x1531)
#define DCM_DerivationAlgorithmSequence          DcmTagKey(0x0022, 0x1612)
#define DCM_OphthalmicImageTypeCodeSequence      DcmTagKey(0x0022, 0x1615)
#define DCM_OphthalmicImageTypeDescription       DcmTagKey(0x0022, 0x1616)
#define DCM_ScanPatternTypeCodeSequence          DcmTagKey(0x0022, 0x1618)
#define DCM_ReferencedSurfaceMeshIdentificationSequence DcmTagKey(0x0022, 0x1620)
#define DCM_OphthalmicVolumetricPropertiesFlag   DcmTagKey(0x0022, 0x1622)
#define DCM_OphthalmicAnatomicReferencePointFrameCoordinate DcmTagKey(0x0022, 0x1623)
#define DCM_OphthalmicAnatomicReferencePointXCoordinate DcmTagKey(0x0022, 0x1624)
#define DCM_OphthalmicAnatomicReferencePointYCoordinate DcmTagKey(0x0022, 0x1626)
#define DCM_OphthalmicEnFaceVolumeDescriptorSequence DcmTagKey(0x0022, 0x1627)
#define DCM_OphthalmicEnFaceImageQualityRatingSequence DcmTagKey(0x0022, 0x1628)
#define DCM_OphthalmicEnFaceVolumeDescriptorScope DcmTagKey(0x0022, 0x1629)
#define DCM_QualityThreshold                     DcmTagKey(0x0022, 0x1630)
#define DCM_OphthalmicAnatomicReferencePointSequence DcmTagKey(0x0022, 0x1632)
#define DCM_OphthalmicAnatomicReferencePointLocalizationType DcmTagKey(0x0022, 0x1633)
#define DCM_PrimaryAnatomicStructureItemIndex    DcmTagKey(0x0022, 0x1634)
#define DCM_OCTBscanAnalysisAcquisitionParametersSequence DcmTagKey(0x0022, 0x1640)
#define DCM_NumberOfBscansPerFrame               DcmTagKey(0x0022, 0x1642)
#define DCM_BscanSlabThickness                   DcmTagKey(0x0022, 0x1643)
#define DCM_DistanceBetweenBscanSlabs            DcmTagKey(0x0022, 0x1644)
#define DCM_BscanCycleTime                       DcmTagKey(0x0022, 0x1645)
#define DCM_BscanCycleTimeVector                 DcmTagKey(0x0022, 0x1646)
#define DCM_AscanRate                            DcmTagKey(0x0022, 0x1649)
#define DCM_BscanRate                            DcmTagKey(0x0022, 0x1650)
#define DCM_SurfaceMeshZPixelOffset              DcmTagKey(0x0022, 0x1658)
#define DCM_VisualFieldHorizontalExtent          DcmTagKey(0x0024, 0x0010)
#define DCM_VisualFieldVerticalExtent            DcmTagKey(0x0024, 0x0011)
#define DCM_VisualFieldShape                     DcmTagKey(0x0024, 0x0012)
#define DCM_ScreeningTestModeCodeSequence        DcmTagKey(0x0024, 0x0016)
#define DCM_MaximumStimulusLuminance             DcmTagKey(0x0024, 0x0018)
#define DCM_BackgroundLuminance                  DcmTagKey(0x0024, 0x0020)
#define DCM_StimulusColorCodeSequence            DcmTagKey(0x0024, 0x0021)
#define DCM_BackgroundIlluminationColorCodeSequence DcmTagKey(0x0024, 0x0024)
#define DCM_StimulusArea                         DcmTagKey(0x0024, 0x0025)
#define DCM_StimulusPresentationTime             DcmTagKey(0x0024, 0x0028)
#define DCM_FixationSequence                     DcmTagKey(0x0024, 0x0032)
#define DCM_FixationMonitoringCodeSequence       DcmTagKey(0x0024, 0x0033)
#define DCM_VisualFieldCatchTrialSequence        DcmTagKey(0x0024, 0x0034)
#define DCM_FixationCheckedQuantity              DcmTagKey(0x0024, 0x0035)
#define DCM_PatientNotProperlyFixatedQuantity    DcmTagKey(0x0024, 0x0036)
#define DCM_PresentedVisualStimuliDataFlag       DcmTagKey(0x0024, 0x0037)
#define DCM_NumberOfVisualStimuli                DcmTagKey(0x0024, 0x0038)
#define DCM_ExcessiveFixationLossesDataFlag      DcmTagKey(0x0024, 0x0039)
#define DCM_ExcessiveFixationLosses              DcmTagKey(0x0024, 0x0040)
#define DCM_StimuliRetestingQuantity             DcmTagKey(0x0024, 0x0042)
#define DCM_CommentsOnPatientPerformanceOfVisualField DcmTagKey(0x0024, 0x0044)
#define DCM_FalseNegativesEstimateFlag           DcmTagKey(0x0024, 0x0045)
#define DCM_FalseNegativesEstimate               DcmTagKey(0x0024, 0x0046)
#define DCM_NegativeCatchTrialsQuantity          DcmTagKey(0x0024, 0x0048)
#define DCM_FalseNegativesQuantity               DcmTagKey(0x0024, 0x0050)
#define DCM_ExcessiveFalseNegativesDataFlag      DcmTagKey(0x0024, 0x0051)
#define DCM_ExcessiveFalseNegatives              DcmTagKey(0x0024, 0x0052)
#define DCM_FalsePositivesEstimateFlag           DcmTagKey(0x0024, 0x0053)
#define DCM_FalsePositivesEstimate               DcmTagKey(0x0024, 0x0054)
#define DCM_CatchTrialsDataFlag                  DcmTagKey(0x0024, 0x0055)
#define DCM_PositiveCatchTrialsQuantity          DcmTagKey(0x0024, 0x0056)
#define DCM_TestPointNormalsDataFlag             DcmTagKey(0x0024, 0x0057)
#define DCM_TestPointNormalsSequence             DcmTagKey(0x0024, 0x0058)
#define DCM_GlobalDeviationProbabilityNormalsFlag DcmTagKey(0x0024, 0x0059)
#define DCM_FalsePositivesQuantity               DcmTagKey(0x0024, 0x0060)
#define DCM_ExcessiveFalsePositivesDataFlag      DcmTagKey(0x0024, 0x0061)
#define DCM_ExcessiveFalsePositives              DcmTagKey(0x0024, 0x0062)
#define DCM_VisualFieldTestNormalsFlag           DcmTagKey(0x0024, 0x0063)
#define DCM_ResultsNormalsSequence               DcmTagKey(0x0024, 0x0064)
#define DCM_AgeCorrectedSensitivityDeviationAlgorithmSequence DcmTagKey(0x0024, 0x0065)
#define DCM_GlobalDeviationFromNormal            DcmTagKey(0x0024, 0x0066)
#define DCM_GeneralizedDefectSensitivityDeviationAlgorithmSequence DcmTagKey(0x0024, 0x0067)
#define DCM_LocalizedDeviationFromNormal         DcmTagKey(0x0024, 0x0068)
#define DCM_PatientReliabilityIndicator          DcmTagKey(0x0024, 0x0069)
#define DCM_VisualFieldMeanSensitivity           DcmTagKey(0x0024, 0x0070)
#define DCM_GlobalDeviationProbability           DcmTagKey(0x0024, 0x0071)
#define DCM_LocalDeviationProbabilityNormalsFlag DcmTagKey(0x0024, 0x0072)
#define DCM_LocalizedDeviationProbability        DcmTagKey(0x0024, 0x0073)
#define DCM_ShortTermFluctuationCalculated       DcmTagKey(0x0024, 0x0074)
#define DCM_ShortTermFluctuation                 DcmTagKey(0x0024, 0x0075)
#define DCM_ShortTermFluctuationProbabilityCalculated DcmTagKey(0x0024, 0x0076)
#define DCM_ShortTermFluctuationProbability      DcmTagKey(0x0024, 0x0077)
#define DCM_CorrectedLocalizedDeviationFromNormalCalculated DcmTagKey(0x0024, 0x0078)
#define DCM_CorrectedLocalizedDeviationFromNormal DcmTagKey(0x0024, 0x0079)
#define DCM_CorrectedLocalizedDeviationFromNormalProbabilityCalculated DcmTagKey(0x0024, 0x0080)
#define DCM_CorrectedLocalizedDeviationFromNormalProbability DcmTagKey(0x0024, 0x0081)
#define DCM_GlobalDeviationProbabilitySequence   DcmTagKey(0x0024, 0x0083)
#define DCM_LocalizedDeviationProbabilitySequence DcmTagKey(0x0024, 0x0085)
#define DCM_FovealSensitivityMeasured            DcmTagKey(0x0024, 0x0086)
#define DCM_FovealSensitivity                    DcmTagKey(0x0024, 0x0087)
#define DCM_VisualFieldTestDuration              DcmTagKey(0x0024, 0x0088)
#define DCM_VisualFieldTestPointSequence         DcmTagKey(0x0024, 0x0089)
#define DCM_VisualFieldTestPointXCoordinate      DcmTagKey(0x0024, 0x0090)
#define DCM_VisualFieldTestPointYCoordinate      DcmTagKey(0x0024, 0x0091)
#define DCM_AgeCorrectedSensitivityDeviationValue DcmTagKey(0x0024, 0x0092)
#define DCM_StimulusResults                      DcmTagKey(0x0024, 0x0093)
#define DCM_SensitivityValue                     DcmTagKey(0x0024, 0x0094)
#define DCM_RetestStimulusSeen                   DcmTagKey(0x0024, 0x0095)
#define DCM_RetestSensitivityValue               DcmTagKey(0x0024, 0x0096)
#define DCM_VisualFieldTestPointNormalsSequence  DcmTagKey(0x0024, 0x0097)
#define DCM_QuantifiedDefect                     DcmTagKey(0x0024, 0x0098)
#define DCM_AgeCorrectedSensitivityDeviationProbabilityValue DcmTagKey(0x0024, 0x0100)
#define DCM_GeneralizedDefectCorrectedSensitivityDeviationFlag DcmTagKey(0x0024, 0x0102)
#define DCM_GeneralizedDefectCorrectedSensitivityDeviationValue DcmTagKey(0x0024, 0x0103)
#define DCM_GeneralizedDefectCorrectedSensitivityDeviationProbabilityValue DcmTagKey(0x0024, 0x0104)
#define DCM_MinimumSensitivityValue              DcmTagKey(0x0024, 0x0105)
#define DCM_BlindSpotLocalized                   DcmTagKey(0x0024, 0x0106)
#define DCM_BlindSpotXCoordinate                 DcmTagKey(0x0024, 0x0107)
#define DCM_BlindSpotYCoordinate                 DcmTagKey(0x0024, 0x0108)
#define DCM_VisualAcuityMeasurementSequence      DcmTagKey(0x0024, 0x0110)
#define DCM_RefractiveParametersUsedOnPatientSequence DcmTagKey(0x0024, 0x0112)
#define DCM_MeasurementLaterality                DcmTagKey(0x0024, 0x0113)
#define DCM_OphthalmicPatientClinicalInformationLeftEyeSequence DcmTagKey(0x0024, 0x0114)
#define DCM_OphthalmicPatientClinicalInformationRightEyeSequence DcmTagKey(0x0024, 0x0115)
#define DCM_FovealPointNormativeDataFlag         DcmTagKey(0x0024, 0x0117)
#define DCM_FovealPointProbabilityValue          DcmTagKey(0x0024, 0x0118)
#define DCM_ScreeningBaselineMeasured            DcmTagKey(0x0024, 0x0120)
#define DCM_ScreeningBaselineMeasuredSequence    DcmTagKey(0x0024, 0x0122)
#define DCM_ScreeningBaselineType                DcmTagKey(0x0024, 0x0124)
#define DCM_ScreeningBaselineValue               DcmTagKey(0x0024, 0x0126)
#define DCM_AlgorithmSource                      DcmTagKey(0x0024, 0x0202)
#define DCM_DataSetName                          DcmTagKey(0x0024, 0x0306)
#define DCM_DataSetVersion                       DcmTagKey(0x0024, 0x0307)
#define DCM_DataSetSource                        DcmTagKey(0x0024, 0x0308)
#define DCM_DataSetDescription                   DcmTagKey(0x0024, 0x0309)
#define DCM_VisualFieldTestReliabilityGlobalIndexSequence DcmTagKey(0x0024, 0x0317)
#define DCM_VisualFieldGlobalResultsIndexSequence DcmTagKey(0x0024, 0x0320)
#define DCM_DataObservationSequence              DcmTagKey(0x0024, 0x0325)
#define DCM_IndexNormalsFlag                     DcmTagKey(0x0024, 0x0338)
#define DCM_IndexProbability                     DcmTagKey(0x0024, 0x0341)
#define DCM_IndexProbabilitySequence             DcmTagKey(0x0024, 0x0344)
#define DCM_SamplesPerPixel                      DcmTagKey(0x0028, 0x0002)
#define DCM_SamplesPerPixelUsed                  DcmTagKey(0x0028, 0x0003)
#define DCM_PhotometricInterpretation            DcmTagKey(0x0028, 0x0004)
#define DCM_RETIRED_ImageDimensions              DcmTagKey(0x0028, 0x0005)
#define DCM_PlanarConfiguration                  DcmTagKey(0x0028, 0x0006)
#define DCM_NumberOfFrames                       DcmTagKey(0x0028, 0x0008)
#define DCM_FrameIncrementPointer                DcmTagKey(0x0028, 0x0009)
#define DCM_FrameDimensionPointer                DcmTagKey(0x0028, 0x000a)
#define DCM_Rows                                 DcmTagKey(0x0028, 0x0010)
#define DCM_Columns                              DcmTagKey(0x0028, 0x0011)
#define DCM_RETIRED_Planes                       DcmTagKey(0x0028, 0x0012)
#define DCM_UltrasoundColorDataPresent           DcmTagKey(0x0028, 0x0014)
#define DCM_PixelSpacing                         DcmTagKey(0x0028, 0x0030)
#define DCM_ZoomFactor                           DcmTagKey(0x0028, 0x0031)
#define DCM_ZoomCenter                           DcmTagKey(0x0028, 0x0032)
#define DCM_PixelAspectRatio                     DcmTagKey(0x0028, 0x0034)
#define DCM_RETIRED_ImageFormat                  DcmTagKey(0x0028, 0x0040)
#define DCM_RETIRED_ManipulatedImage             DcmTagKey(0x0028, 0x0050)
#define DCM_CorrectedImage                       DcmTagKey(0x0028, 0x0051)
#define DCM_RETIRED_CompressionRecognitionCode   DcmTagKey(0x0028, 0x005f)
#define DCM_RETIRED_CompressionCode              DcmTagKey(0x0028, 0x0060)
#define DCM_RETIRED_CompressionOriginator        DcmTagKey(0x0028, 0x0061)
#define DCM_RETIRED_CompressionLabel             DcmTagKey(0x0028, 0x0062)
#define DCM_RETIRED_CompressionDescription       DcmTagKey(0x0028, 0x0063)
#define DCM_RETIRED_CompressionSequence          DcmTagKey(0x0028, 0x0065)
#define DCM_RETIRED_CompressionStepPointers      DcmTagKey(0x0028, 0x0066)
#define DCM_RETIRED_RepeatInterval               DcmTagKey(0x0028, 0x0068)
#define DCM_RETIRED_BitsGrouped                  DcmTagKey(0x0028, 0x0069)
#define DCM_RETIRED_PerimeterTable               DcmTagKey(0x0028, 0x0070)
#define DCM_RETIRED_PerimeterValue               DcmTagKey(0x0028, 0x0071)
#define DCM_RETIRED_PredictorRows                DcmTagKey(0x0028, 0x0080)
#define DCM_RETIRED_PredictorColumns             DcmTagKey(0x0028, 0x0081)
#define DCM_RETIRED_PredictorConstants           DcmTagKey(0x0028, 0x0082)
#define DCM_RETIRED_BlockedPixels                DcmTagKey(0x0028, 0x0090)
#define DCM_RETIRED_BlockRows                    DcmTagKey(0x0028, 0x0091)
#define DCM_RETIRED_BlockColumns                 DcmTagKey(0x0028, 0x0092)
#define DCM_RETIRED_RowOverlap                   DcmTagKey(0x0028, 0x0093)
#define DCM_RETIRED_ColumnOverlap                DcmTagKey(0x0028, 0x0094)
#define DCM_BitsAllocated                        DcmTagKey(0x0028, 0x0100)
#define DCM_BitsStored                           DcmTagKey(0x0028, 0x0101)
#define DCM_HighBit                              DcmTagKey(0x0028, 0x0102)
#define DCM_PixelRepresentation                  DcmTagKey(0x0028, 0x0103)
#define DCM_RETIRED_SmallestValidPixelValue      DcmTagKey(0x0028, 0x0104)
#define DCM_RETIRED_LargestValidPixelValue       DcmTagKey(0x0028, 0x0105)
#define DCM_SmallestImagePixelValue              DcmTagKey(0x0028, 0x0106)
#define DCM_LargestImagePixelValue               DcmTagKey(0x0028, 0x0107)
#define DCM_SmallestPixelValueInSeries           DcmTagKey(0x0028, 0x0108)
#define DCM_LargestPixelValueInSeries            DcmTagKey(0x0028, 0x0109)
#define DCM_RETIRED_SmallestImagePixelValueInPlane DcmTagKey(0x0028, 0x0110)
#define DCM_RETIRED_LargestImagePixelValueInPlane DcmTagKey(0x0028, 0x0111)
#define DCM_PixelPaddingValue                    DcmTagKey(0x0028, 0x0120)
#define DCM_PixelPaddingRangeLimit               DcmTagKey(0x0028, 0x0121)
#define DCM_FloatPixelPaddingValue               DcmTagKey(0x0028, 0x0122)
#define DCM_DoubleFloatPixelPaddingValue         DcmTagKey(0x0028, 0x0123)
#define DCM_FloatPixelPaddingRangeLimit          DcmTagKey(0x0028, 0x0124)
#define DCM_DoubleFloatPixelPaddingRangeLimit    DcmTagKey(0x0028, 0x0125)
#define DCM_RETIRED_ImageLocation                DcmTagKey(0x0028, 0x0200)
#define DCM_QualityControlImage                  DcmTagKey(0x0028, 0x0300)
#define DCM_BurnedInAnnotation                   DcmTagKey(0x0028, 0x0301)
#define DCM_RecognizableVisualFeatures           DcmTagKey(0x0028, 0x0302)
#define DCM_LongitudinalTemporalInformationModified DcmTagKey(0x0028, 0x0303)
#define DCM_ReferencedColorPaletteInstanceUID    DcmTagKey(0x0028, 0x0304)
#define DCM_RETIRED_TransformLabel               DcmTagKey(0x0028, 0x0400)
#define DCM_RETIRED_TransformVersionNumber       DcmTagKey(0x0028, 0x0401)
#define DCM_RETIRED_NumberOfTransformSteps       DcmTagKey(0x0028, 0x0402)
#define DCM_RETIRED_SequenceOfCompressedData     DcmTagKey(0x0028, 0x0403)
#define DCM_RETIRED_DetailsOfCoefficients        DcmTagKey(0x0028, 0x0404)
#define DCM_RETIRED_RowsForNthOrderCoefficients  DcmTagKey(0x0028, 0x0410)
#define DCM_RETIRED_ColumnsForNthOrderCoefficients DcmTagKey(0x0028, 0x0411)
#define DCM_RETIRED_CoefficientCoding            DcmTagKey(0x0028, 0x0412)
#define DCM_RETIRED_CoefficientCodingPointers    DcmTagKey(0x0028, 0x0413)
#define DCM_RETIRED_DCTLabel                     DcmTagKey(0x0028, 0x0700)
#define DCM_RETIRED_DataBlockDescription         DcmTagKey(0x0028, 0x0701)
#define DCM_RETIRED_DataBlock                    DcmTagKey(0x0028, 0x0702)
#define DCM_RETIRED_NormalizationFactorFormat    DcmTagKey(0x0028, 0x0710)
#define DCM_RETIRED_ZonalMapNumberFormat         DcmTagKey(0x0028, 0x0720)
#define DCM_RETIRED_ZonalMapLocation             DcmTagKey(0x0028, 0x0721)
#define DCM_RETIRED_ZonalMapFormat               DcmTagKey(0x0028, 0x0722)
#define DCM_RETIRED_AdaptiveMapFormat            DcmTagKey(0x0028, 0x0730)
#define DCM_RETIRED_CodeNumberFormat             DcmTagKey(0x0028, 0x0740)
#define DCM_RETIRED_CodeLabel                    DcmTagKey(0x0028, 0x0800)
#define DCM_RETIRED_NumberOfTables               DcmTagKey(0x0028, 0x0802)
#define DCM_RETIRED_CodeTableLocation            DcmTagKey(0x0028, 0x0803)
#define DCM_RETIRED_BitsForCodeWord              DcmTagKey(0x0028, 0x0804)
#define DCM_RETIRED_ImageDataLocation            DcmTagKey(0x0028, 0x0808)
#define DCM_PixelSpacingCalibrationType          DcmTagKey(0x0028, 0x0a02)
#define DCM_PixelSpacingCalibrationDescription   DcmTagKey(0x0028, 0x0a04)
#define DCM_PixelIntensityRelationship           DcmTagKey(0x0028, 0x1040)
#define DCM_PixelIntensityRelationshipSign       DcmTagKey(0x0028, 0x1041)
#define DCM_WindowCenter                         DcmTagKey(0x0028, 0x1050)
#define DCM_WindowWidth                          DcmTagKey(0x0028, 0x1051)
#define DCM_RescaleIntercept                     DcmTagKey(0x0028, 0x1052)
#define DCM_RescaleSlope                         DcmTagKey(0x0028, 0x1053)
#define DCM_RescaleType                          DcmTagKey(0x0028, 0x1054)
#define DCM_WindowCenterWidthExplanation         DcmTagKey(0x0028, 0x1055)
#define DCM_VOILUTFunction                       DcmTagKey(0x0028, 0x1056)
#define DCM_RETIRED_GrayScale                    DcmTagKey(0x0028, 0x1080)
#define DCM_RecommendedViewingMode               DcmTagKey(0x0028, 0x1090)
#define DCM_RETIRED_GrayLookupTableDescriptor    DcmTagKey(0x0028, 0x1100)
#define DCM_RedPaletteColorLookupTableDescriptor DcmTagKey(0x0028, 0x1101)
#define DCM_GreenPaletteColorLookupTableDescriptor DcmTagKey(0x0028, 0x1102)
#define DCM_BluePaletteColorLookupTableDescriptor DcmTagKey(0x0028, 0x1103)
#define DCM_AlphaPaletteColorLookupTableDescriptor DcmTagKey(0x0028, 0x1104)
#define DCM_RETIRED_LargeRedPaletteColorLookupTableDescriptor DcmTagKey(0x0028, 0x1111)
#define DCM_RETIRED_LargeGreenPaletteColorLookupTableDescriptor DcmTagKey(0x0028, 0x1112)
#define DCM_RETIRED_LargeBluePaletteColorLookupTableDescriptor DcmTagKey(0x0028, 0x1113)
#define DCM_PaletteColorLookupTableUID           DcmTagKey(0x0028, 0x1199)
#define DCM_RETIRED_GrayLookupTableData          DcmTagKey(0x0028, 0x1200)
#define DCM_RedPaletteColorLookupTableData       DcmTagKey(0x0028, 0x1201)
#define DCM_GreenPaletteColorLookupTableData     DcmTagKey(0x0028, 0x1202)
#define DCM_BluePaletteColorLookupTableData      DcmTagKey(0x0028, 0x1203)
#define DCM_AlphaPaletteColorLookupTableData     DcmTagKey(0x0028, 0x1204)
#define DCM_RETIRED_LargeRedPaletteColorLookupTableData DcmTagKey(0x0028, 0x1211)
#define DCM_RETIRED_LargeGreenPaletteColorLookupTableData DcmTagKey(0x0028, 0x1212)
#define DCM_RETIRED_LargeBluePaletteColorLookupTableData DcmTagKey(0x0028, 0x1213)
#define DCM_RETIRED_LargePaletteColorLookupTableUID DcmTagKey(0x0028, 0x1214)
#define DCM_SegmentedRedPaletteColorLookupTableData DcmTagKey(0x0028, 0x1221)
#define DCM_SegmentedGreenPaletteColorLookupTableData DcmTagKey(0x0028, 0x1222)
#define DCM_SegmentedBluePaletteColorLookupTableData DcmTagKey(0x0028, 0x1223)
#define DCM_SegmentedAlphaPaletteColorLookupTableData DcmTagKey(0x0028, 0x1224)
#define DCM_StoredValueColorRangeSequence        DcmTagKey(0x0028, 0x1230)
#define DCM_MinimumStoredValueMapped             DcmTagKey(0x0028, 0x1231)
#define DCM_MaximumStoredValueMapped             DcmTagKey(0x0028, 0x1232)
#define DCM_BreastImplantPresent                 DcmTagKey(0x0028, 0x1300)
#define DCM_PartialView                          DcmTagKey(0x0028, 0x1350)
#define DCM_PartialViewDescription               DcmTagKey(0x0028, 0x1351)
#define DCM_PartialViewCodeSequence              DcmTagKey(0x0028, 0x1352)
#define DCM_SpatialLocationsPreserved            DcmTagKey(0x0028, 0x135a)
#define DCM_DataFrameAssignmentSequence          DcmTagKey(0x0028, 0x1401)
#define DCM_DataPathAssignment                   DcmTagKey(0x0028, 0x1402)
#define DCM_BitsMappedToColorLookupTable         DcmTagKey(0x0028, 0x1403)
#define DCM_BlendingLUT1Sequence                 DcmTagKey(0x0028, 0x1404)
#define DCM_BlendingLUT1TransferFunction         DcmTagKey(0x0028, 0x1405)
#define DCM_BlendingWeightConstant               DcmTagKey(0x0028, 0x1406)
#define DCM_BlendingLookupTableDescriptor        DcmTagKey(0x0028, 0x1407)
#define DCM_BlendingLookupTableData              DcmTagKey(0x0028, 0x1408)
#define DCM_EnhancedPaletteColorLookupTableSequence DcmTagKey(0x0028, 0x140b)
#define DCM_BlendingLUT2Sequence                 DcmTagKey(0x0028, 0x140c)
#define DCM_BlendingLUT2TransferFunction         DcmTagKey(0x0028, 0x140d)
#define DCM_DataPathID                           DcmTagKey(0x0028, 0x140e)
#define DCM_RGBLUTTransferFunction               DcmTagKey(0x0028, 0x140f)
#define DCM_AlphaLUTTransferFunction             DcmTagKey(0x0028, 0x1410)
#define DCM_ICCProfile                           DcmTagKey(0x0028, 0x2000)
#define DCM_ColorSpace                           DcmTagKey(0x0028, 0x2002)
#define DCM_LossyImageCompression                DcmTagKey(0x0028, 0x2110)
#define DCM_LossyImageCompressionRatio           DcmTagKey(0x0028, 0x2112)
#define DCM_LossyImageCompressionMethod          DcmTagKey(0x0028, 0x2114)
#define DCM_ModalityLUTSequence                  DcmTagKey(0x0028, 0x3000)
#define DCM_VariableModalityLUTSequence          DcmTagKey(0x0028, 0x3001)
#define DCM_LUTDescriptor                        DcmTagKey(0x0028, 0x3002)
#define DCM_LUTExplanation                       DcmTagKey(0x0028, 0x3003)
#define DCM_ModalityLUTType                      DcmTagKey(0x0028, 0x3004)
#define DCM_LUTData                              DcmTagKey(0x0028, 0x3006)
#define DCM_VOILUTSequence                       DcmTagKey(0x0028, 0x3010)
#define DCM_SoftcopyVOILUTSequence               DcmTagKey(0x0028, 0x3110)
#define DCM_RETIRED_ImagePresentationComments    DcmTagKey(0x0028, 0x4000)
#define DCM_RETIRED_BiPlaneAcquisitionSequence   DcmTagKey(0x0028, 0x5000)
#define DCM_RepresentativeFrameNumber            DcmTagKey(0x0028, 0x6010)
#define DCM_FrameNumbersOfInterest               DcmTagKey(0x0028, 0x6020)
#define DCM_FrameOfInterestDescription           DcmTagKey(0x0028, 0x6022)
#define DCM_FrameOfInterestType                  DcmTagKey(0x0028, 0x6023)
#define DCM_RETIRED_MaskPointers                 DcmTagKey(0x0028, 0x6030)
#define DCM_RWavePointer                         DcmTagKey(0x0028, 0x6040)
#define DCM_MaskSubtractionSequence              DcmTagKey(0x0028, 0x6100)
#define DCM_MaskOperation                        DcmTagKey(0x0028, 0x6101)
#define DCM_ApplicableFrameRange                 DcmTagKey(0x0028, 0x6102)
#define DCM_MaskFrameNumbers                     DcmTagKey(0x0028, 0x6110)
#define DCM_ContrastFrameAveraging               DcmTagKey(0x0028, 0x6112)
#define DCM_MaskSubPixelShift                    DcmTagKey(0x0028, 0x6114)
#define DCM_TIDOffset                            DcmTagKey(0x0028, 0x6120)
#define DCM_MaskOperationExplanation             DcmTagKey(0x0028, 0x6190)
#define DCM_EquipmentAdministratorSequence       DcmTagKey(0x0028, 0x7000)
#define DCM_NumberOfDisplaySubsystems            DcmTagKey(0x0028, 0x7001)
#define DCM_CurrentConfigurationID               DcmTagKey(0x0028, 0x7002)
#define DCM_DisplaySubsystemID                   DcmTagKey(0x0028, 0x7003)
#define DCM_DisplaySubsystemName                 DcmTagKey(0x0028, 0x7004)
#define DCM_DisplaySubsystemDescription          DcmTagKey(0x0028, 0x7005)
#define DCM_SystemStatus                         DcmTagKey(0x0028, 0x7006)
#define DCM_SystemStatusComment                  DcmTagKey(0x0028, 0x7007)
#define DCM_TargetLuminanceCharacteristicsSequence DcmTagKey(0x0028, 0x7008)
#define DCM_LuminanceCharacteristicsID           DcmTagKey(0x0028, 0x7009)
#define DCM_DisplaySubsystemConfigurationSequence DcmTagKey(0x0028, 0x700a)
#define DCM_ConfigurationID                      DcmTagKey(0x0028, 0x700b)
#define DCM_ConfigurationName                    DcmTagKey(0x0028, 0x700c)
#define DCM_ConfigurationDescription             DcmTagKey(0x0028, 0x700d)
#define DCM_ReferencedTargetLuminanceCharacteristicsID DcmTagKey(0x0028, 0x700e)
#define DCM_QAResultsSequence                    DcmTagKey(0x0028, 0x700f)
#define DCM_DisplaySubsystemQAResultsSequence    DcmTagKey(0x0028, 0x7010)
#define DCM_ConfigurationQAResultsSequence       DcmTagKey(0x0028, 0x7011)
#define DCM_MeasurementEquipmentSequence         DcmTagKey(0x0028, 0x7012)
#define DCM_MeasurementFunctions                 DcmTagKey(0x0028, 0x7013)
#define DCM_MeasurementEquipmentType             DcmTagKey(0x0028, 0x7014)
#define DCM_VisualEvaluationResultSequence       DcmTagKey(0x0028, 0x7015)
#define DCM_DisplayCalibrationResultSequence     DcmTagKey(0x0028, 0x7016)
#define DCM_DDLValue                             DcmTagKey(0x0028, 0x7017)
#define DCM_CIExyWhitePoint                      DcmTagKey(0x0028, 0x7018)
#define DCM_DisplayFunctionType                  DcmTagKey(0x0028, 0x7019)
#define DCM_GammaValue                           DcmTagKey(0x0028, 0x701a)
#define DCM_NumberOfLuminancePoints              DcmTagKey(0x0028, 0x701b)
#define DCM_LuminanceResponseSequence            DcmTagKey(0x0028, 0x701c)
#define DCM_TargetMinimumLuminance               DcmTagKey(0x0028, 0x701d)
#define DCM_TargetMaximumLuminance               DcmTagKey(0x0028, 0x701e)
#define DCM_LuminanceValue                       DcmTagKey(0x0028, 0x701f)
#define DCM_LuminanceResponseDescription         DcmTagKey(0x0028, 0x7020)
#define DCM_WhitePointFlag                       DcmTagKey(0x0028, 0x7021)
#define DCM_DisplayDeviceTypeCodeSequence        DcmTagKey(0x0028, 0x7022)
#define DCM_DisplaySubsystemSequence             DcmTagKey(0x0028, 0x7023)
#define DCM_LuminanceResultSequence              DcmTagKey(0x0028, 0x7024)
#define DCM_AmbientLightValueSource              DcmTagKey(0x0028, 0x7025)
#define DCM_MeasuredCharacteristics              DcmTagKey(0x0028, 0x7026)
#define DCM_LuminanceUniformityResultSequence    DcmTagKey(0x0028, 0x7027)
#define DCM_VisualEvaluationTestSequence         DcmTagKey(0x0028, 0x7028)
#define DCM_TestResult                           DcmTagKey(0x0028, 0x7029)
#define DCM_TestResultComment                    DcmTagKey(0x0028, 0x702a)
#define DCM_TestImageValidation                  DcmTagKey(0x0028, 0x702b)
#define DCM_TestPatternCodeSequence              DcmTagKey(0x0028, 0x702c)
#define DCM_MeasurementPatternCodeSequence       DcmTagKey(0x0028, 0x702d)
#define DCM_VisualEvaluationMethodCodeSequence   DcmTagKey(0x0028, 0x702e)
#define DCM_PixelDataProviderURL                 DcmTagKey(0x0028, 0x7fe0)
#define DCM_DataPointRows                        DcmTagKey(0x0028, 0x9001)
#define DCM_DataPointColumns                     DcmTagKey(0x0028, 0x9002)
#define DCM_SignalDomainColumns                  DcmTagKey(0x0028, 0x9003)
#define DCM_RETIRED_LargestMonochromePixelValue  DcmTagKey(0x0028, 0x9099)
#define DCM_DataRepresentation                   DcmTagKey(0x0028, 0x9108)
#define DCM_PixelMeasuresSequence                DcmTagKey(0x0028, 0x9110)
#define DCM_FrameVOILUTSequence                  DcmTagKey(0x0028, 0x9132)
#define DCM_PixelValueTransformationSequence     DcmTagKey(0x0028, 0x9145)
#define DCM_SignalDomainRows                     DcmTagKey(0x0028, 0x9235)
#define DCM_DisplayFilterPercentage              DcmTagKey(0x0028, 0x9411)
#define DCM_FramePixelShiftSequence              DcmTagKey(0x0028, 0x9415)
#define DCM_SubtractionItemID                    DcmTagKey(0x0028, 0x9416)
#define DCM_PixelIntensityRelationshipLUTSequence DcmTagKey(0x0028, 0x9422)
#define DCM_FramePixelDataPropertiesSequence     DcmTagKey(0x0028, 0x9443)
#define DCM_GeometricalProperties                DcmTagKey(0x0028, 0x9444)
#define DCM_GeometricMaximumDistortion           DcmTagKey(0x0028, 0x9445)
#define DCM_ImageProcessingApplied               DcmTagKey(0x0028, 0x9446)
#define DCM_MaskSelectionMode                    DcmTagKey(0x0028, 0x9454)
#define DCM_LUTFunction                          DcmTagKey(0x0028, 0x9474)
#define DCM_MaskVisibilityPercentage             DcmTagKey(0x0028, 0x9478)
#define DCM_PixelShiftSequence                   DcmTagKey(0x0028, 0x9501)
#define DCM_RegionPixelShiftSequence             DcmTagKey(0x0028, 0x9502)
#define DCM_VerticesOfTheRegion                  DcmTagKey(0x0028, 0x9503)
#define DCM_MultiFramePresentationSequence       DcmTagKey(0x0028, 0x9505)
#define DCM_PixelShiftFrameRange                 DcmTagKey(0x0028, 0x9506)
#define DCM_LUTFrameRange                        DcmTagKey(0x0028, 0x9507)
#define DCM_ImageToEquipmentMappingMatrix        DcmTagKey(0x0028, 0x9520)
#define DCM_EquipmentCoordinateSystemIdentification DcmTagKey(0x0028, 0x9537)
#define DCM_RETIRED_StudyStatusID                DcmTagKey(0x0032, 0x000a)
#define DCM_RETIRED_StudyPriorityID              DcmTagKey(0x0032, 0x000c)
#define DCM_RETIRED_StudyIDIssuer                DcmTagKey(0x0032, 0x0012)
#define DCM_RETIRED_StudyVerifiedDate            DcmTagKey(0x0032, 0x0032)
#define DCM_RETIRED_StudyVerifiedTime            DcmTagKey(0x0032, 0x0033)
#define DCM_RETIRED_StudyReadDate                DcmTagKey(0x0032, 0x0034)
#define DCM_RETIRED_StudyReadTime                DcmTagKey(0x0032, 0x0035)
#define DCM_RETIRED_ScheduledStudyStartDate      DcmTagKey(0x0032, 0x1000)
#define DCM_RETIRED_ScheduledStudyStartTime      DcmTagKey(0x0032, 0x1001)
#define DCM_RETIRED_ScheduledStudyStopDate       DcmTagKey(0x0032, 0x1010)
#define DCM_RETIRED_ScheduledStudyStopTime       DcmTagKey(0x0032, 0x1011)
#define DCM_RETIRED_ScheduledStudyLocation       DcmTagKey(0x0032, 0x1020)
#define DCM_RETIRED_ScheduledStudyLocationAETitle DcmTagKey(0x0032, 0x1021)
#define DCM_RETIRED_ReasonForStudy               DcmTagKey(0x0032, 0x1030)
#define DCM_RequestingPhysicianIdentificationSequence DcmTagKey(0x0032, 0x1031)
#define DCM_RequestingPhysician                  DcmTagKey(0x0032, 0x1032)
#define DCM_RequestingService                    DcmTagKey(0x0032, 0x1033)
#define DCM_RequestingServiceCodeSequence        DcmTagKey(0x0032, 0x1034)
#define DCM_RETIRED_StudyArrivalDate             DcmTagKey(0x0032, 0x1040)
#define DCM_RETIRED_StudyArrivalTime             DcmTagKey(0x0032, 0x1041)
#define DCM_RETIRED_StudyCompletionDate          DcmTagKey(0x0032, 0x1050)
#define DCM_RETIRED_StudyCompletionTime          DcmTagKey(0x0032, 0x1051)
#define DCM_RETIRED_StudyComponentStatusID       DcmTagKey(0x0032, 0x1055)
#define DCM_RequestedProcedureDescription        DcmTagKey(0x0032, 0x1060)
#define DCM_RequestedProcedureCodeSequence       DcmTagKey(0x0032, 0x1064)
#define DCM_RequestedLateralityCodeSequence      DcmTagKey(0x0032, 0x1065)
#define DCM_ReasonForVisit                       DcmTagKey(0x0032, 0x1066)
#define DCM_ReasonForVisitCodeSequence           DcmTagKey(0x0032, 0x1067)
#define DCM_RequestedContrastAgent               DcmTagKey(0x0032, 0x1070)
#define DCM_RETIRED_StudyComments                DcmTagKey(0x0032, 0x4000)
#define DCM_FlowIdentifierSequence               DcmTagKey(0x0034, 0x0001)
#define DCM_FlowIdentifier                       DcmTagKey(0x0034, 0x0002)
#define DCM_FlowTransferSyntaxUID                DcmTagKey(0x0034, 0x0003)
#define DCM_FlowRTPSamplingRate                  DcmTagKey(0x0034, 0x0004)
#define DCM_SourceIdentifier                     DcmTagKey(0x0034, 0x0005)
#define DCM_FrameOriginTimestamp                 DcmTagKey(0x0034, 0x0007)
#define DCM_IncludesImagingSubject               DcmTagKey(0x0034, 0x0008)
#define DCM_FrameUsefulnessGroupSequence         DcmTagKey(0x0034, 0x0009)
#define DCM_RealTimeBulkDataFlowSequence         DcmTagKey(0x0034, 0x000a)
#define DCM_CameraPositionGroupSequence          DcmTagKey(0x0034, 0x000b)
#define DCM_IncludesInformation                  DcmTagKey(0x0034, 0x000c)
#define DCM_TimeOfFrameGroupSequence             DcmTagKey(0x0034, 0x000d)
#define DCM_RETIRED_ReferencedPatientAliasSequence DcmTagKey(0x0038, 0x0004)
#define DCM_VisitStatusID                        DcmTagKey(0x0038, 0x0008)
#define DCM_AdmissionID                          DcmTagKey(0x0038, 0x0010)
#define DCM_RETIRED_IssuerOfAdmissionID          DcmTagKey(0x0038, 0x0011)
#define DCM_IssuerOfAdmissionIDSequence          DcmTagKey(0x0038, 0x0014)
#define DCM_RouteOfAdmissions                    DcmTagKey(0x0038, 0x0016)
#define DCM_RETIRED_ScheduledAdmissionDate       DcmTagKey(0x0038, 0x001a)
#define DCM_RETIRED_ScheduledAdmissionTime       DcmTagKey(0x0038, 0x001b)
#define DCM_RETIRED_ScheduledDischargeDate       DcmTagKey(0x0038, 0x001c)
#define DCM_RETIRED_ScheduledDischargeTime       DcmTagKey(0x0038, 0x001d)
#define DCM_RETIRED_ScheduledPatientInstitutionResidence DcmTagKey(0x0038, 0x001e)
#define DCM_AdmittingDate                        DcmTagKey(0x0038, 0x0020)
#define DCM_AdmittingTime                        DcmTagKey(0x0038, 0x0021)
#define DCM_RETIRED_DischargeDate                DcmTagKey(0x0038, 0x0030)
#define DCM_RETIRED_DischargeTime                DcmTagKey(0x0038, 0x0032)
#define DCM_RETIRED_DischargeDiagnosisDescription DcmTagKey(0x0038, 0x0040)
#define DCM_RETIRED_DischargeDiagnosisCodeSequence DcmTagKey(0x0038, 0x0044)
#define DCM_SpecialNeeds                         DcmTagKey(0x0038, 0x0050)
#define DCM_ServiceEpisodeID                     DcmTagKey(0x0038, 0x0060)
#define DCM_RETIRED_IssuerOfServiceEpisodeID     DcmTagKey(0x0038, 0x0061)
#define DCM_ServiceEpisodeDescription            DcmTagKey(0x0038, 0x0062)
#define DCM_IssuerOfServiceEpisodeIDSequence     DcmTagKey(0x0038, 0x0064)
#define DCM_PertinentDocumentsSequence           DcmTagKey(0x0038, 0x0100)
#define DCM_PertinentResourcesSequence           DcmTagKey(0x0038, 0x0101)
#define DCM_ResourceDescription                  DcmTagKey(0x0038, 0x0102)
#define DCM_CurrentPatientLocation               DcmTagKey(0x0038, 0x0300)
#define DCM_PatientInstitutionResidence          DcmTagKey(0x0038, 0x0400)
#define DCM_PatientState                         DcmTagKey(0x0038, 0x0500)
#define DCM_PatientClinicalTrialParticipationSequence DcmTagKey(0x0038, 0x0502)
#define DCM_VisitComments                        DcmTagKey(0x0038, 0x4000)
#define DCM_WaveformOriginality                  DcmTagKey(0x003a, 0x0004)
#define DCM_NumberOfWaveformChannels             DcmTagKey(0x003a, 0x0005)
#define DCM_NumberOfWaveformSamples              DcmTagKey(0x003a, 0x0010)
#define DCM_SamplingFrequency                    DcmTagKey(0x003a, 0x001a)
#define DCM_MultiplexGroupLabel                  DcmTagKey(0x003a, 0x0020)
#define DCM_ChannelDefinitionSequence            DcmTagKey(0x003a, 0x0200)
#define DCM_WaveformChannelNumber                DcmTagKey(0x003a, 0x0202)
#define DCM_ChannelLabel                         DcmTagKey(0x003a, 0x0203)
#define DCM_ChannelStatus                        DcmTagKey(0x003a, 0x0205)
#define DCM_ChannelSourceSequence                DcmTagKey(0x003a, 0x0208)
#define DCM_ChannelSourceModifiersSequence       DcmTagKey(0x003a, 0x0209)
#define DCM_SourceWaveformSequence               DcmTagKey(0x003a, 0x020a)
#define DCM_ChannelDerivationDescription         DcmTagKey(0x003a, 0x020c)
#define DCM_ChannelSensitivity                   DcmTagKey(0x003a, 0x0210)
#define DCM_ChannelSensitivityUnitsSequence      DcmTagKey(0x003a, 0x0211)
#define DCM_ChannelSensitivityCorrectionFactor   DcmTagKey(0x003a, 0x0212)
#define DCM_ChannelBaseline                      DcmTagKey(0x003a, 0x0213)
#define DCM_ChannelTimeSkew                      DcmTagKey(0x003a, 0x0214)
#define DCM_ChannelSampleSkew                    DcmTagKey(0x003a, 0x0215)
#define DCM_ChannelOffset                        DcmTagKey(0x003a, 0x0218)
#define DCM_WaveformBitsStored                   DcmTagKey(0x003a, 0x021a)
#define DCM_FilterLowFrequency                   DcmTagKey(0x003a, 0x0220)
#define DCM_FilterHighFrequency                  DcmTagKey(0x003a, 0x0221)
#define DCM_NotchFilterFrequency                 DcmTagKey(0x003a, 0x0222)
#define DCM_NotchFilterBandwidth                 DcmTagKey(0x003a, 0x0223)
#define DCM_WaveformDataDisplayScale             DcmTagKey(0x003a, 0x0230)
#define DCM_WaveformDisplayBackgroundCIELabValue DcmTagKey(0x003a, 0x0231)
#define DCM_WaveformPresentationGroupSequence    DcmTagKey(0x003a, 0x0240)
#define DCM_PresentationGroupNumber              DcmTagKey(0x003a, 0x0241)
#define DCM_ChannelDisplaySequence               DcmTagKey(0x003a, 0x0242)
#define DCM_ChannelRecommendedDisplayCIELabValue DcmTagKey(0x003a, 0x0244)
#define DCM_ChannelPosition                      DcmTagKey(0x003a, 0x0245)
#define DCM_DisplayShadingFlag                   DcmTagKey(0x003a, 0x0246)
#define DCM_FractionalChannelDisplayScale        DcmTagKey(0x003a, 0x0247)
#define DCM_AbsoluteChannelDisplayScale          DcmTagKey(0x003a, 0x0248)
#define DCM_MultiplexedAudioChannelsDescriptionCodeSequence DcmTagKey(0x003a, 0x0300)
#define DCM_ChannelIdentificationCode            DcmTagKey(0x003a, 0x0301)
#define DCM_ChannelMode                          DcmTagKey(0x003a, 0x0302)
#define DCM_MultiplexGroupUID                    DcmTagKey(0x003a, 0x0310)
#define DCM_PowerlineFrequency                   DcmTagKey(0x003a, 0x0311)
#define DCM_ChannelImpedanceSequence             DcmTagKey(0x003a, 0x0312)
#define DCM_ImpedanceValue                       DcmTagKey(0x003a, 0x0313)
#define DCM_ImpedanceMeasurementDateTime         DcmTagKey(0x003a, 0x0314)
#define DCM_ImpedanceMeasurementFrequency        DcmTagKey(0x003a, 0x0315)
#define DCM_ImpedanceMeasurementCurrentType      DcmTagKey(0x003a, 0x0316)
#define DCM_WaveformAmplifierType                DcmTagKey(0x003a, 0x0317)
#define DCM_FilterLowFrequencyCharacteristicsSequence DcmTagKey(0x003a, 0x0318)
#define DCM_FilterHighFrequencyCharacteristicsSequence DcmTagKey(0x003a, 0x0319)
#define DCM_SummarizedFilterLookupTableSequence  DcmTagKey(0x003a, 0x0320)
#define DCM_NotchFilterCharacteristicsSequence   DcmTagKey(0x003a, 0x0321)
#define DCM_WaveformFilterType                   DcmTagKey(0x003a, 0x0322)
#define DCM_AnalogFilterCharacteristicsSequence  DcmTagKey(0x003a, 0x0323)
#define DCM_AnalogFilterRollOff                  DcmTagKey(0x003a, 0x0324)
#define DCM_AnalogFilterTypeCodeSequence         DcmTagKey(0x003a, 0x0325)
#define DCM_DigitalFilterCharacteristicsSequence DcmTagKey(0x003a, 0x0326)
#define DCM_DigitalFilterOrder                   DcmTagKey(0x003a, 0x0327)
#define DCM_DigitalFilterTypeCodeSequence        DcmTagKey(0x003a, 0x0328)
#define DCM_WaveformFilterDescription            DcmTagKey(0x003a, 0x0329)
#define DCM_FilterLookupTableSequence            DcmTagKey(0x003a, 0x032a)
#define DCM_FilterLookupTableDescription         DcmTagKey(0x003a, 0x032b)
#define DCM_FrequencyEncodingCodeSequence        DcmTagKey(0x003a, 0x032c)
#define DCM_MagnitudeEncodingCodeSequence        DcmTagKey(0x003a, 0x032d)
#define DCM_FilterLookupTableData                DcmTagKey(0x003a, 0x032e)
#define DCM_ScheduledStationAETitle              DcmTagKey(0x0040, 0x0001)
#define DCM_ScheduledProcedureStepStartDate      DcmTagKey(0x0040, 0x0002)
#define DCM_ScheduledProcedureStepStartTime      DcmTagKey(0x0040, 0x0003)
#define DCM_ScheduledProcedureStepEndDate        DcmTagKey(0x0040, 0x0004)
#define DCM_ScheduledProcedureStepEndTime        DcmTagKey(0x0040, 0x0005)
#define DCM_ScheduledPerformingPhysicianName     DcmTagKey(0x0040, 0x0006)
#define DCM_ScheduledProcedureStepDescription    DcmTagKey(0x0040, 0x0007)
#define DCM_ScheduledProtocolCodeSequence        DcmTagKey(0x0040, 0x0008)
#define DCM_ScheduledProcedureStepID             DcmTagKey(0x0040, 0x0009)
#define DCM_StageCodeSequence                    DcmTagKey(0x0040, 0x000a)
#define DCM_ScheduledPerformingPhysicianIdentificationSequence DcmTagKey(0x0040, 0x000b)
#define DCM_ScheduledStationName                 DcmTagKey(0x0040, 0x0010)
#define DCM_ScheduledProcedureStepLocation       DcmTagKey(0x0040, 0x0011)
#define DCM_PreMedication                        DcmTagKey(0x0040, 0x0012)
#define DCM_ScheduledProcedureStepStatus         DcmTagKey(0x0040, 0x0020)
#define DCM_OrderPlacerIdentifierSequence        DcmTagKey(0x0040, 0x0026)
#define DCM_OrderFillerIdentifierSequence        DcmTagKey(0x0040, 0x0027)
#define DCM_LocalNamespaceEntityID               DcmTagKey(0x0040, 0x0031)
#define DCM_UniversalEntityID                    DcmTagKey(0x0040, 0x0032)
#define DCM_UniversalEntityIDType                DcmTagKey(0x0040, 0x0033)
#define DCM_IdentifierTypeCode                   DcmTagKey(0x0040, 0x0035)
#define DCM_AssigningFacilitySequence            DcmTagKey(0x0040, 0x0036)
#define DCM_AssigningJurisdictionCodeSequence    DcmTagKey(0x0040, 0x0039)
#define DCM_AssigningAgencyOrDepartmentCodeSequence DcmTagKey(0x0040, 0x003a)
#define DCM_ScheduledProcedureStepSequence       DcmTagKey(0x0040, 0x0100)
#define DCM_ReferencedNonImageCompositeSOPInstanceSequence DcmTagKey(0x0040, 0x0220)
#define DCM_PerformedStationAETitle              DcmTagKey(0x0040, 0x0241)
#define DCM_PerformedStationName                 DcmTagKey(0x0040, 0x0242)
#define DCM_PerformedLocation                    DcmTagKey(0x0040, 0x0243)
#define DCM_PerformedProcedureStepStartDate      DcmTagKey(0x0040, 0x0244)
#define DCM_PerformedProcedureStepStartTime      DcmTagKey(0x0040, 0x0245)
#define DCM_PerformedProcedureStepEndDate        DcmTagKey(0x0040, 0x0250)
#define DCM_PerformedProcedureStepEndTime        DcmTagKey(0x0040, 0x0251)
#define DCM_PerformedProcedureStepStatus         DcmTagKey(0x0040, 0x0252)
#define DCM_PerformedProcedureStepID             DcmTagKey(0x0040, 0x0253)
#define DCM_PerformedProcedureStepDescription    DcmTagKey(0x0040, 0x0254)
#define DCM_PerformedProcedureTypeDescription    DcmTagKey(0x0040, 0x0255)
#define DCM_PerformedProtocolCodeSequence        DcmTagKey(0x0040, 0x0260)
#define DCM_PerformedProtocolType                DcmTagKey(0x0040, 0x0261)
#define DCM_ScheduledStepAttributesSequence      DcmTagKey(0x0040, 0x0270)
#define DCM_RequestAttributesSequence            DcmTagKey(0x0040, 0x0275)
#define DCM_CommentsOnThePerformedProcedureStep  DcmTagKey(0x0040, 0x0280)
#define DCM_PerformedProcedureStepDiscontinuationReasonCodeSequence DcmTagKey(0x0040, 0x0281)
#define DCM_QuantitySequence                     DcmTagKey(0x0040, 0x0293)
#define DCM_Quantity                             DcmTagKey(0x0040, 0x0294)
#define DCM_MeasuringUnitsSequence               DcmTagKey(0x0040, 0x0295)
#define DCM_BillingItemSequence                  DcmTagKey(0x0040, 0x0296)
#define DCM_RETIRED_TotalTimeOfFluoroscopy       DcmTagKey(0x0040, 0x0300)
#define DCM_RETIRED_TotalNumberOfExposures       DcmTagKey(0x0040, 0x0301)
#define DCM_EntranceDose                         DcmTagKey(0x0040, 0x0302)
#define DCM_ExposedArea                          DcmTagKey(0x0040, 0x0303)
#define DCM_DistanceSourceToEntrance             DcmTagKey(0x0040, 0x0306)
#define DCM_RETIRED_DistanceSourceToSupport      DcmTagKey(0x0040, 0x0307)
#define DCM_RETIRED_ExposureDoseSequence         DcmTagKey(0x0040, 0x030e)
#define DCM_CommentsOnRadiationDose              DcmTagKey(0x0040, 0x0310)
#define DCM_XRayOutput                           DcmTagKey(0x0040, 0x0312)
#define DCM_HalfValueLayer                       DcmTagKey(0x0040, 0x0314)
#define DCM_OrganDose                            DcmTagKey(0x0040, 0x0316)
#define DCM_OrganExposed                         DcmTagKey(0x0040, 0x0318)
#define DCM_BillingProcedureStepSequence         DcmTagKey(0x0040, 0x0320)
#define DCM_FilmConsumptionSequence              DcmTagKey(0x0040, 0x0321)
#define DCM_BillingSuppliesAndDevicesSequence    DcmTagKey(0x0040, 0x0324)
#define DCM_RETIRED_ReferencedProcedureStepSequence DcmTagKey(0x0040, 0x0330)
#define DCM_PerformedSeriesSequence              DcmTagKey(0x0040, 0x0340)
#define DCM_CommentsOnTheScheduledProcedureStep  DcmTagKey(0x0040, 0x0400)
#define DCM_ProtocolContextSequence              DcmTagKey(0x0040, 0x0440)
#define DCM_ContentItemModifierSequence          DcmTagKey(0x0040, 0x0441)
#define DCM_ScheduledSpecimenSequence            DcmTagKey(0x0040, 0x0500)
#define DCM_RETIRED_SpecimenAccessionNumber      DcmTagKey(0x0040, 0x050a)
#define DCM_ContainerIdentifier                  DcmTagKey(0x0040, 0x0512)
#define DCM_IssuerOfTheContainerIdentifierSequence DcmTagKey(0x0040, 0x0513)
#define DCM_AlternateContainerIdentifierSequence DcmTagKey(0x0040, 0x0515)
#define DCM_ContainerTypeCodeSequence            DcmTagKey(0x0040, 0x0518)
#define DCM_ContainerDescription                 DcmTagKey(0x0040, 0x051a)
#define DCM_ContainerComponentSequence           DcmTagKey(0x0040, 0x0520)
#define DCM_RETIRED_SpecimenSequence             DcmTagKey(0x0040, 0x0550)
#define DCM_SpecimenIdentifier                   DcmTagKey(0x0040, 0x0551)
#define DCM_RETIRED_SpecimenDescriptionSequenceTrial DcmTagKey(0x0040, 0x0552)
#define DCM_RETIRED_SpecimenDescriptionTrial     DcmTagKey(0x0040, 0x0553)
#define DCM_SpecimenUID                          DcmTagKey(0x0040, 0x0554)
#define DCM_AcquisitionContextSequence           DcmTagKey(0x0040, 0x0555)
#define DCM_AcquisitionContextDescription        DcmTagKey(0x0040, 0x0556)
#define DCM_SpecimenDescriptionSequence          DcmTagKey(0x0040, 0x0560)
#define DCM_IssuerOfTheSpecimenIdentifierSequence DcmTagKey(0x0040, 0x0562)
#define DCM_SpecimenTypeCodeSequence             DcmTagKey(0x0040, 0x059a)
#define DCM_SpecimenShortDescription             DcmTagKey(0x0040, 0x0600)
#define DCM_SpecimenDetailedDescription          DcmTagKey(0x0040, 0x0602)
#define DCM_SpecimenPreparationSequence          DcmTagKey(0x0040, 0x0610)
#define DCM_SpecimenPreparationStepContentItemSequence DcmTagKey(0x0040, 0x0612)
#define DCM_SpecimenLocalizationContentItemSequence DcmTagKey(0x0040, 0x0620)
#define DCM_RETIRED_SlideIdentifier              DcmTagKey(0x0040, 0x06fa)
#define DCM_WholeSlideMicroscopyImageFrameTypeSequence DcmTagKey(0x0040, 0x0710)
#define DCM_ImageCenterPointCoordinatesSequence  DcmTagKey(0x0040, 0x071a)
#define DCM_XOffsetInSlideCoordinateSystem       DcmTagKey(0x0040, 0x072a)
#define DCM_YOffsetInSlideCoordinateSystem       DcmTagKey(0x0040, 0x073a)
#define DCM_ZOffsetInSlideCoordinateSystem       DcmTagKey(0x0040, 0x074a)
#define DCM_RETIRED_PixelSpacingSequence         DcmTagKey(0x0040, 0x08d8)
#define DCM_RETIRED_CoordinateSystemAxisCodeSequence DcmTagKey(0x0040, 0x08da)
#define DCM_MeasurementUnitsCodeSequence         DcmTagKey(0x0040, 0x08ea)
#define DCM_RETIRED_VitalStainCodeSequenceTrial  DcmTagKey(0x0040, 0x09f8)
#define DCM_RequestedProcedureID                 DcmTagKey(0x0040, 0x1001)
#define DCM_ReasonForTheRequestedProcedure       DcmTagKey(0x0040, 0x1002)
#define DCM_RequestedProcedurePriority           DcmTagKey(0x0040, 0x1003)
#define DCM_PatientTransportArrangements         DcmTagKey(0x0040, 0x1004)
#define DCM_RequestedProcedureLocation           DcmTagKey(0x0040, 0x1005)
#define DCM_RETIRED_PlacerOrderNumberProcedure   DcmTagKey(0x0040, 0x1006)
#define DCM_RETIRED_FillerOrderNumberProcedure   DcmTagKey(0x0040, 0x1007)
#define DCM_ConfidentialityCode                  DcmTagKey(0x0040, 0x1008)
#define DCM_ReportingPriority                    DcmTagKey(0x0040, 0x1009)
#define DCM_ReasonForRequestedProcedureCodeSequence DcmTagKey(0x0040, 0x100a)
#define DCM_NamesOfIntendedRecipientsOfResults   DcmTagKey(0x0040, 0x1010)
#define DCM_IntendedRecipientsOfResultsIdentificationSequence DcmTagKey(0x0040, 0x1011)
#define DCM_ReasonForPerformedProcedureCodeSequence DcmTagKey(0x0040, 0x1012)
#define DCM_RETIRED_RequestedProcedureDescriptionTrial DcmTagKey(0x0040, 0x1060)
#define DCM_PersonIdentificationCodeSequence     DcmTagKey(0x0040, 0x1101)
#define DCM_PersonAddress                        DcmTagKey(0x0040, 0x1102)
#define DCM_PersonTelephoneNumbers               DcmTagKey(0x0040, 0x1103)
#define DCM_PersonTelecomInformation             DcmTagKey(0x0040, 0x1104)
#define DCM_RequestedProcedureComments           DcmTagKey(0x0040, 0x1400)
#define DCM_RETIRED_ReasonForTheImagingServiceRequest DcmTagKey(0x0040, 0x2001)
#define DCM_IssueDateOfImagingServiceRequest     DcmTagKey(0x0040, 0x2004)
#define DCM_IssueTimeOfImagingServiceRequest     DcmTagKey(0x0040, 0x2005)
#define DCM_RETIRED_PlacerOrderNumberImagingServiceRequestRetired DcmTagKey(0x0040, 0x2006)
#define DCM_RETIRED_FillerOrderNumberImagingServiceRequestRetired DcmTagKey(0x0040, 0x2007)
#define DCM_OrderEnteredBy                       DcmTagKey(0x0040, 0x2008)
#define DCM_OrderEntererLocation                 DcmTagKey(0x0040, 0x2009)
#define DCM_OrderCallbackPhoneNumber             DcmTagKey(0x0040, 0x2010)
#define DCM_OrderCallbackTelecomInformation      DcmTagKey(0x0040, 0x2011)
#define DCM_PlacerOrderNumberImagingServiceRequest DcmTagKey(0x0040, 0x2016)
#define DCM_FillerOrderNumberImagingServiceRequest DcmTagKey(0x0040, 0x2017)
#define DCM_ImagingServiceRequestComments        DcmTagKey(0x0040, 0x2400)
#define DCM_ConfidentialityConstraintOnPatientDataDescription DcmTagKey(0x0040, 0x3001)
#define DCM_RETIRED_GeneralPurposeScheduledProcedureStepStatus DcmTagKey(0x0040, 0x4001)
#define DCM_RETIRED_GeneralPurposePerformedProcedureStepStatus DcmTagKey(0x0040, 0x4002)
#define DCM_RETIRED_GeneralPurposeScheduledProcedureStepPriority DcmTagKey(0x0040, 0x4003)
#define DCM_RETIRED_ScheduledProcessingApplicationsCodeSequence DcmTagKey(0x0040, 0x4004)
#define DCM_ScheduledProcedureStepStartDateTime  DcmTagKey(0x0040, 0x4005)
#define DCM_RETIRED_MultipleCopiesFlag           DcmTagKey(0x0040, 0x4006)
#define DCM_RETIRED_PerformedProcessingApplicationsCodeSequence DcmTagKey(0x0040, 0x4007)
#define DCM_ScheduledProcedureStepExpirationDateTime DcmTagKey(0x0040, 0x4008)
#define DCM_HumanPerformerCodeSequence           DcmTagKey(0x0040, 0x4009)
#define DCM_ScheduledProcedureStepModificationDateTime DcmTagKey(0x0040, 0x4010)
#define DCM_ExpectedCompletionDateTime           DcmTagKey(0x0040, 0x4011)
#define DCM_RETIRED_ResultingGeneralPurposePerformedProcedureStepsSequence DcmTagKey(0x0040, 0x4015)
#define DCM_RETIRED_ReferencedGeneralPurposeScheduledProcedureStepSequence DcmTagKey(0x0040, 0x4016)
#define DCM_ScheduledWorkitemCodeSequence        DcmTagKey(0x0040, 0x4018)
#define DCM_PerformedWorkitemCodeSequence        DcmTagKey(0x0040, 0x4019)
#define DCM_RETIRED_InputAvailabilityFlag        DcmTagKey(0x0040, 0x4020)
#define DCM_InputInformationSequence             DcmTagKey(0x0040, 0x4021)
#define DCM_RETIRED_RelevantInformationSequence  DcmTagKey(0x0040, 0x4022)
#define DCM_RETIRED_ReferencedGeneralPurposeScheduledProcedureStepTransactionUID DcmTagKey(0x0040, 0x4023)
#define DCM_ScheduledStationNameCodeSequence     DcmTagKey(0x0040, 0x4025)
#define DCM_ScheduledStationClassCodeSequence    DcmTagKey(0x0040, 0x4026)
#define DCM_ScheduledStationGeographicLocationCodeSequence DcmTagKey(0x0040, 0x4027)
#define DCM_PerformedStationNameCodeSequence     DcmTagKey(0x0040, 0x4028)
#define DCM_PerformedStationClassCodeSequence    DcmTagKey(0x0040, 0x4029)
#define DCM_PerformedStationGeographicLocationCodeSequence DcmTagKey(0x0040, 0x4030)
#define DCM_RETIRED_RequestedSubsequentWorkitemCodeSequence DcmTagKey(0x0040, 0x4031)
#define DCM_RETIRED_NonDICOMOutputCodeSequence   DcmTagKey(0x0040, 0x4032)
#define DCM_OutputInformationSequence            DcmTagKey(0x0040, 0x4033)
#define DCM_ScheduledHumanPerformersSequence     DcmTagKey(0x0040, 0x4034)
#define DCM_ActualHumanPerformersSequence        DcmTagKey(0x0040, 0x4035)
#define DCM_HumanPerformerOrganization           DcmTagKey(0x0040, 0x4036)
#define DCM_HumanPerformerName                   DcmTagKey(0x0040, 0x4037)
#define DCM_RawDataHandling                      DcmTagKey(0x0040, 0x4040)
#define DCM_InputReadinessState                  DcmTagKey(0x0040, 0x4041)
#define DCM_PerformedProcedureStepStartDateTime  DcmTagKey(0x0040, 0x4050)
#define DCM_PerformedProcedureStepEndDateTime    DcmTagKey(0x0040, 0x4051)
#define DCM_ProcedureStepCancellationDateTime    DcmTagKey(0x0040, 0x4052)
#define DCM_OutputDestinationSequence            DcmTagKey(0x0040, 0x4070)
#define DCM_DICOMStorageSequence                 DcmTagKey(0x0040, 0x4071)
#define DCM_STOWRSStorageSequence                DcmTagKey(0x0040, 0x4072)
#define DCM_StorageURL                           DcmTagKey(0x0040, 0x4073)
#define DCM_XDSStorageSequence                   DcmTagKey(0x0040, 0x4074)
#define DCM_EntranceDoseInmGy                    DcmTagKey(0x0040, 0x8302)
#define DCM_EntranceDoseDerivation               DcmTagKey(0x0040, 0x8303)
#define DCM_ParametricMapFrameTypeSequence       DcmTagKey(0x0040, 0x9092)
#define DCM_ReferencedImageRealWorldValueMappingSequence DcmTagKey(0x0040, 0x9094)
#define DCM_RealWorldValueMappingSequence        DcmTagKey(0x0040, 0x9096)
#define DCM_PixelValueMappingCodeSequence        DcmTagKey(0x0040, 0x9098)
#define DCM_LUTLabel                             DcmTagKey(0x0040, 0x9210)
#define DCM_RealWorldValueLastValueMapped        DcmTagKey(0x0040, 0x9211)
#define DCM_RealWorldValueLUTData                DcmTagKey(0x0040, 0x9212)
#define DCM_DoubleFloatRealWorldValueLastValueMapped DcmTagKey(0x0040, 0x9213)
#define DCM_DoubleFloatRealWorldValueFirstValueMapped DcmTagKey(0x0040, 0x9214)
#define DCM_RealWorldValueFirstValueMapped       DcmTagKey(0x0040, 0x9216)
#define DCM_QuantityDefinitionSequence           DcmTagKey(0x0040, 0x9220)
#define DCM_RealWorldValueIntercept              DcmTagKey(0x0040, 0x9224)
#define DCM_RealWorldValueSlope                  DcmTagKey(0x0040, 0x9225)
#define DCM_RETIRED_FindingsFlagTrial            DcmTagKey(0x0040, 0xa007)
#define DCM_RelationshipType                     DcmTagKey(0x0040, 0xa010)
#define DCM_RETIRED_FindingsSequenceTrial        DcmTagKey(0x0040, 0xa020)
#define DCM_RETIRED_FindingsGroupUIDTrial        DcmTagKey(0x0040, 0xa021)
#define DCM_RETIRED_ReferencedFindingsGroupUIDTrial DcmTagKey(0x0040, 0xa022)
#define DCM_RETIRED_FindingsGroupRecordingDateTrial DcmTagKey(0x0040, 0xa023)
#define DCM_RETIRED_FindingsGroupRecordingTimeTrial DcmTagKey(0x0040, 0xa024)
#define DCM_RETIRED_FindingsSourceCategoryCodeSequenceTrial DcmTagKey(0x0040, 0xa026)
#define DCM_VerifyingOrganization                DcmTagKey(0x0040, 0xa027)
#define DCM_RETIRED_DocumentingOrganizationIdentifierCodeSequenceTrial DcmTagKey(0x0040, 0xa028)
#define DCM_VerificationDateTime                 DcmTagKey(0x0040, 0xa030)
#define DCM_ObservationDateTime                  DcmTagKey(0x0040, 0xa032)
#define DCM_ObservationStartDateTime             DcmTagKey(0x0040, 0xa033)
#define DCM_ValueType                            DcmTagKey(0x0040, 0xa040)
#define DCM_ConceptNameCodeSequence              DcmTagKey(0x0040, 0xa043)
#define DCM_RETIRED_MeasurementPrecisionDescriptionTrial DcmTagKey(0x0040, 0xa047)
#define DCM_ContinuityOfContent                  DcmTagKey(0x0040, 0xa050)
#define DCM_RETIRED_UrgencyOrPriorityAlertsTrial DcmTagKey(0x0040, 0xa057)
#define DCM_RETIRED_SequencingIndicatorTrial     DcmTagKey(0x0040, 0xa060)
#define DCM_RETIRED_DocumentIdentifierCodeSequenceTrial DcmTagKey(0x0040, 0xa066)
#define DCM_RETIRED_DocumentAuthorTrial          DcmTagKey(0x0040, 0xa067)
#define DCM_RETIRED_DocumentAuthorIdentifierCodeSequenceTrial DcmTagKey(0x0040, 0xa068)
#define DCM_RETIRED_IdentifierCodeSequenceTrial  DcmTagKey(0x0040, 0xa070)
#define DCM_VerifyingObserverSequence            DcmTagKey(0x0040, 0xa073)
#define DCM_RETIRED_ObjectBinaryIdentifierTrial  DcmTagKey(0x0040, 0xa074)
#define DCM_VerifyingObserverName                DcmTagKey(0x0040, 0xa075)
#define DCM_RETIRED_DocumentingObserverIdentifierCodeSequenceTrial DcmTagKey(0x0040, 0xa076)
#define DCM_AuthorObserverSequence               DcmTagKey(0x0040, 0xa078)
#define DCM_ParticipantSequence                  DcmTagKey(0x0040, 0xa07a)
#define DCM_CustodialOrganizationSequence        DcmTagKey(0x0040, 0xa07c)
#define DCM_ParticipationType                    DcmTagKey(0x0040, 0xa080)
#define DCM_ParticipationDateTime                DcmTagKey(0x0040, 0xa082)
#define DCM_ObserverType                         DcmTagKey(0x0040, 0xa084)
#define DCM_RETIRED_ProcedureIdentifierCodeSequenceTrial DcmTagKey(0x0040, 0xa085)
#define DCM_VerifyingObserverIdentificationCodeSequence DcmTagKey(0x0040, 0xa088)
#define DCM_RETIRED_ObjectDirectoryBinaryIdentifierTrial DcmTagKey(0x0040, 0xa089)
#define DCM_RETIRED_EquivalentCDADocumentSequence DcmTagKey(0x0040, 0xa090)
#define DCM_ReferencedWaveformChannels           DcmTagKey(0x0040, 0xa0b0)
#define DCM_RETIRED_DateOfDocumentOrVerbalTransactionTrial DcmTagKey(0x0040, 0xa110)
#define DCM_RETIRED_TimeOfDocumentCreationOrVerbalTransactionTrial DcmTagKey(0x0040, 0xa112)
#define DCM_DateTime                             DcmTagKey(0x0040, 0xa120)
#define DCM_Date                                 DcmTagKey(0x0040, 0xa121)
#define DCM_Time                                 DcmTagKey(0x0040, 0xa122)
#define DCM_PersonName                           DcmTagKey(0x0040, 0xa123)
#define DCM_UID                                  DcmTagKey(0x0040, 0xa124)
#define DCM_RETIRED_ReportStatusIDTrial          DcmTagKey(0x0040, 0xa125)
#define DCM_TemporalRangeType                    DcmTagKey(0x0040, 0xa130)
#define DCM_ReferencedSamplePositions            DcmTagKey(0x0040, 0xa132)
#define DCM_RETIRED_ReferencedFrameNumbers       DcmTagKey(0x0040, 0xa136)
#define DCM_ReferencedTimeOffsets                DcmTagKey(0x0040, 0xa138)
#define DCM_ReferencedDateTime                   DcmTagKey(0x0040, 0xa13a)
#define DCM_TextValue                            DcmTagKey(0x0040, 0xa160)
#define DCM_FloatingPointValue                   DcmTagKey(0x0040, 0xa161)
#define DCM_RationalNumeratorValue               DcmTagKey(0x0040, 0xa162)
#define DCM_RationalDenominatorValue             DcmTagKey(0x0040, 0xa163)
#define DCM_RETIRED_ObservationCategoryCodeSequenceTrial DcmTagKey(0x0040, 0xa167)
#define DCM_ConceptCodeSequence                  DcmTagKey(0x0040, 0xa168)
#define DCM_RETIRED_BibliographicCitationTrial   DcmTagKey(0x0040, 0xa16a)
#define DCM_PurposeOfReferenceCodeSequence       DcmTagKey(0x0040, 0xa170)
#define DCM_ObservationUID                       DcmTagKey(0x0040, 0xa171)
#define DCM_RETIRED_ReferencedObservationUIDTrial DcmTagKey(0x0040, 0xa172)
#define DCM_RETIRED_ReferencedObservationClassTrial DcmTagKey(0x0040, 0xa173)
#define DCM_RETIRED_ReferencedObjectObservationClassTrial DcmTagKey(0x0040, 0xa174)
#define DCM_AnnotationGroupNumber                DcmTagKey(0x0040, 0xa180)
#define DCM_RETIRED_ObservationDateTrial         DcmTagKey(0x0040, 0xa192)
#define DCM_RETIRED_ObservationTimeTrial         DcmTagKey(0x0040, 0xa193)
#define DCM_RETIRED_MeasurementAutomationTrial   DcmTagKey(0x0040, 0xa194)
#define DCM_ModifierCodeSequence                 DcmTagKey(0x0040, 0xa195)
#define DCM_RETIRED_IdentificationDescriptionTrial DcmTagKey(0x0040, 0xa224)
#define DCM_RETIRED_CoordinatesSetGeometricTypeTrial DcmTagKey(0x0040, 0xa290)
#define DCM_RETIRED_AlgorithmCodeSequenceTrial   DcmTagKey(0x0040, 0xa296)
#define DCM_RETIRED_AlgorithmDescriptionTrial    DcmTagKey(0x0040, 0xa297)
#define DCM_RETIRED_PixelCoordinatesSetTrial     DcmTagKey(0x0040, 0xa29a)
#define DCM_MeasuredValueSequence                DcmTagKey(0x0040, 0xa300)
#define DCM_NumericValueQualifierCodeSequence    DcmTagKey(0x0040, 0xa301)
#define DCM_RETIRED_CurrentObserverTrial         DcmTagKey(0x0040, 0xa307)
#define DCM_NumericValue                         DcmTagKey(0x0040, 0xa30a)
#define DCM_RETIRED_ReferencedAccessionSequenceTrial DcmTagKey(0x0040, 0xa313)
#define DCM_RETIRED_ReportStatusCommentTrial     DcmTagKey(0x0040, 0xa33a)
#define DCM_RETIRED_ProcedureContextSequenceTrial DcmTagKey(0x0040, 0xa340)
#define DCM_RETIRED_VerbalSourceTrial            DcmTagKey(0x0040, 0xa352)
#define DCM_RETIRED_AddressTrial                 DcmTagKey(0x0040, 0xa353)
#define DCM_RETIRED_TelephoneNumberTrial         DcmTagKey(0x0040, 0xa354)
#define DCM_RETIRED_VerbalSourceIdentifierCodeSequenceTrial DcmTagKey(0x0040, 0xa358)
#define DCM_PredecessorDocumentsSequence         DcmTagKey(0x0040, 0xa360)
#define DCM_ReferencedRequestSequence            DcmTagKey(0x0040, 0xa370)
#define DCM_PerformedProcedureCodeSequence       DcmTagKey(0x0040, 0xa372)
#define DCM_CurrentRequestedProcedureEvidenceSequence DcmTagKey(0x0040, 0xa375)
#define DCM_RETIRED_ReportDetailSequenceTrial    DcmTagKey(0x0040, 0xa380)
#define DCM_PertinentOtherEvidenceSequence       DcmTagKey(0x0040, 0xa385)
#define DCM_HL7StructuredDocumentReferenceSequence DcmTagKey(0x0040, 0xa390)
#define DCM_RETIRED_ObservationSubjectUIDTrial   DcmTagKey(0x0040, 0xa402)
#define DCM_RETIRED_ObservationSubjectClassTrial DcmTagKey(0x0040, 0xa403)
#define DCM_RETIRED_ObservationSubjectTypeCodeSequenceTrial DcmTagKey(0x0040, 0xa404)
#define DCM_CompletionFlag                       DcmTagKey(0x0040, 0xa491)
#define DCM_CompletionFlagDescription            DcmTagKey(0x0040, 0xa492)
#define DCM_VerificationFlag                     DcmTagKey(0x0040, 0xa493)
#define DCM_ArchiveRequested                     DcmTagKey(0x0040, 0xa494)
#define DCM_PreliminaryFlag                      DcmTagKey(0x0040, 0xa496)
#define DCM_ContentTemplateSequence              DcmTagKey(0x0040, 0xa504)
#define DCM_IdenticalDocumentsSequence           DcmTagKey(0x0040, 0xa525)
#define DCM_RETIRED_ObservationSubjectContextFlagTrial DcmTagKey(0x0040, 0xa600)
#define DCM_RETIRED_ObserverContextFlagTrial     DcmTagKey(0x0040, 0xa601)
#define DCM_RETIRED_ProcedureContextFlagTrial    DcmTagKey(0x0040, 0xa603)
#define DCM_ContentSequence                      DcmTagKey(0x0040, 0xa730)
#define DCM_RETIRED_RelationshipSequenceTrial    DcmTagKey(0x0040, 0xa731)
#define DCM_RETIRED_RelationshipTypeCodeSequenceTrial DcmTagKey(0x0040, 0xa732)
#define DCM_RETIRED_LanguageCodeSequenceTrial    DcmTagKey(0x0040, 0xa744)
#define DCM_TabulatedValuesSequence              DcmTagKey(0x0040, 0xa801)
#define DCM_NumberOfTableRows                    DcmTagKey(0x0040, 0xa802)
#define DCM_NumberOfTableColumns                 DcmTagKey(0x0040, 0xa803)
#define DCM_TableRowNumber                       DcmTagKey(0x0040, 0xa804)
#define DCM_TableColumnNumber                    DcmTagKey(0x0040, 0xa805)
#define DCM_TableRowDefinitionSequence           DcmTagKey(0x0040, 0xa806)
#define DCM_TableColumnDefinitionSequence        DcmTagKey(0x0040, 0xa807)
#define DCM_CellValuesSequence                   DcmTagKey(0x0040, 0xa808)
#define DCM_RETIRED_UniformResourceLocatorTrial  DcmTagKey(0x0040, 0xa992)
#define DCM_WaveformAnnotationSequence           DcmTagKey(0x0040, 0xb020)
#define DCM_TemplateIdentifier                   DcmTagKey(0x0040, 0xdb00)
#define DCM_RETIRED_TemplateVersion              DcmTagKey(0x0040, 0xdb06)
#define DCM_RETIRED_TemplateLocalVersion         DcmTagKey(0x0040, 0xdb07)
#define DCM_RETIRED_TemplateExtensionFlag        DcmTagKey(0x0040, 0xdb0b)
#define DCM_RETIRED_TemplateExtensionOrganizationUID DcmTagKey(0x0040, 0xdb0c)
#define DCM_RETIRED_TemplateExtensionCreatorUID  DcmTagKey(0x0040, 0xdb0d)
#define DCM_ReferencedContentItemIdentifier      DcmTagKey(0x0040, 0xdb73)
#define DCM_HL7InstanceIdentifier                DcmTagKey(0x0040, 0xe001)
#define DCM_HL7DocumentEffectiveTime             DcmTagKey(0x0040, 0xe004)
#define DCM_HL7DocumentTypeCodeSequence          DcmTagKey(0x0040, 0xe006)
#define DCM_DocumentClassCodeSequence            DcmTagKey(0x0040, 0xe008)
#define DCM_RetrieveURI                          DcmTagKey(0x0040, 0xe010)
#define DCM_RetrieveLocationUID                  DcmTagKey(0x0040, 0xe011)
#define DCM_TypeOfInstances                      DcmTagKey(0x0040, 0xe020)
#define DCM_DICOMRetrievalSequence               DcmTagKey(0x0040, 0xe021)
#define DCM_DICOMMediaRetrievalSequence          DcmTagKey(0x0040, 0xe022)
#define DCM_WADORetrievalSequence                DcmTagKey(0x0040, 0xe023)
#define DCM_XDSRetrievalSequence                 DcmTagKey(0x0040, 0xe024)
#define DCM_WADORSRetrievalSequence              DcmTagKey(0x0040, 0xe025)
#define DCM_RepositoryUniqueID                   DcmTagKey(0x0040, 0xe030)
#define DCM_HomeCommunityID                      DcmTagKey(0x0040, 0xe031)
#define DCM_DocumentTitle                        DcmTagKey(0x0042, 0x0010)
#define DCM_EncapsulatedDocument                 DcmTagKey(0x0042, 0x0011)
#define DCM_MIMETypeOfEncapsulatedDocument       DcmTagKey(0x0042, 0x0012)
#define DCM_SourceInstanceSequence               DcmTagKey(0x0042, 0x0013)
#define DCM_ListOfMIMETypes                      DcmTagKey(0x0042, 0x0014)
#define DCM_EncapsulatedDocumentLength           DcmTagKey(0x0042, 0x0015)
#define DCM_ProductPackageIdentifier             DcmTagKey(0x0044, 0x0001)
#define DCM_SubstanceAdministrationApproval      DcmTagKey(0x0044, 0x0002)
#define DCM_ApprovalStatusFurtherDescription     DcmTagKey(0x0044, 0x0003)
#define DCM_ApprovalStatusDateTime               DcmTagKey(0x0044, 0x0004)
#define DCM_ProductTypeCodeSequence              DcmTagKey(0x0044, 0x0007)
#define DCM_ProductName                          DcmTagKey(0x0044, 0x0008)
#define DCM_ProductDescription                   DcmTagKey(0x0044, 0x0009)
#define DCM_ProductLotIdentifier                 DcmTagKey(0x0044, 0x000a)
#define DCM_ProductExpirationDateTime            DcmTagKey(0x0044, 0x000b)
#define DCM_SubstanceAdministrationDateTime      DcmTagKey(0x0044, 0x0010)
#define DCM_SubstanceAdministrationNotes         DcmTagKey(0x0044, 0x0011)
#define DCM_SubstanceAdministrationDeviceID      DcmTagKey(0x0044, 0x0012)
#define DCM_ProductParameterSequence             DcmTagKey(0x0044, 0x0013)
#define DCM_SubstanceAdministrationParameterSequence DcmTagKey(0x0044, 0x0019)
#define DCM_ApprovalSequence                     DcmTagKey(0x0044, 0x0100)
#define DCM_AssertionCodeSequence                DcmTagKey(0x0044, 0x0101)
#define DCM_AssertionUID                         DcmTagKey(0x0044, 0x0102)
#define DCM_AsserterIdentificationSequence       DcmTagKey(0x0044, 0x0103)
#define DCM_AssertionDateTime                    DcmTagKey(0x0044, 0x0104)
#define DCM_AssertionExpirationDateTime          DcmTagKey(0x0044, 0x0105)
#define DCM_AssertionComments                    DcmTagKey(0x0044, 0x0106)
#define DCM_RelatedAssertionSequence             DcmTagKey(0x0044, 0x0107)
#define DCM_ReferencedAssertionUID               DcmTagKey(0x0044, 0x0108)
#define DCM_ApprovalSubjectSequence              DcmTagKey(0x0044, 0x0109)
#define DCM_OrganizationalRoleCodeSequence       DcmTagKey(0x0044, 0x010a)
#define DCM_LensDescription                      DcmTagKey(0x0046, 0x0012)
#define DCM_RightLensSequence                    DcmTagKey(0x0046, 0x0014)
#define DCM_LeftLensSequence                     DcmTagKey(0x0046, 0x0015)
#define DCM_UnspecifiedLateralityLensSequence    DcmTagKey(0x0046, 0x0016)
#define DCM_CylinderSequence                     DcmTagKey(0x0046, 0x0018)
#define DCM_PrismSequence                        DcmTagKey(0x0046, 0x0028)
#define DCM_HorizontalPrismPower                 DcmTagKey(0x0046, 0x0030)
#define DCM_HorizontalPrismBase                  DcmTagKey(0x0046, 0x0032)
#define DCM_VerticalPrismPower                   DcmTagKey(0x0046, 0x0034)
#define DCM_VerticalPrismBase                    DcmTagKey(0x0046, 0x0036)
#define DCM_LensSegmentType                      DcmTagKey(0x0046, 0x0038)
#define DCM_OpticalTransmittance                 DcmTagKey(0x0046, 0x0040)
#define DCM_ChannelWidth                         DcmTagKey(0x0046, 0x0042)
#define DCM_PupilSize                            DcmTagKey(0x0046, 0x0044)
#define DCM_CornealSize                          DcmTagKey(0x0046, 0x0046)
#define DCM_CornealSizeSequence                  DcmTagKey(0x0046, 0x0047)
#define DCM_AutorefractionRightEyeSequence       DcmTagKey(0x0046, 0x0050)
#define DCM_AutorefractionLeftEyeSequence        DcmTagKey(0x0046, 0x0052)
#define DCM_DistancePupillaryDistance            DcmTagKey(0x0046, 0x0060)
#define DCM_NearPupillaryDistance                DcmTagKey(0x0046, 0x0062)
#define DCM_IntermediatePupillaryDistance        DcmTagKey(0x0046, 0x0063)
#define DCM_OtherPupillaryDistance               DcmTagKey(0x0046, 0x0064)
#define DCM_KeratometryRightEyeSequence          DcmTagKey(0x0046, 0x0070)
#define DCM_KeratometryLeftEyeSequence           DcmTagKey(0x0046, 0x0071)
#define DCM_SteepKeratometricAxisSequence        DcmTagKey(0x0046, 0x0074)
#define DCM_RadiusOfCurvature                    DcmTagKey(0x0046, 0x0075)
#define DCM_KeratometricPower                    DcmTagKey(0x0046, 0x0076)
#define DCM_KeratometricAxis                     DcmTagKey(0x0046, 0x0077)
#define DCM_FlatKeratometricAxisSequence         DcmTagKey(0x0046, 0x0080)
#define DCM_BackgroundColor                      DcmTagKey(0x0046, 0x0092)
#define DCM_Optotype                             DcmTagKey(0x0046, 0x0094)
#define DCM_OptotypePresentation                 DcmTagKey(0x0046, 0x0095)
#define DCM_SubjectiveRefractionRightEyeSequence DcmTagKey(0x0046, 0x0097)
#define DCM_SubjectiveRefractionLeftEyeSequence  DcmTagKey(0x0046, 0x0098)
#define DCM_AddNearSequence                      DcmTagKey(0x0046, 0x0100)
#define DCM_AddIntermediateSequence              DcmTagKey(0x0046, 0x0101)
#define DCM_AddOtherSequence                     DcmTagKey(0x0046, 0x0102)
#define DCM_AddPower                             DcmTagKey(0x0046, 0x0104)
#define DCM_ViewingDistance                      DcmTagKey(0x0046, 0x0106)
#define DCM_CorneaMeasurementsSequence           DcmTagKey(0x0046, 0x0110)
#define DCM_SourceOfCorneaMeasurementDataCodeSequence DcmTagKey(0x0046, 0x0111)
#define DCM_SteepCornealAxisSequence             DcmTagKey(0x0046, 0x0112)
#define DCM_FlatCornealAxisSequence              DcmTagKey(0x0046, 0x0113)
#define DCM_CornealPower                         DcmTagKey(0x0046, 0x0114)
#define DCM_CornealAxis                          DcmTagKey(0x0046, 0x0115)
#define DCM_CorneaMeasurementMethodCodeSequence  DcmTagKey(0x0046, 0x0116)
#define DCM_RefractiveIndexOfCornea              DcmTagKey(0x0046, 0x0117)
#define DCM_RefractiveIndexOfAqueousHumor        DcmTagKey(0x0046, 0x0118)
#define DCM_VisualAcuityTypeCodeSequence         DcmTagKey(0x0046, 0x0121)
#define DCM_VisualAcuityRightEyeSequence         DcmTagKey(0x0046, 0x0122)
#define DCM_VisualAcuityLeftEyeSequence          DcmTagKey(0x0046, 0x0123)
#define DCM_VisualAcuityBothEyesOpenSequence     DcmTagKey(0x0046, 0x0124)
#define DCM_ViewingDistanceType                  DcmTagKey(0x0046, 0x0125)
#define DCM_VisualAcuityModifiers                DcmTagKey(0x0046, 0x0135)
#define DCM_DecimalVisualAcuity                  DcmTagKey(0x0046, 0x0137)
#define DCM_OptotypeDetailedDefinition           DcmTagKey(0x0046, 0x0139)
#define DCM_ReferencedRefractiveMeasurementsSequence DcmTagKey(0x0046, 0x0145)
#define DCM_SpherePower                          DcmTagKey(0x0046, 0x0146)
#define DCM_CylinderPower                        DcmTagKey(0x0046, 0x0147)
#define DCM_CornealTopographySurface             DcmTagKey(0x0046, 0x0201)
#define DCM_CornealVertexLocation                DcmTagKey(0x0046, 0x0202)
#define DCM_PupilCentroidXCoordinate             DcmTagKey(0x0046, 0x0203)
#define DCM_PupilCentroidYCoordinate             DcmTagKey(0x0046, 0x0204)
#define DCM_EquivalentPupilRadius                DcmTagKey(0x0046, 0x0205)
#define DCM_CornealTopographyMapTypeCodeSequence DcmTagKey(0x0046, 0x0207)
#define DCM_VerticesOfTheOutlineOfPupil          DcmTagKey(0x0046, 0x0208)
#define DCM_CornealTopographyMappingNormalsSequence DcmTagKey(0x0046, 0x0210)
#define DCM_MaximumCornealCurvatureSequence      DcmTagKey(0x0046, 0x0211)
#define DCM_MaximumCornealCurvature              DcmTagKey(0x0046, 0x0212)
#define DCM_MaximumCornealCurvatureLocation      DcmTagKey(0x0046, 0x0213)
#define DCM_MinimumKeratometricSequence          DcmTagKey(0x0046, 0x0215)
#define DCM_SimulatedKeratometricCylinderSequence DcmTagKey(0x0046, 0x0218)
#define DCM_AverageCornealPower                  DcmTagKey(0x0046, 0x0220)
#define DCM_CornealISValue                       DcmTagKey(0x0046, 0x0224)
#define DCM_AnalyzedArea                         DcmTagKey(0x0046, 0x0227)
#define DCM_SurfaceRegularityIndex               DcmTagKey(0x0046, 0x0230)
#define DCM_SurfaceAsymmetryIndex                DcmTagKey(0x0046, 0x0232)
#define DCM_CornealEccentricityIndex             DcmTagKey(0x0046, 0x0234)
#define DCM_KeratoconusPredictionIndex           DcmTagKey(0x0046, 0x0236)
#define DCM_DecimalPotentialVisualAcuity         DcmTagKey(0x0046, 0x0238)
#define DCM_CornealTopographyMapQualityEvaluation DcmTagKey(0x0046, 0x0242)
#define DCM_SourceImageCornealProcessedDataSequence DcmTagKey(0x0046, 0x0244)
#define DCM_CornealPointLocation                 DcmTagKey(0x0046, 0x0247)
#define DCM_CornealPointEstimated                DcmTagKey(0x0046, 0x0248)
#define DCM_AxialPower                           DcmTagKey(0x0046, 0x0249)
#define DCM_TangentialPower                      DcmTagKey(0x0046, 0x0250)
#define DCM_RefractivePower                      DcmTagKey(0x0046, 0x0251)
#define DCM_RelativeElevation                    DcmTagKey(0x0046, 0x0252)
#define DCM_CornealWavefront                     DcmTagKey(0x0046, 0x0253)
#define DCM_ImagedVolumeWidth                    DcmTagKey(0x0048, 0x0001)
#define DCM_ImagedVolumeHeight                   DcmTagKey(0x0048, 0x0002)
#define DCM_ImagedVolumeDepth                    DcmTagKey(0x0048, 0x0003)
#define DCM_TotalPixelMatrixColumns              DcmTagKey(0x0048, 0x0006)
#define DCM_TotalPixelMatrixRows                 DcmTagKey(0x0048, 0x0007)
#define DCM_TotalPixelMatrixOriginSequence       DcmTagKey(0x0048, 0x0008)
#define DCM_SpecimenLabelInImage                 DcmTagKey(0x0048, 0x0010)
#define DCM_FocusMethod                          DcmTagKey(0x0048, 0x0011)
#define DCM_ExtendedDepthOfField                 DcmTagKey(0x0048, 0x0012)
#define DCM_NumberOfFocalPlanes                  DcmTagKey(0x0048, 0x0013)
#define DCM_DistanceBetweenFocalPlanes           DcmTagKey(0x0048, 0x0014)
#define DCM_RecommendedAbsentPixelCIELabValue    DcmTagKey(0x0048, 0x0015)
#define DCM_IlluminatorTypeCodeSequence          DcmTagKey(0x0048, 0x0100)
#define DCM_ImageOrientationSlide                DcmTagKey(0x0048, 0x0102)
#define DCM_OpticalPathSequence                  DcmTagKey(0x0048, 0x0105)
#define DCM_OpticalPathIdentifier                DcmTagKey(0x0048, 0x0106)
#define DCM_OpticalPathDescription               DcmTagKey(0x0048, 0x0107)
#define DCM_IlluminationColorCodeSequence        DcmTagKey(0x0048, 0x0108)
#define DCM_SpecimenReferenceSequence            DcmTagKey(0x0048, 0x0110)
#define DCM_CondenserLensPower                   DcmTagKey(0x0048, 0x0111)
#define DCM_ObjectiveLensPower                   DcmTagKey(0x0048, 0x0112)
#define DCM_ObjectiveLensNumericalAperture       DcmTagKey(0x0048, 0x0113)
#define DCM_ConfocalMode                         DcmTagKey(0x0048, 0x0114)
#define DCM_TissueLocation                       DcmTagKey(0x0048, 0x0115)
#define DCM_ConfocalMicroscopyImageFrameTypeSequence DcmTagKey(0x0048, 0x0116)
#define DCM_ImageAcquisitionDepth                DcmTagKey(0x0048, 0x0117)
#define DCM_PaletteColorLookupTableSequence      DcmTagKey(0x0048, 0x0120)
#define DCM_RETIRED_ReferencedImageNavigationSequence DcmTagKey(0x0048, 0x0200)
#define DCM_RETIRED_TopLeftHandCornerOfLocalizerArea DcmTagKey(0x0048, 0x0201)
#define DCM_RETIRED_BottomRightHandCornerOfLocalizerArea DcmTagKey(0x0048, 0x0202)
#define DCM_OpticalPathIdentificationSequence    DcmTagKey(0x0048, 0x0207)
#define DCM_PlanePositionSlideSequence           DcmTagKey(0x0048, 0x021a)
#define DCM_ColumnPositionInTotalImagePixelMatrix DcmTagKey(0x0048, 0x021e)
#define DCM_RowPositionInTotalImagePixelMatrix   DcmTagKey(0x0048, 0x021f)
#define DCM_PixelOriginInterpretation            DcmTagKey(0x0048, 0x0301)
#define DCM_NumberOfOpticalPaths                 DcmTagKey(0x0048, 0x0302)
#define DCM_TotalPixelMatrixFocalPlanes          DcmTagKey(0x0048, 0x0303)
#define DCM_CalibrationImage                     DcmTagKey(0x0050, 0x0004)
#define DCM_DeviceSequence                       DcmTagKey(0x0050, 0x0010)
#define DCM_ContainerComponentTypeCodeSequence   DcmTagKey(0x0050, 0x0012)
#define DCM_ContainerComponentThickness          DcmTagKey(0x0050, 0x0013)
#define DCM_DeviceLength                         DcmTagKey(0x0050, 0x0014)
#define DCM_ContainerComponentWidth              DcmTagKey(0x0050, 0x0015)
#define DCM_DeviceDiameter                       DcmTagKey(0x0050, 0x0016)
#define DCM_DeviceDiameterUnits                  DcmTagKey(0x0050, 0x0017)
#define DCM_DeviceVolume                         DcmTagKey(0x0050, 0x0018)
#define DCM_InterMarkerDistance                  DcmTagKey(0x0050, 0x0019)
#define DCM_ContainerComponentMaterial           DcmTagKey(0x0050, 0x001a)
#define DCM_ContainerComponentID                 DcmTagKey(0x0050, 0x001b)
#define DCM_ContainerComponentLength             DcmTagKey(0x0050, 0x001c)
#define DCM_ContainerComponentDiameter           DcmTagKey(0x0050, 0x001d)
#define DCM_ContainerComponentDescription        DcmTagKey(0x0050, 0x001e)
#define DCM_DeviceDescription                    DcmTagKey(0x0050, 0x0020)
#define DCM_LongDeviceDescription                DcmTagKey(0x0050, 0x0021)
#define DCM_ContrastBolusIngredientPercentByVolume DcmTagKey(0x0052, 0x0001)
#define DCM_OCTFocalDistance                     DcmTagKey(0x0052, 0x0002)
#define DCM_BeamSpotSize                         DcmTagKey(0x0052, 0x0003)
#define DCM_EffectiveRefractiveIndex             DcmTagKey(0x0052, 0x0004)
#define DCM_OCTAcquisitionDomain                 DcmTagKey(0x0052, 0x0006)
#define DCM_OCTOpticalCenterWavelength           DcmTagKey(0x0052, 0x0007)
#define DCM_AxialResolution                      DcmTagKey(0x0052, 0x0008)
#define DCM_RangingDepth                         DcmTagKey(0x0052, 0x0009)
#define DCM_ALineRate                            DcmTagKey(0x0052, 0x0011)
#define DCM_ALinesPerFrame                       DcmTagKey(0x0052, 0x0012)
#define DCM_CatheterRotationalRate               DcmTagKey(0x0052, 0x0013)
#define DCM_ALinePixelSpacing                    DcmTagKey(0x0052, 0x0014)
#define DCM_ModeOfPercutaneousAccessSequence     DcmTagKey(0x0052, 0x0016)
#define DCM_IntravascularOCTFrameTypeSequence    DcmTagKey(0x0052, 0x0025)
#define DCM_OCTZOffsetApplied                    DcmTagKey(0x0052, 0x0026)
#define DCM_IntravascularFrameContentSequence    DcmTagKey(0x0052, 0x0027)
#define DCM_IntravascularLongitudinalDistance    DcmTagKey(0x0052, 0x0028)
#define DCM_IntravascularOCTFrameContentSequence DcmTagKey(0x0052, 0x0029)
#define DCM_OCTZOffsetCorrection                 DcmTagKey(0x0052, 0x0030)
#define DCM_CatheterDirectionOfRotation          DcmTagKey(0x0052, 0x0031)
#define DCM_SeamLineLocation                     DcmTagKey(0x0052, 0x0033)
#define DCM_FirstALineLocation                   DcmTagKey(0x0052, 0x0034)
#define DCM_SeamLineIndex                        DcmTagKey(0x0052, 0x0036)
#define DCM_NumberOfPaddedALines                 DcmTagKey(0x0052, 0x0038)
#define DCM_InterpolationType                    DcmTagKey(0x0052, 0x0039)
#define DCM_RefractiveIndexApplied               DcmTagKey(0x0052, 0x003a)
#define DCM_EnergyWindowVector                   DcmTagKey(0x0054, 0x0010)
#define DCM_NumberOfEnergyWindows                DcmTagKey(0x0054, 0x0011)
#define DCM_EnergyWindowInformationSequence      DcmTagKey(0x0054, 0x0012)
#define DCM_EnergyWindowRangeSequence            DcmTagKey(0x0054, 0x0013)
#define DCM_EnergyWindowLowerLimit               DcmTagKey(0x0054, 0x0014)
#define DCM_EnergyWindowUpperLimit               DcmTagKey(0x0054, 0x0015)
#define DCM_RadiopharmaceuticalInformationSequence DcmTagKey(0x0054, 0x0016)
#define DCM_ResidualSyringeCounts                DcmTagKey(0x0054, 0x0017)
#define DCM_EnergyWindowName                     DcmTagKey(0x0054, 0x0018)
#define DCM_DetectorVector                       DcmTagKey(0x0054, 0x0020)
#define DCM_NumberOfDetectors                    DcmTagKey(0x0054, 0x0021)
#define DCM_DetectorInformationSequence          DcmTagKey(0x0054, 0x0022)
#define DCM_PhaseVector                          DcmTagKey(0x0054, 0x0030)
#define DCM_NumberOfPhases                       DcmTagKey(0x0054, 0x0031)
#define DCM_PhaseInformationSequence             DcmTagKey(0x0054, 0x0032)
#define DCM_NumberOfFramesInPhase                DcmTagKey(0x0054, 0x0033)
#define DCM_PhaseDelay                           DcmTagKey(0x0054, 0x0036)
#define DCM_PauseBetweenFrames                   DcmTagKey(0x0054, 0x0038)
#define DCM_PhaseDescription                     DcmTagKey(0x0054, 0x0039)
#define DCM_RotationVector                       DcmTagKey(0x0054, 0x0050)
#define DCM_NumberOfRotations                    DcmTagKey(0x0054, 0x0051)
#define DCM_RotationInformationSequence          DcmTagKey(0x0054, 0x0052)
#define DCM_NumberOfFramesInRotation             DcmTagKey(0x0054, 0x0053)
#define DCM_RRIntervalVector                     DcmTagKey(0x0054, 0x0060)
#define DCM_NumberOfRRIntervals                  DcmTagKey(0x0054, 0x0061)
#define DCM_GatedInformationSequence             DcmTagKey(0x0054, 0x0062)
#define DCM_DataInformationSequence              DcmTagKey(0x0054, 0x0063)
#define DCM_TimeSlotVector                       DcmTagKey(0x0054, 0x0070)
#define DCM_NumberOfTimeSlots                    DcmTagKey(0x0054, 0x0071)
#define DCM_TimeSlotInformationSequence          DcmTagKey(0x0054, 0x0072)
#define DCM_TimeSlotTime                         DcmTagKey(0x0054, 0x0073)
#define DCM_SliceVector                          DcmTagKey(0x0054, 0x0080)
#define DCM_NumberOfSlices                       DcmTagKey(0x0054, 0x0081)
#define DCM_AngularViewVector                    DcmTagKey(0x0054, 0x0090)
#define DCM_TimeSliceVector                      DcmTagKey(0x0054, 0x0100)
#define DCM_NumberOfTimeSlices                   DcmTagKey(0x0054, 0x0101)
#define DCM_StartAngle                           DcmTagKey(0x0054, 0x0200)
#define DCM_TypeOfDetectorMotion                 DcmTagKey(0x0054, 0x0202)
#define DCM_TriggerVector                        DcmTagKey(0x0054, 0x0210)
#define DCM_NumberOfTriggersInPhase              DcmTagKey(0x0054, 0x0211)
#define DCM_ViewCodeSequence                     DcmTagKey(0x0054, 0x0220)
#define DCM_ViewModifierCodeSequence             DcmTagKey(0x0054, 0x0222)
#define DCM_RadionuclideCodeSequence             DcmTagKey(0x0054, 0x0300)
#define DCM_AdministrationRouteCodeSequence      DcmTagKey(0x0054, 0x0302)
#define DCM_RadiopharmaceuticalCodeSequence      DcmTagKey(0x0054, 0x0304)
#define DCM_CalibrationDataSequence              DcmTagKey(0x0054, 0x0306)
#define DCM_EnergyWindowNumber                   DcmTagKey(0x0054, 0x0308)
#define DCM_ImageID                              DcmTagKey(0x0054, 0x0400)
#define DCM_PatientOrientationCodeSequence       DcmTagKey(0x0054, 0x0410)
#define DCM_PatientOrientationModifierCodeSequence DcmTagKey(0x0054, 0x0412)
#define DCM_PatientGantryRelationshipCodeSequence DcmTagKey(0x0054, 0x0414)
#define DCM_SliceProgressionDirection            DcmTagKey(0x0054, 0x0500)
#define DCM_ScanProgressionDirection             DcmTagKey(0x0054, 0x0501)
#define DCM_SeriesType                           DcmTagKey(0x0054, 0x1000)
#define DCM_Units                                DcmTagKey(0x0054, 0x1001)
#define DCM_CountsSource                         DcmTagKey(0x0054, 0x1002)
#define DCM_ReprojectionMethod                   DcmTagKey(0x0054, 0x1004)
#define DCM_SUVType                              DcmTagKey(0x0054, 0x1006)
#define DCM_RandomsCorrectionMethod              DcmTagKey(0x0054, 0x1100)
#define DCM_AttenuationCorrectionMethod          DcmTagKey(0x0054, 0x1101)
#define DCM_DecayCorrection                      DcmTagKey(0x0054, 0x1102)
#define DCM_ReconstructionMethod                 DcmTagKey(0x0054, 0x1103)
#define DCM_DetectorLinesOfResponseUsed          DcmTagKey(0x0054, 0x1104)
#define DCM_ScatterCorrectionMethod              DcmTagKey(0x0054, 0x1105)
#define DCM_AxialAcceptance                      DcmTagKey(0x0054, 0x1200)
#define DCM_AxialMash                            DcmTagKey(0x0054, 0x1201)
#define DCM_TransverseMash                       DcmTagKey(0x0054, 0x1202)
#define DCM_DetectorElementSize                  DcmTagKey(0x0054, 0x1203)
#define DCM_CoincidenceWindowWidth               DcmTagKey(0x0054, 0x1210)
#define DCM_SecondaryCountsType                  DcmTagKey(0x0054, 0x1220)
#define DCM_FrameReferenceTime                   DcmTagKey(0x0054, 0x1300)
#define DCM_PrimaryPromptsCountsAccumulated      DcmTagKey(0x0054, 0x1310)
#define DCM_SecondaryCountsAccumulated           DcmTagKey(0x0054, 0x1311)
#define DCM_SliceSensitivityFactor               DcmTagKey(0x0054, 0x1320)
#define DCM_DecayFactor                          DcmTagKey(0x0054, 0x1321)
#define DCM_DoseCalibrationFactor                DcmTagKey(0x0054, 0x1322)
#define DCM_ScatterFractionFactor                DcmTagKey(0x0054, 0x1323)
#define DCM_DeadTimeFactor                       DcmTagKey(0x0054, 0x1324)
#define DCM_ImageIndex                           DcmTagKey(0x0054, 0x1330)
#define DCM_RETIRED_CountsIncluded               DcmTagKey(0x0054, 0x1400)
#define DCM_RETIRED_DeadTimeCorrectionFlag       DcmTagKey(0x0054, 0x1401)
#define DCM_HistogramSequence                    DcmTagKey(0x0060, 0x3000)
#define DCM_HistogramNumberOfBins                DcmTagKey(0x0060, 0x3002)
#define DCM_HistogramFirstBinValue               DcmTagKey(0x0060, 0x3004)
#define DCM_HistogramLastBinValue                DcmTagKey(0x0060, 0x3006)
#define DCM_HistogramBinWidth                    DcmTagKey(0x0060, 0x3008)
#define DCM_HistogramExplanation                 DcmTagKey(0x0060, 0x3010)
#define DCM_HistogramData                        DcmTagKey(0x0060, 0x3020)
#define DCM_SegmentationType                     DcmTagKey(0x0062, 0x0001)
#define DCM_SegmentSequence                      DcmTagKey(0x0062, 0x0002)
#define DCM_SegmentedPropertyCategoryCodeSequence DcmTagKey(0x0062, 0x0003)
#define DCM_SegmentNumber                        DcmTagKey(0x0062, 0x0004)
#define DCM_SegmentLabel                         DcmTagKey(0x0062, 0x0005)
#define DCM_SegmentDescription                   DcmTagKey(0x0062, 0x0006)
#define DCM_SegmentationAlgorithmIdentificationSequence DcmTagKey(0x0062, 0x0007)
#define DCM_SegmentAlgorithmType                 DcmTagKey(0x0062, 0x0008)
#define DCM_SegmentAlgorithmName                 DcmTagKey(0x0062, 0x0009)
#define DCM_SegmentIdentificationSequence        DcmTagKey(0x0062, 0x000a)
#define DCM_ReferencedSegmentNumber              DcmTagKey(0x0062, 0x000b)
#define DCM_RecommendedDisplayGrayscaleValue     DcmTagKey(0x0062, 0x000c)
#define DCM_RecommendedDisplayCIELabValue        DcmTagKey(0x0062, 0x000d)
#define DCM_MaximumFractionalValue               DcmTagKey(0x0062, 0x000e)
#define DCM_SegmentedPropertyTypeCodeSequence    DcmTagKey(0x0062, 0x000f)
#define DCM_SegmentationFractionalType           DcmTagKey(0x0062, 0x0010)
#define DCM_SegmentedPropertyTypeModifierCodeSequence DcmTagKey(0x0062, 0x0011)
#define DCM_UsedSegmentsSequence                 DcmTagKey(0x0062, 0x0012)
#define DCM_SegmentsOverlap                      DcmTagKey(0x0062, 0x0013)
#define DCM_TrackingID                           DcmTagKey(0x0062, 0x0020)
#define DCM_TrackingUID                          DcmTagKey(0x0062, 0x0021)
#define DCM_DeformableRegistrationSequence       DcmTagKey(0x0064, 0x0002)
#define DCM_SourceFrameOfReferenceUID            DcmTagKey(0x0064, 0x0003)
#define DCM_DeformableRegistrationGridSequence   DcmTagKey(0x0064, 0x0005)
#define DCM_GridDimensions                       DcmTagKey(0x0064, 0x0007)
#define DCM_GridResolution                       DcmTagKey(0x0064, 0x0008)
#define DCM_VectorGridData                       DcmTagKey(0x0064, 0x0009)
#define DCM_PreDeformationMatrixRegistrationSequence DcmTagKey(0x0064, 0x000f)
#define DCM_PostDeformationMatrixRegistrationSequence DcmTagKey(0x0064, 0x0010)
#define DCM_NumberOfSurfaces                     DcmTagKey(0x0066, 0x0001)
#define DCM_SurfaceSequence                      DcmTagKey(0x0066, 0x0002)
#define DCM_SurfaceNumber                        DcmTagKey(0x0066, 0x0003)
#define DCM_SurfaceComments                      DcmTagKey(0x0066, 0x0004)
#define DCM_SurfaceOffset                        DcmTagKey(0x0066, 0x0005)
#define DCM_SurfaceProcessing                    DcmTagKey(0x0066, 0x0009)
#define DCM_SurfaceProcessingRatio               DcmTagKey(0x0066, 0x000a)
#define DCM_SurfaceProcessingDescription         DcmTagKey(0x0066, 0x000b)
#define DCM_RecommendedPresentationOpacity       DcmTagKey(0x0066, 0x000c)
#define DCM_RecommendedPresentationType          DcmTagKey(0x0066, 0x000d)
#define DCM_FiniteVolume                         DcmTagKey(0x0066, 0x000e)
#define DCM_Manifold                             DcmTagKey(0x0066, 0x0010)
#define DCM_SurfacePointsSequence                DcmTagKey(0x0066, 0x0011)
#define DCM_SurfacePointsNormalsSequence         DcmTagKey(0x0066, 0x0012)
#define DCM_SurfaceMeshPrimitivesSequence        DcmTagKey(0x0066, 0x0013)
#define DCM_NumberOfSurfacePoints                DcmTagKey(0x0066, 0x0015)
#define DCM_PointCoordinatesData                 DcmTagKey(0x0066, 0x0016)
#define DCM_PointPositionAccuracy                DcmTagKey(0x0066, 0x0017)
#define DCM_MeanPointDistance                    DcmTagKey(0x0066, 0x0018)
#define DCM_MaximumPointDistance                 DcmTagKey(0x0066, 0x0019)
#define DCM_PointsBoundingBoxCoordinates         DcmTagKey(0x0066, 0x001a)
#define DCM_AxisOfRotation                       DcmTagKey(0x0066, 0x001b)
#define DCM_CenterOfRotation                     DcmTagKey(0x0066, 0x001c)
#define DCM_NumberOfVectors                      DcmTagKey(0x0066, 0x001e)
#define DCM_VectorDimensionality                 DcmTagKey(0x0066, 0x001f)
#define DCM_VectorAccuracy                       DcmTagKey(0x0066, 0x0020)
#define DCM_VectorCoordinateData                 DcmTagKey(0x0066, 0x0021)
#define DCM_DoublePointCoordinatesData           DcmTagKey(0x0066, 0x0022)
#define DCM_RETIRED_TrianglePointIndexList       DcmTagKey(0x0066, 0x0023)
#define DCM_RETIRED_EdgePointIndexList           DcmTagKey(0x0066, 0x0024)
#define DCM_RETIRED_VertexPointIndexList         DcmTagKey(0x0066, 0x0025)
#define DCM_TriangleStripSequence                DcmTagKey(0x0066, 0x0026)
#define DCM_TriangleFanSequence                  DcmTagKey(0x0066, 0x0027)
#define DCM_LineSequence                         DcmTagKey(0x0066, 0x0028)
#define DCM_RETIRED_PrimitivePointIndexList      DcmTagKey(0x0066, 0x0029)
#define DCM_SurfaceCount                         DcmTagKey(0x0066, 0x002a)
#define DCM_ReferencedSurfaceSequence            DcmTagKey(0x0066, 0x002b)
#define DCM_ReferencedSurfaceNumber              DcmTagKey(0x0066, 0x002c)
#define DCM_SegmentSurfaceGenerationAlgorithmIdentificationSequence DcmTagKey(0x0066, 0x002d)
#define DCM_SegmentSurfaceSourceInstanceSequence DcmTagKey(0x0066, 0x002e)
#define DCM_AlgorithmFamilyCodeSequence          DcmTagKey(0x0066, 0x002f)
#define DCM_AlgorithmNameCodeSequence            DcmTagKey(0x0066, 0x0030)
#define DCM_AlgorithmVersion                     DcmTagKey(0x0066, 0x0031)
#define DCM_AlgorithmParameters                  DcmTagKey(0x0066, 0x0032)
#define DCM_FacetSequence                        DcmTagKey(0x0066, 0x0034)
#define DCM_SurfaceProcessingAlgorithmIdentificationSequence DcmTagKey(0x0066, 0x0035)
#define DCM_AlgorithmName                        DcmTagKey(0x0066, 0x0036)
#define DCM_RecommendedPointRadius               DcmTagKey(0x0066, 0x0037)
#define DCM_RecommendedLineThickness             DcmTagKey(0x0066, 0x0038)
#define DCM_LongPrimitivePointIndexList          DcmTagKey(0x0066, 0x0040)
#define DCM_LongTrianglePointIndexList           DcmTagKey(0x0066, 0x0041)
#define DCM_LongEdgePointIndexList               DcmTagKey(0x0066, 0x0042)
#define DCM_LongVertexPointIndexList             DcmTagKey(0x0066, 0x0043)
#define DCM_TrackSetSequence                     DcmTagKey(0x0066, 0x0101)
#define DCM_TrackSequence                        DcmTagKey(0x0066, 0x0102)
#define DCM_RecommendedDisplayCIELabValueList    DcmTagKey(0x0066, 0x0103)
#define DCM_TrackingAlgorithmIdentificationSequence DcmTagKey(0x0066, 0x0104)
#define DCM_TrackSetNumber                       DcmTagKey(0x0066, 0x0105)
#define DCM_TrackSetLabel                        DcmTagKey(0x0066, 0x0106)
#define DCM_TrackSetDescription                  DcmTagKey(0x0066, 0x0107)
#define DCM_TrackSetAnatomicalTypeCodeSequence   DcmTagKey(0x0066, 0x0108)
#define DCM_MeasurementsSequence                 DcmTagKey(0x0066, 0x0121)
#define DCM_TrackSetStatisticsSequence           DcmTagKey(0x0066, 0x0124)
#define DCM_FloatingPointValues                  DcmTagKey(0x0066, 0x0125)
#define DCM_TrackPointIndexList                  DcmTagKey(0x0066, 0x0129)
#define DCM_TrackStatisticsSequence              DcmTagKey(0x0066, 0x0130)
#define DCM_MeasurementValuesSequence            DcmTagKey(0x0066, 0x0132)
#define DCM_DiffusionAcquisitionCodeSequence     DcmTagKey(0x0066, 0x0133)
#define DCM_DiffusionModelCodeSequence           DcmTagKey(0x0066, 0x0134)
#define DCM_ImplantSize                          DcmTagKey(0x0068, 0x6210)
#define DCM_ImplantTemplateVersion               DcmTagKey(0x0068, 0x6221)
#define DCM_ReplacedImplantTemplateSequence      DcmTagKey(0x0068, 0x6222)
#define DCM_ImplantType                          DcmTagKey(0x0068, 0x6223)
#define DCM_DerivationImplantTemplateSequence    DcmTagKey(0x0068, 0x6224)
#define DCM_OriginalImplantTemplateSequence      DcmTagKey(0x0068, 0x6225)
#define DCM_EffectiveDateTime                    DcmTagKey(0x0068, 0x6226)
#define DCM_ImplantTargetAnatomySequence         DcmTagKey(0x0068, 0x6230)
#define DCM_InformationFromManufacturerSequence  DcmTagKey(0x0068, 0x6260)
#define DCM_NotificationFromManufacturerSequence DcmTagKey(0x0068, 0x6265)
#define DCM_InformationIssueDateTime             DcmTagKey(0x0068, 0x6270)
#define DCM_InformationSummary                   DcmTagKey(0x0068, 0x6280)
#define DCM_ImplantRegulatoryDisapprovalCodeSequence DcmTagKey(0x0068, 0x62a0)
#define DCM_OverallTemplateSpatialTolerance      DcmTagKey(0x0068, 0x62a5)
#define DCM_HPGLDocumentSequence                 DcmTagKey(0x0068, 0x62c0)
#define DCM_HPGLDocumentID                       DcmTagKey(0x0068, 0x62d0)
#define DCM_HPGLDocumentLabel                    DcmTagKey(0x0068, 0x62d5)
#define DCM_ViewOrientationCodeSequence          DcmTagKey(0x0068, 0x62e0)
#define DCM_ViewOrientationModifierCodeSequence  DcmTagKey(0x0068, 0x62f0)
#define DCM_HPGLDocumentScaling                  DcmTagKey(0x0068, 0x62f2)
#define DCM_HPGLDocument                         DcmTagKey(0x0068, 0x6300)
#define DCM_HPGLContourPenNumber                 DcmTagKey(0x0068, 0x6310)
#define DCM_HPGLPenSequence                      DcmTagKey(0x0068, 0x6320)
#define DCM_HPGLPenNumber                        DcmTagKey(0x0068, 0x6330)
#define DCM_HPGLPenLabel                         DcmTagKey(0x0068, 0x6340)
#define DCM_HPGLPenDescription                   DcmTagKey(0x0068, 0x6345)
#define DCM_RecommendedRotationPoint             DcmTagKey(0x0068, 0x6346)
#define DCM_BoundingRectangle                    DcmTagKey(0x0068, 0x6347)
#define DCM_ImplantTemplate3DModelSurfaceNumber  DcmTagKey(0x0068, 0x6350)
#define DCM_SurfaceModelDescriptionSequence      DcmTagKey(0x0068, 0x6360)
#define DCM_SurfaceModelLabel                    DcmTagKey(0x0068, 0x6380)
#define DCM_SurfaceModelScalingFactor            DcmTagKey(0x0068, 0x6390)
#define DCM_MaterialsCodeSequence                DcmTagKey(0x0068, 0x63a0)
#define DCM_CoatingMaterialsCodeSequence         DcmTagKey(0x0068, 0x63a4)
#define DCM_ImplantTypeCodeSequence              DcmTagKey(0x0068, 0x63a8)
#define DCM_FixationMethodCodeSequence           DcmTagKey(0x0068, 0x63ac)
#define DCM_MatingFeatureSetsSequence            DcmTagKey(0x0068, 0x63b0)
#define DCM_MatingFeatureSetID                   DcmTagKey(0x0068, 0x63c0)
#define DCM_MatingFeatureSetLabel                DcmTagKey(0x0068, 0x63d0)
#define DCM_MatingFeatureSequence                DcmTagKey(0x0068, 0x63e0)
#define DCM_MatingFeatureID                      DcmTagKey(0x0068, 0x63f0)
#define DCM_MatingFeatureDegreeOfFreedomSequence DcmTagKey(0x0068, 0x6400)
#define DCM_DegreeOfFreedomID                    DcmTagKey(0x0068, 0x6410)
#define DCM_DegreeOfFreedomType                  DcmTagKey(0x0068, 0x6420)
#define DCM_TwoDMatingFeatureCoordinatesSequence DcmTagKey(0x0068, 0x6430)
#define DCM_ReferencedHPGLDocumentID             DcmTagKey(0x0068, 0x6440)
#define DCM_TwoDMatingPoint                      DcmTagKey(0x0068, 0x6450)
#define DCM_TwoDMatingAxes                       DcmTagKey(0x0068, 0x6460)
#define DCM_TwoDDegreeOfFreedomSequence          DcmTagKey(0x0068, 0x6470)
#define DCM_ThreeDDegreeOfFreedomAxis            DcmTagKey(0x0068, 0x6490)
#define DCM_RangeOfFreedom                       DcmTagKey(0x0068, 0x64a0)
#define DCM_ThreeDMatingPoint                    DcmTagKey(0x0068, 0x64c0)
#define DCM_ThreeDMatingAxes                     DcmTagKey(0x0068, 0x64d0)
#define DCM_TwoDDegreeOfFreedomAxis              DcmTagKey(0x0068, 0x64f0)
#define DCM_PlanningLandmarkPointSequence        DcmTagKey(0x0068, 0x6500)
#define DCM_PlanningLandmarkLineSequence         DcmTagKey(0x0068, 0x6510)
#define DCM_PlanningLandmarkPlaneSequence        DcmTagKey(0x0068, 0x6520)
#define DCM_PlanningLandmarkID                   DcmTagKey(0x0068, 0x6530)
#define DCM_PlanningLandmarkDescription          DcmTagKey(0x0068, 0x6540)
#define DCM_PlanningLandmarkIdentificationCodeSequence DcmTagKey(0x0068, 0x6545)
#define DCM_TwoDPointCoordinatesSequence         DcmTagKey(0x0068, 0x6550)
#define DCM_TwoDPointCoordinates                 DcmTagKey(0x0068, 0x6560)
#define DCM_ThreeDPointCoordinates               DcmTagKey(0x0068, 0x6590)
#define DCM_TwoDLineCoordinatesSequence          DcmTagKey(0x0068, 0x65a0)
#define DCM_TwoDLineCoordinates                  DcmTagKey(0x0068, 0x65b0)
#define DCM_ThreeDLineCoordinates                DcmTagKey(0x0068, 0x65d0)
#define DCM_TwoDPlaneCoordinatesSequence         DcmTagKey(0x0068, 0x65e0)
#define DCM_TwoDPlaneIntersection                DcmTagKey(0x0068, 0x65f0)
#define DCM_ThreeDPlaneOrigin                    DcmTagKey(0x0068, 0x6610)
#define DCM_ThreeDPlaneNormal                    DcmTagKey(0x0068, 0x6620)
#define DCM_ModelModification                    DcmTagKey(0x0068, 0x7001)
#define DCM_ModelMirroring                       DcmTagKey(0x0068, 0x7002)
#define DCM_ModelUsageCodeSequence               DcmTagKey(0x0068, 0x7003)
#define DCM_ModelGroupUID                        DcmTagKey(0x0068, 0x7004)
#define DCM_RelativeURIReferenceWithinEncapsulatedDocument DcmTagKey(0x0068, 0x7005)
#define DCM_AnnotationCoordinateType             DcmTagKey(0x006a, 0x0001)
#define DCM_AnnotationGroupSequence              DcmTagKey(0x006a, 0x0002)
#define DCM_AnnotationGroupUID                   DcmTagKey(0x006a, 0x0003)
#define DCM_AnnotationGroupLabel                 DcmTagKey(0x006a, 0x0005)
#define DCM_AnnotationGroupDescription           DcmTagKey(0x006a, 0x0006)
#define DCM_AnnotationGroupGenerationType        DcmTagKey(0x006a, 0x0007)
#define DCM_AnnotationGroupAlgorithmIdentificationSequence DcmTagKey(0x006a, 0x0008)
#define DCM_AnnotationPropertyCategoryCodeSequence DcmTagKey(0x006a, 0x0009)
#define DCM_AnnotationPropertyTypeCodeSequence   DcmTagKey(0x006a, 0x000a)
#define DCM_AnnotationPropertyTypeModifierCodeSequence DcmTagKey(0x006a, 0x000b)
#define DCM_NumberOfAnnotations                  DcmTagKey(0x006a, 0x000c)
#define DCM_AnnotationAppliesToAllOpticalPaths   DcmTagKey(0x006a, 0x000d)
#define DCM_ReferencedOpticalPathIdentifier      DcmTagKey(0x006a, 0x000e)
#define DCM_AnnotationAppliesToAllZPlanes        DcmTagKey(0x006a, 0x000f)
#define DCM_CommonZCoordinateValue               DcmTagKey(0x006a, 0x0010)
#define DCM_AnnotationIndexList                  DcmTagKey(0x006a, 0x0011)
#define DCM_GraphicAnnotationSequence            DcmTagKey(0x0070, 0x0001)
#define DCM_GraphicLayer                         DcmTagKey(0x0070, 0x0002)
#define DCM_BoundingBoxAnnotationUnits           DcmTagKey(0x0070, 0x0003)
#define DCM_AnchorPointAnnotationUnits           DcmTagKey(0x0070, 0x0004)
#define DCM_GraphicAnnotationUnits               DcmTagKey(0x0070, 0x0005)
#define DCM_UnformattedTextValue                 DcmTagKey(0x0070, 0x0006)
#define DCM_TextObjectSequence                   DcmTagKey(0x0070, 0x0008)
#define DCM_GraphicObjectSequence                DcmTagKey(0x0070, 0x0009)
#define DCM_BoundingBoxTopLeftHandCorner         DcmTagKey(0x0070, 0x0010)
#define DCM_BoundingBoxBottomRightHandCorner     DcmTagKey(0x0070, 0x0011)
#define DCM_BoundingBoxTextHorizontalJustification DcmTagKey(0x0070, 0x0012)
#define DCM_AnchorPoint                          DcmTagKey(0x0070, 0x0014)
#define DCM_AnchorPointVisibility                DcmTagKey(0x0070, 0x0015)
#define DCM_GraphicDimensions                    DcmTagKey(0x0070, 0x0020)
#define DCM_NumberOfGraphicPoints                DcmTagKey(0x0070, 0x0021)
#define DCM_GraphicData                          DcmTagKey(0x0070, 0x0022)
#define DCM_GraphicType                          DcmTagKey(0x0070, 0x0023)
#define DCM_GraphicFilled                        DcmTagKey(0x0070, 0x0024)
#define DCM_RETIRED_ImageRotationRetired         DcmTagKey(0x0070, 0x0040)
#define DCM_ImageHorizontalFlip                  DcmTagKey(0x0070, 0x0041)
#define DCM_ImageRotation                        DcmTagKey(0x0070, 0x0042)
#define DCM_RETIRED_DisplayedAreaTopLeftHandCornerTrial DcmTagKey(0x0070, 0x0050)
#define DCM_RETIRED_DisplayedAreaBottomRightHandCornerTrial DcmTagKey(0x0070, 0x0051)
#define DCM_DisplayedAreaTopLeftHandCorner       DcmTagKey(0x0070, 0x0052)
#define DCM_DisplayedAreaBottomRightHandCorner   DcmTagKey(0x0070, 0x0053)
#define DCM_DisplayedAreaSelectionSequence       DcmTagKey(0x0070, 0x005a)
#define DCM_GraphicLayerSequence                 DcmTagKey(0x0070, 0x0060)
#define DCM_GraphicLayerOrder                    DcmTagKey(0x0070, 0x0062)
#define DCM_GraphicLayerRecommendedDisplayGrayscaleValue DcmTagKey(0x0070, 0x0066)
#define DCM_RETIRED_GraphicLayerRecommendedDisplayRGBValue DcmTagKey(0x0070, 0x0067)
#define DCM_GraphicLayerDescription              DcmTagKey(0x0070, 0x0068)
#define DCM_ContentLabel                         DcmTagKey(0x0070, 0x0080)
#define DCM_ContentDescription                   DcmTagKey(0x0070, 0x0081)
#define DCM_PresentationCreationDate             DcmTagKey(0x0070, 0x0082)
#define DCM_PresentationCreationTime             DcmTagKey(0x0070, 0x0083)
#define DCM_ContentCreatorName                   DcmTagKey(0x0070, 0x0084)
#define DCM_ContentCreatorIdentificationCodeSequence DcmTagKey(0x0070, 0x0086)
#define DCM_AlternateContentDescriptionSequence  DcmTagKey(0x0070, 0x0087)
#define DCM_PresentationSizeMode                 DcmTagKey(0x0070, 0x0100)
#define DCM_PresentationPixelSpacing             DcmTagKey(0x0070, 0x0101)
#define DCM_PresentationPixelAspectRatio         DcmTagKey(0x0070, 0x0102)
#define DCM_PresentationPixelMagnificationRatio  DcmTagKey(0x0070, 0x0103)
#define DCM_GraphicGroupLabel                    DcmTagKey(0x0070, 0x0207)
#define DCM_GraphicGroupDescription              DcmTagKey(0x0070, 0x0208)
#define DCM_CompoundGraphicSequence              DcmTagKey(0x0070, 0x0209)
#define DCM_CompoundGraphicInstanceID            DcmTagKey(0x0070, 0x0226)
#define DCM_FontName                             DcmTagKey(0x0070, 0x0227)
#define DCM_FontNameType                         DcmTagKey(0x0070, 0x0228)
#define DCM_CSSFontName                          DcmTagKey(0x0070, 0x0229)
#define DCM_RotationAngle                        DcmTagKey(0x0070, 0x0230)
#define DCM_TextStyleSequence                    DcmTagKey(0x0070, 0x0231)
#define DCM_LineStyleSequence                    DcmTagKey(0x0070, 0x0232)
#define DCM_FillStyleSequence                    DcmTagKey(0x0070, 0x0233)
#define DCM_GraphicGroupSequence                 DcmTagKey(0x0070, 0x0234)
#define DCM_TextColorCIELabValue                 DcmTagKey(0x0070, 0x0241)
#define DCM_HorizontalAlignment                  DcmTagKey(0x0070, 0x0242)
#define DCM_VerticalAlignment                    DcmTagKey(0x0070, 0x0243)
#define DCM_ShadowStyle                          DcmTagKey(0x0070, 0x0244)
#define DCM_ShadowOffsetX                        DcmTagKey(0x0070, 0x0245)
#define DCM_ShadowOffsetY                        DcmTagKey(0x0070, 0x0246)
#define DCM_ShadowColorCIELabValue               DcmTagKey(0x0070, 0x0247)
#define DCM_Underlined                           DcmTagKey(0x0070, 0x0248)
#define DCM_Bold                                 DcmTagKey(0x0070, 0x0249)
#define DCM_Italic                               DcmTagKey(0x0070, 0x0250)
#define DCM_PatternOnColorCIELabValue            DcmTagKey(0x0070, 0x0251)
#define DCM_PatternOffColorCIELabValue           DcmTagKey(0x0070, 0x0252)
#define DCM_LineThickness                        DcmTagKey(0x0070, 0x0253)
#define DCM_LineDashingStyle                     DcmTagKey(0x0070, 0x0254)
#define DCM_LinePattern                          DcmTagKey(0x0070, 0x0255)
#define DCM_FillPattern                          DcmTagKey(0x0070, 0x0256)
#define DCM_FillMode                             DcmTagKey(0x0070, 0x0257)
#define DCM_ShadowOpacity                        DcmTagKey(0x0070, 0x0258)
#define DCM_GapLength                            DcmTagKey(0x0070, 0x0261)
#define DCM_DiameterOfVisibility                 DcmTagKey(0x0070, 0x0262)
#define DCM_RotationPoint                        DcmTagKey(0x0070, 0x0273)
#define DCM_TickAlignment                        DcmTagKey(0x0070, 0x0274)
#define DCM_ShowTickLabel                        DcmTagKey(0x0070, 0x0278)
#define DCM_TickLabelAlignment                   DcmTagKey(0x0070, 0x0279)
#define DCM_CompoundGraphicUnits                 DcmTagKey(0x0070, 0x0282)
#define DCM_PatternOnOpacity                     DcmTagKey(0x0070, 0x0284)
#define DCM_PatternOffOpacity                    DcmTagKey(0x0070, 0x0285)
#define DCM_MajorTicksSequence                   DcmTagKey(0x0070, 0x0287)
#define DCM_TickPosition                         DcmTagKey(0x0070, 0x0288)
#define DCM_TickLabel                            DcmTagKey(0x0070, 0x0289)
#define DCM_CompoundGraphicType                  DcmTagKey(0x0070, 0x0294)
#define DCM_GraphicGroupID                       DcmTagKey(0x0070, 0x0295)
#define DCM_ShapeType                            DcmTagKey(0x0070, 0x0306)
#define DCM_RegistrationSequence                 DcmTagKey(0x0070, 0x0308)
#define DCM_MatrixRegistrationSequence           DcmTagKey(0x0070, 0x0309)
#define DCM_MatrixSequence                       DcmTagKey(0x0070, 0x030a)
#define DCM_FrameOfReferenceToDisplayedCoordinateSystemTransformationMatrix DcmTagKey(0x0070, 0x030b)
#define DCM_FrameOfReferenceTransformationMatrixType DcmTagKey(0x0070, 0x030c)
#define DCM_RegistrationTypeCodeSequence         DcmTagKey(0x0070, 0x030d)
#define DCM_FiducialDescription                  DcmTagKey(0x0070, 0x030f)
#define DCM_FiducialIdentifier                   DcmTagKey(0x0070, 0x0310)
#define DCM_FiducialIdentifierCodeSequence       DcmTagKey(0x0070, 0x0311)
#define DCM_ContourUncertaintyRadius             DcmTagKey(0x0070, 0x0312)
#define DCM_UsedFiducialsSequence                DcmTagKey(0x0070, 0x0314)
#define DCM_UsedRTStructureSetROISequence        DcmTagKey(0x0070, 0x0315)
#define DCM_GraphicCoordinatesDataSequence       DcmTagKey(0x0070, 0x0318)
#define DCM_FiducialUID                          DcmTagKey(0x0070, 0x031a)
#define DCM_ReferencedFiducialUID                DcmTagKey(0x0070, 0x031b)
#define DCM_FiducialSetSequence                  DcmTagKey(0x0070, 0x031c)
#define DCM_FiducialSequence                     DcmTagKey(0x0070, 0x031e)
#define DCM_FiducialsPropertyCategoryCodeSequence DcmTagKey(0x0070, 0x031f)
#define DCM_GraphicLayerRecommendedDisplayCIELabValue DcmTagKey(0x0070, 0x0401)
#define DCM_BlendingSequence                     DcmTagKey(0x0070, 0x0402)
#define DCM_RelativeOpacity                      DcmTagKey(0x0070, 0x0403)
#define DCM_ReferencedSpatialRegistrationSequence DcmTagKey(0x0070, 0x0404)
#define DCM_BlendingPosition                     DcmTagKey(0x0070, 0x0405)
#define DCM_PresentationDisplayCollectionUID     DcmTagKey(0x0070, 0x1101)
#define DCM_PresentationSequenceCollectionUID    DcmTagKey(0x0070, 0x1102)
#define DCM_PresentationSequencePositionIndex    DcmTagKey(0x0070, 0x1103)
#define DCM_RenderedImageReferenceSequence       DcmTagKey(0x0070, 0x1104)
#define DCM_VolumetricPresentationStateInputSequence DcmTagKey(0x0070, 0x1201)
#define DCM_PresentationInputType                DcmTagKey(0x0070, 0x1202)
#define DCM_InputSequencePositionIndex           DcmTagKey(0x0070, 0x1203)
#define DCM_Crop                                 DcmTagKey(0x0070, 0x1204)
#define DCM_CroppingSpecificationIndex           DcmTagKey(0x0070, 0x1205)
#define DCM_RETIRED_CompositingMethod            DcmTagKey(0x0070, 0x1206)
#define DCM_VolumetricPresentationInputNumber    DcmTagKey(0x0070, 0x1207)
#define DCM_ImageVolumeGeometry                  DcmTagKey(0x0070, 0x1208)
#define DCM_VolumetricPresentationInputSetUID    DcmTagKey(0x0070, 0x1209)
#define DCM_VolumetricPresentationInputSetSequence DcmTagKey(0x0070, 0x120a)
#define DCM_GlobalCrop                           DcmTagKey(0x0070, 0x120b)
#define DCM_GlobalCroppingSpecificationIndex     DcmTagKey(0x0070, 0x120c)
#define DCM_RenderingMethod                      DcmTagKey(0x0070, 0x120d)
#define DCM_VolumeCroppingSequence               DcmTagKey(0x0070, 0x1301)
#define DCM_VolumeCroppingMethod                 DcmTagKey(0x0070, 0x1302)
#define DCM_BoundingBoxCrop                      DcmTagKey(0x0070, 0x1303)
#define DCM_ObliqueCroppingPlaneSequence         DcmTagKey(0x0070, 0x1304)
#define DCM_Plane                                DcmTagKey(0x0070, 0x1305)
#define DCM_PlaneNormal                          DcmTagKey(0x0070, 0x1306)
#define DCM_CroppingSpecificationNumber          DcmTagKey(0x0070, 0x1309)
#define DCM_MultiPlanarReconstructionStyle       DcmTagKey(0x0070, 0x1501)
#define DCM_MPRThicknessType                     DcmTagKey(0x0070, 0x1502)
#define DCM_MPRSlabThickness                     DcmTagKey(0x0070, 0x1503)
#define DCM_MPRTopLeftHandCorner                 DcmTagKey(0x0070, 0x1505)
#define DCM_MPRViewWidthDirection                DcmTagKey(0x0070, 0x1507)
#define DCM_MPRViewWidth                         DcmTagKey(0x0070, 0x1508)
#define DCM_NumberOfVolumetricCurvePoints        DcmTagKey(0x0070, 0x150c)
#define DCM_VolumetricCurvePoints                DcmTagKey(0x0070, 0x150d)
#define DCM_MPRViewHeightDirection               DcmTagKey(0x0070, 0x1511)
#define DCM_MPRViewHeight                        DcmTagKey(0x0070, 0x1512)
#define DCM_RenderProjection                     DcmTagKey(0x0070, 0x1602)
#define DCM_ViewpointPosition                    DcmTagKey(0x0070, 0x1603)
#define DCM_ViewpointLookAtPoint                 DcmTagKey(0x0070, 0x1604)
#define DCM_ViewpointUpDirection                 DcmTagKey(0x0070, 0x1605)
#define DCM_RenderFieldOfView                    DcmTagKey(0x0070, 0x1606)
#define DCM_SamplingStepSize                     DcmTagKey(0x0070, 0x1607)
#define DCM_ShadingStyle                         DcmTagKey(0x0070, 0x1701)
#define DCM_AmbientReflectionIntensity           DcmTagKey(0x0070, 0x1702)
#define DCM_LightDirection                       DcmTagKey(0x0070, 0x1703)
#define DCM_DiffuseReflectionIntensity           DcmTagKey(0x0070, 0x1704)
#define DCM_SpecularReflectionIntensity          DcmTagKey(0x0070, 0x1705)
#define DCM_Shininess                            DcmTagKey(0x0070, 0x1706)
#define DCM_PresentationStateClassificationComponentSequence DcmTagKey(0x0070, 0x1801)
#define DCM_ComponentType                        DcmTagKey(0x0070, 0x1802)
#define DCM_ComponentInputSequence               DcmTagKey(0x0070, 0x1803)
#define DCM_VolumetricPresentationInputIndex     DcmTagKey(0x0070, 0x1804)
#define DCM_PresentationStateCompositorComponentSequence DcmTagKey(0x0070, 0x1805)
#define DCM_WeightingTransferFunctionSequence    DcmTagKey(0x0070, 0x1806)
#define DCM_RETIRED_WeightingLookupTableDescriptor DcmTagKey(0x0070, 0x1807)
#define DCM_RETIRED_WeightingLookupTableData     DcmTagKey(0x0070, 0x1808)
#define DCM_VolumetricAnnotationSequence         DcmTagKey(0x0070, 0x1901)
#define DCM_ReferencedStructuredContextSequence  DcmTagKey(0x0070, 0x1903)
#define DCM_ReferencedContentItem                DcmTagKey(0x0070, 0x1904)
#define DCM_VolumetricPresentationInputAnnotationSequence DcmTagKey(0x0070, 0x1905)
#define DCM_AnnotationClipping                   DcmTagKey(0x0070, 0x1907)
#define DCM_PresentationAnimationStyle           DcmTagKey(0x0070, 0x1a01)
#define DCM_RecommendedAnimationRate             DcmTagKey(0x0070, 0x1a03)
#define DCM_AnimationCurveSequence               DcmTagKey(0x0070, 0x1a04)
#define DCM_AnimationStepSize                    DcmTagKey(0x0070, 0x1a05)
#define DCM_SwivelRange                          DcmTagKey(0x0070, 0x1a06)
#define DCM_VolumetricCurveUpDirections          DcmTagKey(0x0070, 0x1a07)
#define DCM_VolumeStreamSequence                 DcmTagKey(0x0070, 0x1a08)
#define DCM_RGBATransferFunctionDescription      DcmTagKey(0x0070, 0x1a09)
#define DCM_AdvancedBlendingSequence             DcmTagKey(0x0070, 0x1b01)
#define DCM_BlendingInputNumber                  DcmTagKey(0x0070, 0x1b02)
#define DCM_BlendingDisplayInputSequence         DcmTagKey(0x0070, 0x1b03)
#define DCM_BlendingDisplaySequence              DcmTagKey(0x0070, 0x1b04)
#define DCM_BlendingMode                         DcmTagKey(0x0070, 0x1b06)
#define DCM_TimeSeriesBlending                   DcmTagKey(0x0070, 0x1b07)
#define DCM_GeometryForDisplay                   DcmTagKey(0x0070, 0x1b08)
#define DCM_ThresholdSequence                    DcmTagKey(0x0070, 0x1b11)
#define DCM_ThresholdValueSequence               DcmTagKey(0x0070, 0x1b12)
#define DCM_ThresholdType                        DcmTagKey(0x0070, 0x1b13)
#define DCM_ThresholdValue                       DcmTagKey(0x0070, 0x1b14)
#define DCM_HangingProtocolName                  DcmTagKey(0x0072, 0x0002)
#define DCM_HangingProtocolDescription           DcmTagKey(0x0072, 0x0004)
#define DCM_HangingProtocolLevel                 DcmTagKey(0x0072, 0x0006)
#define DCM_HangingProtocolCreator               DcmTagKey(0x0072, 0x0008)
#define DCM_HangingProtocolCreationDateTime      DcmTagKey(0x0072, 0x000a)
#define DCM_HangingProtocolDefinitionSequence    DcmTagKey(0x0072, 0x000c)
#define DCM_HangingProtocolUserIdentificationCodeSequence DcmTagKey(0x0072, 0x000e)
#define DCM_HangingProtocolUserGroupName         DcmTagKey(0x0072, 0x0010)
#define DCM_SourceHangingProtocolSequence        DcmTagKey(0x0072, 0x0012)
#define DCM_NumberOfPriorsReferenced             DcmTagKey(0x0072, 0x0014)
#define DCM_ImageSetsSequence                    DcmTagKey(0x0072, 0x0020)
#define DCM_ImageSetSelectorSequence             DcmTagKey(0x0072, 0x0022)
#define DCM_ImageSetSelectorUsageFlag            DcmTagKey(0x0072, 0x0024)
#define DCM_SelectorAttribute                    DcmTagKey(0x0072, 0x0026)
#define DCM_SelectorValueNumber                  DcmTagKey(0x0072, 0x0028)
#define DCM_TimeBasedImageSetsSequence           DcmTagKey(0x0072, 0x0030)
#define DCM_ImageSetNumber                       DcmTagKey(0x0072, 0x0032)
#define DCM_ImageSetSelectorCategory             DcmTagKey(0x0072, 0x0034)
#define DCM_RelativeTime                         DcmTagKey(0x0072, 0x0038)
#define DCM_RelativeTimeUnits                    DcmTagKey(0x0072, 0x003a)
#define DCM_AbstractPriorValue                   DcmTagKey(0x0072, 0x003c)
#define DCM_AbstractPriorCodeSequence            DcmTagKey(0x0072, 0x003e)
#define DCM_ImageSetLabel                        DcmTagKey(0x0072, 0x0040)
#define DCM_SelectorAttributeVR                  DcmTagKey(0x0072, 0x0050)
#define DCM_SelectorSequencePointer              DcmTagKey(0x0072, 0x0052)
#define DCM_SelectorSequencePointerPrivateCreator DcmTagKey(0x0072, 0x0054)
#define DCM_SelectorAttributePrivateCreator      DcmTagKey(0x0072, 0x0056)
#define DCM_SelectorAEValue                      DcmTagKey(0x0072, 0x005e)
#define DCM_SelectorASValue                      DcmTagKey(0x0072, 0x005f)
#define DCM_SelectorATValue                      DcmTagKey(0x0072, 0x0060)
#define DCM_SelectorDAValue                      DcmTagKey(0x0072, 0x0061)
#define DCM_SelectorCSValue                      DcmTagKey(0x0072, 0x0062)
#define DCM_SelectorDTValue                      DcmTagKey(0x0072, 0x0063)
#define DCM_SelectorISValue                      DcmTagKey(0x0072, 0x0064)
#define DCM_SelectorOBValue                      DcmTagKey(0x0072, 0x0065)
#define DCM_SelectorLOValue                      DcmTagKey(0x0072, 0x0066)
#define DCM_SelectorOFValue                      DcmTagKey(0x0072, 0x0067)
#define DCM_SelectorLTValue                      DcmTagKey(0x0072, 0x0068)
#define DCM_SelectorOWValue                      DcmTagKey(0x0072, 0x0069)
#define DCM_SelectorPNValue                      DcmTagKey(0x0072, 0x006a)
#define DCM_SelectorTMValue                      DcmTagKey(0x0072, 0x006b)
#define DCM_SelectorSHValue                      DcmTagKey(0x0072, 0x006c)
#define DCM_SelectorUNValue                      DcmTagKey(0x0072, 0x006d)
#define DCM_SelectorSTValue                      DcmTagKey(0x0072, 0x006e)
#define DCM_SelectorUCValue                      DcmTagKey(0x0072, 0x006f)
#define DCM_SelectorUTValue                      DcmTagKey(0x0072, 0x0070)
#define DCM_SelectorURValue                      DcmTagKey(0x0072, 0x0071)
#define DCM_SelectorDSValue                      DcmTagKey(0x0072, 0x0072)
#define DCM_SelectorODValue                      DcmTagKey(0x0072, 0x0073)
#define DCM_SelectorFDValue                      DcmTagKey(0x0072, 0x0074)
#define DCM_SelectorOLValue                      DcmTagKey(0x0072, 0x0075)
#define DCM_SelectorFLValue                      DcmTagKey(0x0072, 0x0076)
#define DCM_SelectorULValue                      DcmTagKey(0x0072, 0x0078)
#define DCM_SelectorUSValue                      DcmTagKey(0x0072, 0x007a)
#define DCM_SelectorSLValue                      DcmTagKey(0x0072, 0x007c)
#define DCM_SelectorSSValue                      DcmTagKey(0x0072, 0x007e)
#define DCM_SelectorUIValue                      DcmTagKey(0x0072, 0x007f)
#define DCM_SelectorCodeSequenceValue            DcmTagKey(0x0072, 0x0080)
#define DCM_SelectorOVValue                      DcmTagKey(0x0072, 0x0081)
#define DCM_SelectorSVValue                      DcmTagKey(0x0072, 0x0082)
#define DCM_SelectorUVValue                      DcmTagKey(0x0072, 0x0083)
#define DCM_NumberOfScreens                      DcmTagKey(0x0072, 0x0100)
#define DCM_NominalScreenDefinitionSequence      DcmTagKey(0x0072, 0x0102)
#define DCM_NumberOfVerticalPixels               DcmTagKey(0x0072, 0x0104)
#define DCM_NumberOfHorizontalPixels             DcmTagKey(0x0072, 0x0106)
#define DCM_DisplayEnvironmentSpatialPosition    DcmTagKey(0x0072, 0x0108)
#define DCM_ScreenMinimumGrayscaleBitDepth       DcmTagKey(0x0072, 0x010a)
#define DCM_ScreenMinimumColorBitDepth           DcmTagKey(0x0072, 0x010c)
#define DCM_ApplicationMaximumRepaintTime        DcmTagKey(0x0072, 0x010e)
#define DCM_DisplaySetsSequence                  DcmTagKey(0x0072, 0x0200)
#define DCM_DisplaySetNumber                     DcmTagKey(0x0072, 0x0202)
#define DCM_DisplaySetLabel                      DcmTagKey(0x0072, 0x0203)
#define DCM_DisplaySetPresentationGroup          DcmTagKey(0x0072, 0x0204)
#define DCM_DisplaySetPresentationGroupDescription DcmTagKey(0x0072, 0x0206)
#define DCM_PartialDataDisplayHandling           DcmTagKey(0x0072, 0x0208)
#define DCM_SynchronizedScrollingSequence        DcmTagKey(0x0072, 0x0210)
#define DCM_DisplaySetScrollingGroup             DcmTagKey(0x0072, 0x0212)
#define DCM_NavigationIndicatorSequence          DcmTagKey(0x0072, 0x0214)
#define DCM_NavigationDisplaySet                 DcmTagKey(0x0072, 0x0216)
#define DCM_ReferenceDisplaySets                 DcmTagKey(0x0072, 0x0218)
#define DCM_ImageBoxesSequence                   DcmTagKey(0x0072, 0x0300)
#define DCM_ImageBoxNumber                       DcmTagKey(0x0072, 0x0302)
#define DCM_ImageBoxLayoutType                   DcmTagKey(0x0072, 0x0304)
#define DCM_ImageBoxTileHorizontalDimension      DcmTagKey(0x0072, 0x0306)
#define DCM_ImageBoxTileVerticalDimension        DcmTagKey(0x0072, 0x0308)
#define DCM_ImageBoxScrollDirection              DcmTagKey(0x0072, 0x0310)
#define DCM_ImageBoxSmallScrollType              DcmTagKey(0x0072, 0x0312)
#define DCM_ImageBoxSmallScrollAmount            DcmTagKey(0x0072, 0x0314)
#define DCM_ImageBoxLargeScrollType              DcmTagKey(0x0072, 0x0316)
#define DCM_ImageBoxLargeScrollAmount            DcmTagKey(0x0072, 0x0318)
#define DCM_ImageBoxOverlapPriority              DcmTagKey(0x0072, 0x0320)
#define DCM_CineRelativeToRealTime               DcmTagKey(0x0072, 0x0330)
#define DCM_FilterOperationsSequence             DcmTagKey(0x0072, 0x0400)
#define DCM_FilterByCategory                     DcmTagKey(0x0072, 0x0402)
#define DCM_FilterByAttributePresence            DcmTagKey(0x0072, 0x0404)
#define DCM_FilterByOperator                     DcmTagKey(0x0072, 0x0406)
#define DCM_StructuredDisplayBackgroundCIELabValue DcmTagKey(0x0072, 0x0420)
#define DCM_EmptyImageBoxCIELabValue             DcmTagKey(0x0072, 0x0421)
#define DCM_StructuredDisplayImageBoxSequence    DcmTagKey(0x0072, 0x0422)
#define DCM_StructuredDisplayTextBoxSequence     DcmTagKey(0x0072, 0x0424)
#define DCM_ReferencedFirstFrameSequence         DcmTagKey(0x0072, 0x0427)
#define DCM_ImageBoxSynchronizationSequence      DcmTagKey(0x0072, 0x0430)
#define DCM_SynchronizedImageBoxList             DcmTagKey(0x0072, 0x0432)
#define DCM_TypeOfSynchronization                DcmTagKey(0x0072, 0x0434)
#define DCM_BlendingOperationType                DcmTagKey(0x0072, 0x0500)
#define DCM_ReformattingOperationType            DcmTagKey(0x0072, 0x0510)
#define DCM_ReformattingThickness                DcmTagKey(0x0072, 0x0512)
#define DCM_ReformattingInterval                 DcmTagKey(0x0072, 0x0514)
#define DCM_ReformattingOperationInitialViewDirection DcmTagKey(0x0072, 0x0516)
#define DCM_ThreeDRenderingType                  DcmTagKey(0x0072, 0x0520)
#define DCM_SortingOperationsSequence            DcmTagKey(0x0072, 0x0600)
#define DCM_SortByCategory                       DcmTagKey(0x0072, 0x0602)
#define DCM_SortingDirection                     DcmTagKey(0x0072, 0x0604)
#define DCM_DisplaySetPatientOrientation         DcmTagKey(0x0072, 0x0700)
#define DCM_VOIType                              DcmTagKey(0x0072, 0x0702)
#define DCM_PseudoColorType                      DcmTagKey(0x0072, 0x0704)
#define DCM_PseudoColorPaletteInstanceReferenceSequence DcmTagKey(0x0072, 0x0705)
#define DCM_ShowGrayscaleInverted                DcmTagKey(0x0072, 0x0706)
#define DCM_ShowImageTrueSizeFlag                DcmTagKey(0x0072, 0x0710)
#define DCM_ShowGraphicAnnotationFlag            DcmTagKey(0x0072, 0x0712)
#define DCM_ShowPatientDemographicsFlag          DcmTagKey(0x0072, 0x0714)
#define DCM_ShowAcquisitionTechniquesFlag        DcmTagKey(0x0072, 0x0716)
#define DCM_DisplaySetHorizontalJustification    DcmTagKey(0x0072, 0x0717)
#define DCM_DisplaySetVerticalJustification      DcmTagKey(0x0072, 0x0718)
#define DCM_ContinuationStartMeterset            DcmTagKey(0x0074, 0x0120)
#define DCM_ContinuationEndMeterset              DcmTagKey(0x0074, 0x0121)
#define DCM_ProcedureStepState                   DcmTagKey(0x0074, 0x1000)
#define DCM_ProcedureStepProgressInformationSequence DcmTagKey(0x0074, 0x1002)
#define DCM_ProcedureStepProgress                DcmTagKey(0x0074, 0x1004)
#define DCM_ProcedureStepProgressDescription     DcmTagKey(0x0074, 0x1006)
#define DCM_ProcedureStepProgressParametersSequence DcmTagKey(0x0074, 0x1007)
#define DCM_ProcedureStepCommunicationsURISequence DcmTagKey(0x0074, 0x1008)
#define DCM_ContactURI                           DcmTagKey(0x0074, 0x100a)
#define DCM_ContactDisplayName                   DcmTagKey(0x0074, 0x100c)
#define DCM_ProcedureStepDiscontinuationReasonCodeSequence DcmTagKey(0x0074, 0x100e)
#define DCM_BeamTaskSequence                     DcmTagKey(0x0074, 0x1020)
#define DCM_BeamTaskType                         DcmTagKey(0x0074, 0x1022)
#define DCM_RETIRED_BeamOrderIndexTrial          DcmTagKey(0x0074, 0x1024)
#define DCM_AutosequenceFlag                     DcmTagKey(0x0074, 0x1025)
#define DCM_TableTopVerticalAdjustedPosition     DcmTagKey(0x0074, 0x1026)
#define DCM_TableTopLongitudinalAdjustedPosition DcmTagKey(0x0074, 0x1027)
#define DCM_TableTopLateralAdjustedPosition      DcmTagKey(0x0074, 0x1028)
#define DCM_PatientSupportAdjustedAngle          DcmTagKey(0x0074, 0x102a)
#define DCM_TableTopEccentricAdjustedAngle       DcmTagKey(0x0074, 0x102b)
#define DCM_TableTopPitchAdjustedAngle           DcmTagKey(0x0074, 0x102c)
#define DCM_TableTopRollAdjustedAngle            DcmTagKey(0x0074, 0x102d)
#define DCM_DeliveryVerificationImageSequence    DcmTagKey(0x0074, 0x1030)
#define DCM_VerificationImageTiming              DcmTagKey(0x0074, 0x1032)
#define DCM_DoubleExposureFlag                   DcmTagKey(0x0074, 0x1034)
#define DCM_DoubleExposureOrdering               DcmTagKey(0x0074, 0x1036)
#define DCM_RETIRED_DoubleExposureMetersetTrial  DcmTagKey(0x0074, 0x1038)
#define DCM_RETIRED_DoubleExposureFieldDeltaTrial DcmTagKey(0x0074, 0x103a)
#define DCM_RelatedReferenceRTImageSequence      DcmTagKey(0x0074, 0x1040)
#define DCM_GeneralMachineVerificationSequence   DcmTagKey(0x0074, 0x1042)
#define DCM_ConventionalMachineVerificationSequence DcmTagKey(0x0074, 0x1044)
#define DCM_IonMachineVerificationSequence       DcmTagKey(0x0074, 0x1046)
#define DCM_FailedAttributesSequence             DcmTagKey(0x0074, 0x1048)
#define DCM_OverriddenAttributesSequence         DcmTagKey(0x0074, 0x104a)
#define DCM_ConventionalControlPointVerificationSequence DcmTagKey(0x0074, 0x104c)
#define DCM_IonControlPointVerificationSequence  DcmTagKey(0x0074, 0x104e)
#define DCM_AttributeOccurrenceSequence          DcmTagKey(0x0074, 0x1050)
#define DCM_AttributeOccurrencePointer           DcmTagKey(0x0074, 0x1052)
#define DCM_AttributeItemSelector                DcmTagKey(0x0074, 0x1054)
#define DCM_AttributeOccurrencePrivateCreator    DcmTagKey(0x0074, 0x1056)
#define DCM_SelectorSequencePointerItems         DcmTagKey(0x0074, 0x1057)
#define DCM_ScheduledProcedureStepPriority       DcmTagKey(0x0074, 0x1200)
#define DCM_WorklistLabel                        DcmTagKey(0x0074, 0x1202)
#define DCM_ProcedureStepLabel                   DcmTagKey(0x0074, 0x1204)
#define DCM_ScheduledProcessingParametersSequence DcmTagKey(0x0074, 0x1210)
#define DCM_PerformedProcessingParametersSequence DcmTagKey(0x0074, 0x1212)
#define DCM_UnifiedProcedureStepPerformedProcedureSequence DcmTagKey(0x0074, 0x1216)
#define DCM_RETIRED_RelatedProcedureStepSequence DcmTagKey(0x0074, 0x1220)
#define DCM_RETIRED_ProcedureStepRelationshipType DcmTagKey(0x0074, 0x1222)
#define DCM_ReplacedProcedureStepSequence        DcmTagKey(0x0074, 0x1224)
#define DCM_DeletionLock                         DcmTagKey(0x0074, 0x1230)
#define DCM_ReceivingAE                          DcmTagKey(0x0074, 0x1234)
#define DCM_RequestingAE                         DcmTagKey(0x0074, 0x1236)
#define DCM_ReasonForCancellation                DcmTagKey(0x0074, 0x1238)
#define DCM_SCPStatus                            DcmTagKey(0x0074, 0x1242)
#define DCM_SubscriptionListStatus               DcmTagKey(0x0074, 0x1244)
#define DCM_UnifiedProcedureStepListStatus       DcmTagKey(0x0074, 0x1246)
#define DCM_BeamOrderIndex                       DcmTagKey(0x0074, 0x1324)
#define DCM_DoubleExposureMeterset               DcmTagKey(0x0074, 0x1338)
#define DCM_DoubleExposureFieldDelta             DcmTagKey(0x0074, 0x133a)
#define DCM_BrachyTaskSequence                   DcmTagKey(0x0074, 0x1401)
#define DCM_ContinuationStartTotalReferenceAirKerma DcmTagKey(0x0074, 0x1402)
#define DCM_ContinuationEndTotalReferenceAirKerma DcmTagKey(0x0074, 0x1403)
#define DCM_ContinuationPulseNumber              DcmTagKey(0x0074, 0x1404)
#define DCM_ChannelDeliveryOrderSequence         DcmTagKey(0x0074, 0x1405)
#define DCM_ReferencedChannelNumber              DcmTagKey(0x0074, 0x1406)
#define DCM_StartCumulativeTimeWeight            DcmTagKey(0x0074, 0x1407)
#define DCM_EndCumulativeTimeWeight              DcmTagKey(0x0074, 0x1408)
#define DCM_OmittedChannelSequence               DcmTagKey(0x0074, 0x1409)
#define DCM_ReasonForChannelOmission             DcmTagKey(0x0074, 0x140a)
#define DCM_ReasonForChannelOmissionDescription  DcmTagKey(0x0074, 0x140b)
#define DCM_ChannelDeliveryOrderIndex            DcmTagKey(0x0074, 0x140c)
#define DCM_ChannelDeliveryContinuationSequence  DcmTagKey(0x0074, 0x140d)
#define DCM_OmittedApplicationSetupSequence      DcmTagKey(0x0074, 0x140e)
#define DCM_ImplantAssemblyTemplateName          DcmTagKey(0x0076, 0x0001)
#define DCM_ImplantAssemblyTemplateIssuer        DcmTagKey(0x0076, 0x0003)
#define DCM_ImplantAssemblyTemplateVersion       DcmTagKey(0x0076, 0x0006)
#define DCM_ReplacedImplantAssemblyTemplateSequence DcmTagKey(0x0076, 0x0008)
#define DCM_ImplantAssemblyTemplateType          DcmTagKey(0x0076, 0x000a)
#define DCM_OriginalImplantAssemblyTemplateSequence DcmTagKey(0x0076, 0x000c)
#define DCM_DerivationImplantAssemblyTemplateSequence DcmTagKey(0x0076, 0x000e)
#define DCM_ImplantAssemblyTemplateTargetAnatomySequence DcmTagKey(0x0076, 0x0010)
#define DCM_ProcedureTypeCodeSequence            DcmTagKey(0x0076, 0x0020)
#define DCM_SurgicalTechnique                    DcmTagKey(0x0076, 0x0030)
#define DCM_ComponentTypesSequence               DcmTagKey(0x0076, 0x0032)
#define DCM_ComponentTypeCodeSequence            DcmTagKey(0x0076, 0x0034)
#define DCM_ExclusiveComponentType               DcmTagKey(0x0076, 0x0036)
#define DCM_MandatoryComponentType               DcmTagKey(0x0076, 0x0038)
#define DCM_ComponentSequence                    DcmTagKey(0x0076, 0x0040)
#define DCM_ComponentID                          DcmTagKey(0x0076, 0x0055)
#define DCM_ComponentAssemblySequence            DcmTagKey(0x0076, 0x0060)
#define DCM_Component1ReferencedID               DcmTagKey(0x0076, 0x0070)
#define DCM_Component1ReferencedMatingFeatureSetID DcmTagKey(0x0076, 0x0080)
#define DCM_Component1ReferencedMatingFeatureID  DcmTagKey(0x0076, 0x0090)
#define DCM_Component2ReferencedID               DcmTagKey(0x0076, 0x00a0)
#define DCM_Component2ReferencedMatingFeatureSetID DcmTagKey(0x0076, 0x00b0)
#define DCM_Component2ReferencedMatingFeatureID  DcmTagKey(0x0076, 0x00c0)
#define DCM_ImplantTemplateGroupName             DcmTagKey(0x0078, 0x0001)
#define DCM_ImplantTemplateGroupDescription      DcmTagKey(0x0078, 0x0010)
#define DCM_ImplantTemplateGroupIssuer           DcmTagKey(0x0078, 0x0020)
#define DCM_ImplantTemplateGroupVersion          DcmTagKey(0x0078, 0x0024)
#define DCM_ReplacedImplantTemplateGroupSequence DcmTagKey(0x0078, 0x0026)
#define DCM_ImplantTemplateGroupTargetAnatomySequence DcmTagKey(0x0078, 0x0028)
#define DCM_ImplantTemplateGroupMembersSequence  DcmTagKey(0x0078, 0x002a)
#define DCM_ImplantTemplateGroupMemberID         DcmTagKey(0x0078, 0x002e)
#define DCM_ThreeDImplantTemplateGroupMemberMatchingPoint DcmTagKey(0x0078, 0x0050)
#define DCM_ThreeDImplantTemplateGroupMemberMatchingAxes DcmTagKey(0x0078, 0x0060)
#define DCM_ImplantTemplateGroupMemberMatching2DCoordinatesSequence DcmTagKey(0x0078, 0x0070)
#define DCM_TwoDImplantTemplateGroupMemberMatchingPoint DcmTagKey(0x0078, 0x0090)
#define DCM_TwoDImplantTemplateGroupMemberMatchingAxes DcmTagKey(0x0078, 0x00a0)
#define DCM_ImplantTemplateGroupVariationDimensionSequence DcmTagKey(0x0078, 0x00b0)
#define DCM_ImplantTemplateGroupVariationDimensionName DcmTagKey(0x0078, 0x00b2)
#define DCM_ImplantTemplateGroupVariationDimensionRankSequence DcmTagKey(0x0078, 0x00b4)
#define DCM_ReferencedImplantTemplateGroupMemberID DcmTagKey(0x0078, 0x00b6)
#define DCM_ImplantTemplateGroupVariationDimensionRank DcmTagKey(0x0078, 0x00b8)
#define DCM_SurfaceScanAcquisitionTypeCodeSequence DcmTagKey(0x0080, 0x0001)
#define DCM_SurfaceScanModeCodeSequence          DcmTagKey(0x0080, 0x0002)
#define DCM_RegistrationMethodCodeSequence       DcmTagKey(0x0080, 0x0003)
#define DCM_ShotDurationTime                     DcmTagKey(0x0080, 0x0004)
#define DCM_ShotOffsetTime                       DcmTagKey(0x0080, 0x0005)
#define DCM_SurfacePointPresentationValueData    DcmTagKey(0x0080, 0x0006)
#define DCM_SurfacePointColorCIELabValueData     DcmTagKey(0x0080, 0x0007)
#define DCM_UVMappingSequence                    DcmTagKey(0x0080, 0x0008)
#define DCM_TextureLabel                         DcmTagKey(0x0080, 0x0009)
#define DCM_UValueData                           DcmTagKey(0x0080, 0x0010)
#define DCM_VValueData                           DcmTagKey(0x0080, 0x0011)
#define DCM_ReferencedTextureSequence            DcmTagKey(0x0080, 0x0012)
#define DCM_ReferencedSurfaceDataSequence        DcmTagKey(0x0080, 0x0013)
#define DCM_AssessmentSummary                    DcmTagKey(0x0082, 0x0001)
#define DCM_AssessmentSummaryDescription         DcmTagKey(0x0082, 0x0003)
#define DCM_AssessedSOPInstanceSequence          DcmTagKey(0x0082, 0x0004)
#define DCM_ReferencedComparisonSOPInstanceSequence DcmTagKey(0x0082, 0x0005)
#define DCM_NumberOfAssessmentObservations       DcmTagKey(0x0082, 0x0006)
#define DCM_AssessmentObservationsSequence       DcmTagKey(0x0082, 0x0007)
#define DCM_ObservationSignificance              DcmTagKey(0x0082, 0x0008)
#define DCM_ObservationDescription               DcmTagKey(0x0082, 0x000a)
#define DCM_StructuredConstraintObservationSequence DcmTagKey(0x0082, 0x000c)
#define DCM_AssessedAttributeValueSequence       DcmTagKey(0x0082, 0x0010)
#define DCM_AssessmentSetID                      DcmTagKey(0x0082, 0x0016)
#define DCM_AssessmentRequesterSequence          DcmTagKey(0x0082, 0x0017)
#define DCM_SelectorAttributeName                DcmTagKey(0x0082, 0x0018)
#define DCM_SelectorAttributeKeyword             DcmTagKey(0x0082, 0x0019)
#define DCM_AssessmentTypeCodeSequence           DcmTagKey(0x0082, 0x0021)
#define DCM_ObservationBasisCodeSequence         DcmTagKey(0x0082, 0x0022)
#define DCM_AssessmentLabel                      DcmTagKey(0x0082, 0x0023)
#define DCM_ConstraintType                       DcmTagKey(0x0082, 0x0032)
#define DCM_SpecificationSelectionGuidance       DcmTagKey(0x0082, 0x0033)
#define DCM_ConstraintValueSequence              DcmTagKey(0x0082, 0x0034)
#define DCM_RecommendedDefaultValueSequence      DcmTagKey(0x0082, 0x0035)
#define DCM_ConstraintViolationSignificance      DcmTagKey(0x0082, 0x0036)
#define DCM_ConstraintViolationCondition         DcmTagKey(0x0082, 0x0037)
#define DCM_ModifiableConstraintFlag             DcmTagKey(0x0082, 0x0038)
#define DCM_StorageMediaFileSetID                DcmTagKey(0x0088, 0x0130)
#define DCM_StorageMediaFileSetUID               DcmTagKey(0x0088, 0x0140)
#define DCM_IconImageSequence                    DcmTagKey(0x0088, 0x0200)
#define DCM_RETIRED_TopicTitle                   DcmTagKey(0x0088, 0x0904)
#define DCM_RETIRED_TopicSubject                 DcmTagKey(0x0088, 0x0906)
#define DCM_RETIRED_TopicAuthor                  DcmTagKey(0x0088, 0x0910)
#define DCM_RETIRED_TopicKeywords                DcmTagKey(0x0088, 0x0912)
#define DCM_SOPInstanceStatus                    DcmTagKey(0x0100, 0x0410)
#define DCM_SOPAuthorizationDateTime             DcmTagKey(0x0100, 0x0420)
#define DCM_SOPAuthorizationComment              DcmTagKey(0x0100, 0x0424)
#define DCM_AuthorizationEquipmentCertificationNumber DcmTagKey(0x0100, 0x0426)
#define DCM_MACIDNumber                          DcmTagKey(0x0400, 0x0005)
#define DCM_MACCalculationTransferSyntaxUID      DcmTagKey(0x0400, 0x0010)
#define DCM_MACAlgorithm                         DcmTagKey(0x0400, 0x0015)
#define DCM_DataElementsSigned                   DcmTagKey(0x0400, 0x0020)
#define DCM_DigitalSignatureUID                  DcmTagKey(0x0400, 0x0100)
#define DCM_DigitalSignatureDateTime             DcmTagKey(0x0400, 0x0105)
#define DCM_CertificateType                      DcmTagKey(0x0400, 0x0110)
#define DCM_CertificateOfSigner                  DcmTagKey(0x0400, 0x0115)
#define DCM_Signature                            DcmTagKey(0x0400, 0x0120)
#define DCM_CertifiedTimestampType               DcmTagKey(0x0400, 0x0305)
#define DCM_CertifiedTimestamp                   DcmTagKey(0x0400, 0x0310)
#define DCM_DigitalSignaturePurposeCodeSequence  DcmTagKey(0x0400, 0x0401)
#define DCM_ReferencedDigitalSignatureSequence   DcmTagKey(0x0400, 0x0402)
#define DCM_ReferencedSOPInstanceMACSequence     DcmTagKey(0x0400, 0x0403)
#define DCM_MAC                                  DcmTagKey(0x0400, 0x0404)
#define DCM_EncryptedAttributesSequence          DcmTagKey(0x0400, 0x0500)
#define DCM_EncryptedContentTransferSyntaxUID    DcmTagKey(0x0400, 0x0510)
#define DCM_EncryptedContent                     DcmTagKey(0x0400, 0x0520)
#define DCM_ModifiedAttributesSequence           DcmTagKey(0x0400, 0x0550)
#define DCM_NonconformingModifiedAttributesSequence DcmTagKey(0x0400, 0x0551)
#define DCM_NonconformingDataElementValue        DcmTagKey(0x0400, 0x0552)
#define DCM_OriginalAttributesSequence           DcmTagKey(0x0400, 0x0561)
#define DCM_AttributeModificationDateTime        DcmTagKey(0x0400, 0x0562)
#define DCM_ModifyingSystem                      DcmTagKey(0x0400, 0x0563)
#define DCM_SourceOfPreviousValues               DcmTagKey(0x0400, 0x0564)
#define DCM_ReasonForTheAttributeModification    DcmTagKey(0x0400, 0x0565)
#define DCM_InstanceOriginStatus                 DcmTagKey(0x0400, 0x0600)
#define DCM_RETIRED_EscapeTriplet                DcmTagKey(0x1000, 0x0010)
#define DCM_RETIRED_RunLengthTriplet             DcmTagKey(0x1000, 0x0011)
#define DCM_RETIRED_HuffmanTableSize             DcmTagKey(0x1000, 0x0012)
#define DCM_RETIRED_HuffmanTableTriplet          DcmTagKey(0x1000, 0x0013)
#define DCM_RETIRED_ShiftTableSize               DcmTagKey(0x1000, 0x0014)
#define DCM_RETIRED_ShiftTableTriplet            DcmTagKey(0x1000, 0x0015)
#define DCM_RETIRED_ZonalMap                     DcmTagKey(0x1010, 0x0004)
#define DCM_NumberOfCopies                       DcmTagKey(0x2000, 0x0010)
#define DCM_PrinterConfigurationSequence         DcmTagKey(0x2000, 0x001e)
#define DCM_PrintPriority                        DcmTagKey(0x2000, 0x0020)
#define DCM_MediumType                           DcmTagKey(0x2000, 0x0030)
#define DCM_FilmDestination                      DcmTagKey(0x2000, 0x0040)
#define DCM_FilmSessionLabel                     DcmTagKey(0x2000, 0x0050)
#define DCM_MemoryAllocation                     DcmTagKey(0x2000, 0x0060)
#define DCM_MaximumMemoryAllocation              DcmTagKey(0x2000, 0x0061)
#define DCM_RETIRED_ColorImagePrintingFlag       DcmTagKey(0x2000, 0x0062)
#define DCM_RETIRED_CollationFlag                DcmTagKey(0x2000, 0x0063)
#define DCM_RETIRED_AnnotationFlag               DcmTagKey(0x2000, 0x0065)
#define DCM_RETIRED_ImageOverlayFlag             DcmTagKey(0x2000, 0x0067)
#define DCM_RETIRED_PresentationLUTFlag          DcmTagKey(0x2000, 0x0069)
#define DCM_RETIRED_ImageBoxPresentationLUTFlag  DcmTagKey(0x2000, 0x006a)
#define DCM_MemoryBitDepth                       DcmTagKey(0x2000, 0x00a0)
#define DCM_PrintingBitDepth                     DcmTagKey(0x2000, 0x00a1)
#define DCM_MediaInstalledSequence               DcmTagKey(0x2000, 0x00a2)
#define DCM_OtherMediaAvailableSequence          DcmTagKey(0x2000, 0x00a4)
#define DCM_SupportedImageDisplayFormatsSequence DcmTagKey(0x2000, 0x00a8)
#define DCM_ReferencedFilmBoxSequence            DcmTagKey(0x2000, 0x0500)
#define DCM_RETIRED_ReferencedStoredPrintSequence DcmTagKey(0x2000, 0x0510)
#define DCM_ImageDisplayFormat                   DcmTagKey(0x2010, 0x0010)
#define DCM_AnnotationDisplayFormatID            DcmTagKey(0x2010, 0x0030)
#define DCM_FilmOrientation                      DcmTagKey(0x2010, 0x0040)
#define DCM_FilmSizeID                           DcmTagKey(0x2010, 0x0050)
#define DCM_PrinterResolutionID                  DcmTagKey(0x2010, 0x0052)
#define DCM_DefaultPrinterResolutionID           DcmTagKey(0x2010, 0x0054)
#define DCM_MagnificationType                    DcmTagKey(0x2010, 0x0060)
#define DCM_SmoothingType                        DcmTagKey(0x2010, 0x0080)
#define DCM_DefaultMagnificationType             DcmTagKey(0x2010, 0x00a6)
#define DCM_OtherMagnificationTypesAvailable     DcmTagKey(0x2010, 0x00a7)
#define DCM_DefaultSmoothingType                 DcmTagKey(0x2010, 0x00a8)
#define DCM_OtherSmoothingTypesAvailable         DcmTagKey(0x2010, 0x00a9)
#define DCM_BorderDensity                        DcmTagKey(0x2010, 0x0100)
#define DCM_EmptyImageDensity                    DcmTagKey(0x2010, 0x0110)
#define DCM_MinDensity                           DcmTagKey(0x2010, 0x0120)
#define DCM_MaxDensity                           DcmTagKey(0x2010, 0x0130)
#define DCM_Trim                                 DcmTagKey(0x2010, 0x0140)
#define DCM_ConfigurationInformation             DcmTagKey(0x2010, 0x0150)
#define DCM_ConfigurationInformationDescription  DcmTagKey(0x2010, 0x0152)
#define DCM_MaximumCollatedFilms                 DcmTagKey(0x2010, 0x0154)
#define DCM_Illumination                         DcmTagKey(0x2010, 0x015e)
#define DCM_ReflectedAmbientLight                DcmTagKey(0x2010, 0x0160)
#define DCM_PrinterPixelSpacing                  DcmTagKey(0x2010, 0x0376)
#define DCM_ReferencedFilmSessionSequence        DcmTagKey(0x2010, 0x0500)
#define DCM_ReferencedImageBoxSequence           DcmTagKey(0x2010, 0x0510)
#define DCM_ReferencedBasicAnnotationBoxSequence DcmTagKey(0x2010, 0x0520)
#define DCM_ImageBoxPosition                     DcmTagKey(0x2020, 0x0010)
#define DCM_Polarity                             DcmTagKey(0x2020, 0x0020)
#define DCM_RequestedImageSize                   DcmTagKey(0x2020, 0x0030)
#define DCM_RequestedDecimateCropBehavior        DcmTagKey(0x2020, 0x0040)
#define DCM_RequestedResolutionID                DcmTagKey(0x2020, 0x0050)
#define DCM_RequestedImageSizeFlag               DcmTagKey(0x2020, 0x00a0)
#define DCM_DecimateCropResult                   DcmTagKey(0x2020, 0x00a2)
#define DCM_BasicGrayscaleImageSequence          DcmTagKey(0x2020, 0x0110)
#define DCM_BasicColorImageSequence              DcmTagKey(0x2020, 0x0111)
#define DCM_RETIRED_ReferencedImageOverlayBoxSequence DcmTagKey(0x2020, 0x0130)
#define DCM_RETIRED_ReferencedVOILUTBoxSequence  DcmTagKey(0x2020, 0x0140)
#define DCM_AnnotationPosition                   DcmTagKey(0x2030, 0x0010)
#define DCM_TextString                           DcmTagKey(0x2030, 0x0020)
#define DCM_RETIRED_ReferencedOverlayPlaneSequence DcmTagKey(0x2040, 0x0010)
#define DCM_RETIRED_ReferencedOverlayPlaneGroups DcmTagKey(0x2040, 0x0011)
#define DCM_RETIRED_OverlayPixelDataSequence     DcmTagKey(0x2040, 0x0020)
#define DCM_RETIRED_OverlayMagnificationType     DcmTagKey(0x2040, 0x0060)
#define DCM_RETIRED_OverlaySmoothingType         DcmTagKey(0x2040, 0x0070)
#define DCM_RETIRED_OverlayOrImageMagnification  DcmTagKey(0x2040, 0x0072)
#define DCM_RETIRED_MagnifyToNumberOfColumns     DcmTagKey(0x2040, 0x0074)
#define DCM_RETIRED_OverlayForegroundDensity     DcmTagKey(0x2040, 0x0080)
#define DCM_RETIRED_OverlayBackgroundDensity     DcmTagKey(0x2040, 0x0082)
#define DCM_RETIRED_OverlayMode                  DcmTagKey(0x2040, 0x0090)
#define DCM_RETIRED_ThresholdDensity             DcmTagKey(0x2040, 0x0100)
#define DCM_RETIRED_ReferencedImageBoxSequenceRetired DcmTagKey(0x2040, 0x0500)
#define DCM_PresentationLUTSequence              DcmTagKey(0x2050, 0x0010)
#define DCM_PresentationLUTShape                 DcmTagKey(0x2050, 0x0020)
#define DCM_ReferencedPresentationLUTSequence    DcmTagKey(0x2050, 0x0500)
#define DCM_RETIRED_PrintJobID                   DcmTagKey(0x2100, 0x0010)
#define DCM_ExecutionStatus                      DcmTagKey(0x2100, 0x0020)
#define DCM_ExecutionStatusInfo                  DcmTagKey(0x2100, 0x0030)
#define DCM_CreationDate                         DcmTagKey(0x2100, 0x0040)
#define DCM_CreationTime                         DcmTagKey(0x2100, 0x0050)
#define DCM_Originator                           DcmTagKey(0x2100, 0x0070)
#define DCM_DestinationAE                        DcmTagKey(0x2100, 0x0140)
#define DCM_OwnerID                              DcmTagKey(0x2100, 0x0160)
#define DCM_NumberOfFilms                        DcmTagKey(0x2100, 0x0170)
#define DCM_RETIRED_ReferencedPrintJobSequencePullStoredPrint DcmTagKey(0x2100, 0x0500)
#define DCM_PrinterStatus                        DcmTagKey(0x2110, 0x0010)
#define DCM_PrinterStatusInfo                    DcmTagKey(0x2110, 0x0020)
#define DCM_PrinterName                          DcmTagKey(0x2110, 0x0030)
#define DCM_RETIRED_PrintQueueID                 DcmTagKey(0x2110, 0x0099)
#define DCM_RETIRED_QueueStatus                  DcmTagKey(0x2120, 0x0010)
#define DCM_RETIRED_PrintJobDescriptionSequence  DcmTagKey(0x2120, 0x0050)
#define DCM_RETIRED_ReferencedPrintJobSequence   DcmTagKey(0x2120, 0x0070)
#define DCM_RETIRED_PrintManagementCapabilitiesSequence DcmTagKey(0x2130, 0x0010)
#define DCM_RETIRED_PrinterCharacteristicsSequence DcmTagKey(0x2130, 0x0015)
#define DCM_RETIRED_FilmBoxContentSequence       DcmTagKey(0x2130, 0x0030)
#define DCM_RETIRED_ImageBoxContentSequence      DcmTagKey(0x2130, 0x0040)
#define DCM_RETIRED_AnnotationContentSequence    DcmTagKey(0x2130, 0x0050)
#define DCM_RETIRED_ImageOverlayBoxContentSequence DcmTagKey(0x2130, 0x0060)
#define DCM_RETIRED_PresentationLUTContentSequence DcmTagKey(0x2130, 0x0080)
#define DCM_ProposedStudySequence                DcmTagKey(0x2130, 0x00a0)
#define DCM_OriginalImageSequence                DcmTagKey(0x2130, 0x00c0)
#define DCM_LabelUsingInformationExtractedFromInstances DcmTagKey(0x2200, 0x0001)
#define DCM_LabelText                            DcmTagKey(0x2200, 0x0002)
#define DCM_LabelStyleSelection                  DcmTagKey(0x2200, 0x0003)
#define DCM_MediaDisposition                     DcmTagKey(0x2200, 0x0004)
#define DCM_BarcodeValue                         DcmTagKey(0x2200, 0x0005)
#define DCM_BarcodeSymbology                     DcmTagKey(0x2200, 0x0006)
#define DCM_AllowMediaSplitting                  DcmTagKey(0x2200, 0x0007)
#define DCM_IncludeNonDICOMObjects               DcmTagKey(0x2200, 0x0008)
#define DCM_IncludeDisplayApplication            DcmTagKey(0x2200, 0x0009)
#define DCM_PreserveCompositeInstancesAfterMediaCreation DcmTagKey(0x2200, 0x000a)
#define DCM_TotalNumberOfPiecesOfMediaCreated    DcmTagKey(0x2200, 0x000b)
#define DCM_RequestedMediaApplicationProfile     DcmTagKey(0x2200, 0x000c)
#define DCM_ReferencedStorageMediaSequence       DcmTagKey(0x2200, 0x000d)
#define DCM_FailureAttributes                    DcmTagKey(0x2200, 0x000e)
#define DCM_AllowLossyCompression                DcmTagKey(0x2200, 0x000f)
#define DCM_RequestPriority                      DcmTagKey(0x2200, 0x0020)
#define DCM_RTImageLabel                         DcmTagKey(0x3002, 0x0002)
#define DCM_RTImageName                          DcmTagKey(0x3002, 0x0003)
#define DCM_RTImageDescription                   DcmTagKey(0x3002, 0x0004)
#define DCM_ReportedValuesOrigin                 DcmTagKey(0x3002, 0x000a)
#define DCM_RTImagePlane                         DcmTagKey(0x3002, 0x000c)
#define DCM_XRayImageReceptorTranslation         DcmTagKey(0x3002, 0x000d)
#define DCM_XRayImageReceptorAngle               DcmTagKey(0x3002, 0x000e)
#define DCM_RTImageOrientation                   DcmTagKey(0x3002, 0x0010)
#define DCM_ImagePlanePixelSpacing               DcmTagKey(0x3002, 0x0011)
#define DCM_RTImagePosition                      DcmTagKey(0x3002, 0x0012)
#define DCM_RadiationMachineName                 DcmTagKey(0x3002, 0x0020)
#define DCM_RadiationMachineSAD                  DcmTagKey(0x3002, 0x0022)
#define DCM_RadiationMachineSSD                  DcmTagKey(0x3002, 0x0024)
#define DCM_RTImageSID                           DcmTagKey(0x3002, 0x0026)
#define DCM_SourceToReferenceObjectDistance      DcmTagKey(0x3002, 0x0028)
#define DCM_FractionNumber                       DcmTagKey(0x3002, 0x0029)
#define DCM_ExposureSequence                     DcmTagKey(0x3002, 0x0030)
#define DCM_MetersetExposure                     DcmTagKey(0x3002, 0x0032)
#define DCM_DiaphragmPosition                    DcmTagKey(0x3002, 0x0034)
#define DCM_FluenceMapSequence                   DcmTagKey(0x3002, 0x0040)
#define DCM_FluenceDataSource                    DcmTagKey(0x3002, 0x0041)
#define DCM_FluenceDataScale                     DcmTagKey(0x3002, 0x0042)
#define DCM_PrimaryFluenceModeSequence           DcmTagKey(0x3002, 0x0050)
#define DCM_FluenceMode                          DcmTagKey(0x3002, 0x0051)
#define DCM_FluenceModeID                        DcmTagKey(0x3002, 0x0052)
#define DCM_SelectedFrameNumber                  DcmTagKey(0x3002, 0x0100)
#define DCM_SelectedFrameFunctionalGroupsSequence DcmTagKey(0x3002, 0x0101)
#define DCM_RTImageFrameGeneralContentSequence   DcmTagKey(0x3002, 0x0102)
#define DCM_RTImageFrameContextSequence          DcmTagKey(0x3002, 0x0103)
#define DCM_RTImageScopeSequence                 DcmTagKey(0x3002, 0x0104)
#define DCM_BeamModifierCoordinatesPresenceFlag  DcmTagKey(0x3002, 0x0105)
#define DCM_StartCumulativeMeterset              DcmTagKey(0x3002, 0x0106)
#define DCM_StopCumulativeMeterset               DcmTagKey(0x3002, 0x0107)
#define DCM_RTAcquisitionPatientPositionSequence DcmTagKey(0x3002, 0x0108)
#define DCM_RTImageFrameImagingDevicePositionSequence DcmTagKey(0x3002, 0x0109)
#define DCM_RTImageFramekVRadiationAcquisitionSequence DcmTagKey(0x3002, 0x010a)
#define DCM_RTImageFrameMVRadiationAcquisitionSequence DcmTagKey(0x3002, 0x010b)
#define DCM_RTImageFrameRadiationAcquisitionSequence DcmTagKey(0x3002, 0x010c)
#define DCM_ImagingSourcePositionSequence        DcmTagKey(0x3002, 0x010d)
#define DCM_ImageReceptorPositionSequence        DcmTagKey(0x3002, 0x010e)
#define DCM_DevicePositionToEquipmentMappingMatrix DcmTagKey(0x3002, 0x010f)
#define DCM_DevicePositionParameterSequence      DcmTagKey(0x3002, 0x0110)
#define DCM_ImagingSourceLocationSpecificationType DcmTagKey(0x3002, 0x0111)
#define DCM_ImagingDeviceLocationMatrixSequence  DcmTagKey(0x3002, 0x0112)
#define DCM_ImagingDeviceLocationParameterSequence DcmTagKey(0x3002, 0x0113)
#define DCM_ImagingApertureSequence              DcmTagKey(0x3002, 0x0114)
#define DCM_ImagingApertureSpecificationType     DcmTagKey(0x3002, 0x0115)
#define DCM_NumberOfAcquisitionDevices           DcmTagKey(0x3002, 0x0116)
#define DCM_AcquisitionDeviceSequence            DcmTagKey(0x3002, 0x0117)
#define DCM_AcquisitionTaskSequence              DcmTagKey(0x3002, 0x0118)
#define DCM_AcquisitionTaskWorkitemCodeSequence  DcmTagKey(0x3002, 0x0119)
#define DCM_AcquisitionSubtaskSequence           DcmTagKey(0x3002, 0x011a)
#define DCM_SubtaskWorkitemCodeSequence          DcmTagKey(0x3002, 0x011b)
#define DCM_AcquisitionTaskIndex                 DcmTagKey(0x3002, 0x011c)
#define DCM_AcquisitionSubtaskIndex              DcmTagKey(0x3002, 0x011d)
#define DCM_ReferencedBaselineParametersRTRadiationInstanceSequence DcmTagKey(0x3002, 0x011e)
#define DCM_PositionAcquisitionTemplateIdentificationSequence DcmTagKey(0x3002, 0x011f)
#define DCM_PositionAcquisitionTemplateID        DcmTagKey(0x3002, 0x0120)
#define DCM_PositionAcquisitionTemplateName      DcmTagKey(0x3002, 0x0121)
#define DCM_PositionAcquisitionTemplateCodeSequence DcmTagKey(0x3002, 0x0122)
#define DCM_PositionAcquisitionTemplateDescription DcmTagKey(0x3002, 0x0123)
#define DCM_AcquisitionTaskApplicabilitySequence DcmTagKey(0x3002, 0x0124)
#define DCM_ProjectionImagingAcquisitionParameterSequence DcmTagKey(0x3002, 0x0125)
#define DCM_CTImagingAcquisitionParameterSequence DcmTagKey(0x3002, 0x0126)
#define DCM_KVImagingGenerationParametersSequence DcmTagKey(0x3002, 0x0127)
#define DCM_MVImagingGenerationParametersSequence DcmTagKey(0x3002, 0x0128)
#define DCM_AcquisitionSignalType                DcmTagKey(0x3002, 0x0129)
#define DCM_AcquisitionMethod                    DcmTagKey(0x3002, 0x012a)
#define DCM_ScanStartPositionSequence            DcmTagKey(0x3002, 0x012b)
#define DCM_ScanStopPositionSequence             DcmTagKey(0x3002, 0x012c)
#define DCM_ImagingSourceToBeamModifierDefinitionPlaneDistance DcmTagKey(0x3002, 0x012d)
#define DCM_ScanArcType                          DcmTagKey(0x3002, 0x012e)
#define DCM_DetectorPositioningType              DcmTagKey(0x3002, 0x012f)
#define DCM_AdditionalRTAccessoryDeviceSequence  DcmTagKey(0x3002, 0x0130)
#define DCM_DeviceSpecificAcquisitionParameterSequence DcmTagKey(0x3002, 0x0131)
#define DCM_ReferencedPositionReferenceInstanceSequence DcmTagKey(0x3002, 0x0132)
#define DCM_EnergyDerivationCodeSequence         DcmTagKey(0x3002, 0x0133)
#define DCM_MaximumCumulativeMetersetExposure    DcmTagKey(0x3002, 0x0134)
#define DCM_AcquisitionInitiationSequence        DcmTagKey(0x3002, 0x0135)
#define DCM_RTConeBeamImagingGeometrySequence    DcmTagKey(0x3002, 0x0136)
#define DCM_DVHType                              DcmTagKey(0x3004, 0x0001)
#define DCM_DoseUnits                            DcmTagKey(0x3004, 0x0002)
#define DCM_DoseType                             DcmTagKey(0x3004, 0x0004)
#define DCM_SpatialTransformOfDose               DcmTagKey(0x3004, 0x0005)
#define DCM_DoseComment                          DcmTagKey(0x3004, 0x0006)
#define DCM_NormalizationPoint                   DcmTagKey(0x3004, 0x0008)
#define DCM_DoseSummationType                    DcmTagKey(0x3004, 0x000a)
#define DCM_GridFrameOffsetVector                DcmTagKey(0x3004, 0x000c)
#define DCM_DoseGridScaling                      DcmTagKey(0x3004, 0x000e)
#define DCM_RETIRED_RTDoseROISequence            DcmTagKey(0x3004, 0x0010)
#define DCM_DoseValue                            DcmTagKey(0x3004, 0x0012)
#define DCM_TissueHeterogeneityCorrection        DcmTagKey(0x3004, 0x0014)
#define DCM_RecommendedIsodoseLevelSequence      DcmTagKey(0x3004, 0x0016)
#define DCM_DVHNormalizationPoint                DcmTagKey(0x3004, 0x0040)
#define DCM_DVHNormalizationDoseValue            DcmTagKey(0x3004, 0x0042)
#define DCM_DVHSequence                          DcmTagKey(0x3004, 0x0050)
#define DCM_DVHDoseScaling                       DcmTagKey(0x3004, 0x0052)
#define DCM_DVHVolumeUnits                       DcmTagKey(0x3004, 0x0054)
#define DCM_DVHNumberOfBins                      DcmTagKey(0x3004, 0x0056)
#define DCM_DVHData                              DcmTagKey(0x3004, 0x0058)
#define DCM_DVHReferencedROISequence             DcmTagKey(0x3004, 0x0060)
#define DCM_DVHROIContributionType               DcmTagKey(0x3004, 0x0062)
#define DCM_DVHMinimumDose                       DcmTagKey(0x3004, 0x0070)
#define DCM_DVHMaximumDose                       DcmTagKey(0x3004, 0x0072)
#define DCM_DVHMeanDose                          DcmTagKey(0x3004, 0x0074)
#define DCM_StructureSetLabel                    DcmTagKey(0x3006, 0x0002)
#define DCM_StructureSetName                     DcmTagKey(0x3006, 0x0004)
#define DCM_StructureSetDescription              DcmTagKey(0x3006, 0x0006)
#define DCM_StructureSetDate                     DcmTagKey(0x3006, 0x0008)
#define DCM_StructureSetTime                     DcmTagKey(0x3006, 0x0009)
#define DCM_ReferencedFrameOfReferenceSequence   DcmTagKey(0x3006, 0x0010)
#define DCM_RTReferencedStudySequence            DcmTagKey(0x3006, 0x0012)
#define DCM_RTReferencedSeriesSequence           DcmTagKey(0x3006, 0x0014)
#define DCM_ContourImageSequence                 DcmTagKey(0x3006, 0x0016)
#define DCM_PredecessorStructureSetSequence      DcmTagKey(0x3006, 0x0018)
#define DCM_StructureSetROISequence              DcmTagKey(0x3006, 0x0020)
#define DCM_ROINumber                            DcmTagKey(0x3006, 0x0022)
#define DCM_ReferencedFrameOfReferenceUID        DcmTagKey(0x3006, 0x0024)
#define DCM_ROIName                              DcmTagKey(0x3006, 0x0026)
#define DCM_ROIDescription                       DcmTagKey(0x3006, 0x0028)
#define DCM_ROIDisplayColor                      DcmTagKey(0x3006, 0x002a)
#define DCM_ROIVolume                            DcmTagKey(0x3006, 0x002c)
#define DCM_ROIDateTime                          DcmTagKey(0x3006, 0x002d)
#define DCM_ROIObservationDateTime               DcmTagKey(0x3006, 0x002e)
#define DCM_RTRelatedROISequence                 DcmTagKey(0x3006, 0x0030)
#define DCM_RTROIRelationship                    DcmTagKey(0x3006, 0x0033)
#define DCM_ROIGenerationAlgorithm               DcmTagKey(0x3006, 0x0036)
#define DCM_ROIDerivationAlgorithmIdentificationSequence DcmTagKey(0x3006, 0x0037)
#define DCM_ROIGenerationDescription             DcmTagKey(0x3006, 0x0038)
#define DCM_ROIContourSequence                   DcmTagKey(0x3006, 0x0039)
#define DCM_ContourSequence                      DcmTagKey(0x3006, 0x0040)
#define DCM_ContourGeometricType                 DcmTagKey(0x3006, 0x0042)
#define DCM_RETIRED_ContourSlabThickness         DcmTagKey(0x3006, 0x0044)
#define DCM_RETIRED_ContourOffsetVector          DcmTagKey(0x3006, 0x0045)
#define DCM_NumberOfContourPoints                DcmTagKey(0x3006, 0x0046)
#define DCM_ContourNumber                        DcmTagKey(0x3006, 0x0048)
#define DCM_RETIRED_AttachedContours             DcmTagKey(0x3006, 0x0049)
#define DCM_SourcePixelPlanesCharacteristicsSequence DcmTagKey(0x3006, 0x004a)
#define DCM_SourceSeriesSequence                 DcmTagKey(0x3006, 0x004b)
#define DCM_SourceSeriesInformationSequence      DcmTagKey(0x3006, 0x004c)
#define DCM_ROICreatorSequence                   DcmTagKey(0x3006, 0x004d)
#define DCM_ROIInterpreterSequence               DcmTagKey(0x3006, 0x004e)
#define DCM_ROIObservationContextCodeSequence    DcmTagKey(0x3006, 0x004f)
#define DCM_ContourData                          DcmTagKey(0x3006, 0x0050)
#define DCM_RTROIObservationsSequence            DcmTagKey(0x3006, 0x0080)
#define DCM_ObservationNumber                    DcmTagKey(0x3006, 0x0082)
#define DCM_ReferencedROINumber                  DcmTagKey(0x3006, 0x0084)
#define DCM_RETIRED_ROIObservationLabel          DcmTagKey(0x3006, 0x0085)
#define DCM_RTROIIdentificationCodeSequence      DcmTagKey(0x3006, 0x0086)
#define DCM_RETIRED_ROIObservationDescription    DcmTagKey(0x3006, 0x0088)
#define DCM_RelatedRTROIObservationsSequence     DcmTagKey(0x3006, 0x00a0)
#define DCM_RTROIInterpretedType                 DcmTagKey(0x3006, 0x00a4)
#define DCM_ROIInterpreter                       DcmTagKey(0x3006, 0x00a6)
#define DCM_ROIPhysicalPropertiesSequence        DcmTagKey(0x3006, 0x00b0)
#define DCM_ROIPhysicalProperty                  DcmTagKey(0x3006, 0x00b2)
#define DCM_ROIPhysicalPropertyValue             DcmTagKey(0x3006, 0x00b4)
#define DCM_ROIElementalCompositionSequence      DcmTagKey(0x3006, 0x00b6)
#define DCM_ROIElementalCompositionAtomicNumber  DcmTagKey(0x3006, 0x00b7)
#define DCM_ROIElementalCompositionAtomicMassFraction DcmTagKey(0x3006, 0x00b8)
#define DCM_RETIRED_AdditionalRTROIIdentificationCodeSequence DcmTagKey(0x3006, 0x00b9)
#define DCM_RETIRED_FrameOfReferenceRelationshipSequence DcmTagKey(0x3006, 0x00c0)
#define DCM_RETIRED_RelatedFrameOfReferenceUID   DcmTagKey(0x3006, 0x00c2)
#define DCM_RETIRED_FrameOfReferenceTransformationType DcmTagKey(0x3006, 0x00c4)
#define DCM_FrameOfReferenceTransformationMatrix DcmTagKey(0x3006, 0x00c6)
#define DCM_FrameOfReferenceTransformationComment DcmTagKey(0x3006, 0x00c8)
#define DCM_PatientLocationCoordinatesSequence   DcmTagKey(0x3006, 0x00c9)
#define DCM_PatientLocationCoordinatesCodeSequence DcmTagKey(0x3006, 0x00ca)
#define DCM_PatientSupportPositionSequence       DcmTagKey(0x3006, 0x00cb)
#define DCM_MeasuredDoseReferenceSequence        DcmTagKey(0x3008, 0x0010)
#define DCM_MeasuredDoseDescription              DcmTagKey(0x3008, 0x0012)
#define DCM_MeasuredDoseType                     DcmTagKey(0x3008, 0x0014)
#define DCM_MeasuredDoseValue                    DcmTagKey(0x3008, 0x0016)
#define DCM_TreatmentSessionBeamSequence         DcmTagKey(0x3008, 0x0020)
#define DCM_TreatmentSessionIonBeamSequence      DcmTagKey(0x3008, 0x0021)
#define DCM_CurrentFractionNumber                DcmTagKey(0x3008, 0x0022)
#define DCM_TreatmentControlPointDate            DcmTagKey(0x3008, 0x0024)
#define DCM_TreatmentControlPointTime            DcmTagKey(0x3008, 0x0025)
#define DCM_TreatmentTerminationStatus           DcmTagKey(0x3008, 0x002a)
#define DCM_RETIRED_TreatmentTerminationCode     DcmTagKey(0x3008, 0x002b)
#define DCM_TreatmentVerificationStatus          DcmTagKey(0x3008, 0x002c)
#define DCM_ReferencedTreatmentRecordSequence    DcmTagKey(0x3008, 0x0030)
#define DCM_SpecifiedPrimaryMeterset             DcmTagKey(0x3008, 0x0032)
#define DCM_SpecifiedSecondaryMeterset           DcmTagKey(0x3008, 0x0033)
#define DCM_DeliveredPrimaryMeterset             DcmTagKey(0x3008, 0x0036)
#define DCM_DeliveredSecondaryMeterset           DcmTagKey(0x3008, 0x0037)
#define DCM_SpecifiedTreatmentTime               DcmTagKey(0x3008, 0x003a)
#define DCM_DeliveredTreatmentTime               DcmTagKey(0x3008, 0x003b)
#define DCM_ControlPointDeliverySequence         DcmTagKey(0x3008, 0x0040)
#define DCM_IonControlPointDeliverySequence      DcmTagKey(0x3008, 0x0041)
#define DCM_SpecifiedMeterset                    DcmTagKey(0x3008, 0x0042)
#define DCM_DeliveredMeterset                    DcmTagKey(0x3008, 0x0044)
#define DCM_MetersetRateSet                      DcmTagKey(0x3008, 0x0045)
#define DCM_MetersetRateDelivered                DcmTagKey(0x3008, 0x0046)
#define DCM_ScanSpotMetersetsDelivered           DcmTagKey(0x3008, 0x0047)
#define DCM_DoseRateDelivered                    DcmTagKey(0x3008, 0x0048)
#define DCM_TreatmentSummaryCalculatedDoseReferenceSequence DcmTagKey(0x3008, 0x0050)
#define DCM_CumulativeDoseToDoseReference        DcmTagKey(0x3008, 0x0052)
#define DCM_FirstTreatmentDate                   DcmTagKey(0x3008, 0x0054)
#define DCM_MostRecentTreatmentDate              DcmTagKey(0x3008, 0x0056)
#define DCM_NumberOfFractionsDelivered           DcmTagKey(0x3008, 0x005a)
#define DCM_OverrideSequence                     DcmTagKey(0x3008, 0x0060)
#define DCM_ParameterSequencePointer             DcmTagKey(0x3008, 0x0061)
#define DCM_OverrideParameterPointer             DcmTagKey(0x3008, 0x0062)
#define DCM_ParameterItemIndex                   DcmTagKey(0x3008, 0x0063)
#define DCM_MeasuredDoseReferenceNumber          DcmTagKey(0x3008, 0x0064)
#define DCM_ParameterPointer                     DcmTagKey(0x3008, 0x0065)
#define DCM_OverrideReason                       DcmTagKey(0x3008, 0x0066)
#define DCM_ParameterValueNumber                 DcmTagKey(0x3008, 0x0067)
#define DCM_CorrectedParameterSequence           DcmTagKey(0x3008, 0x0068)
#define DCM_CorrectionValue                      DcmTagKey(0x3008, 0x006a)
#define DCM_CalculatedDoseReferenceSequence      DcmTagKey(0x3008, 0x0070)
#define DCM_CalculatedDoseReferenceNumber        DcmTagKey(0x3008, 0x0072)
#define DCM_CalculatedDoseReferenceDescription   DcmTagKey(0x3008, 0x0074)
#define DCM_CalculatedDoseReferenceDoseValue     DcmTagKey(0x3008, 0x0076)
#define DCM_StartMeterset                        DcmTagKey(0x3008, 0x0078)
#define DCM_EndMeterset                          DcmTagKey(0x3008, 0x007a)
#define DCM_ReferencedMeasuredDoseReferenceSequence DcmTagKey(0x3008, 0x0080)
#define DCM_ReferencedMeasuredDoseReferenceNumber DcmTagKey(0x3008, 0x0082)
#define DCM_ReferencedCalculatedDoseReferenceSequence DcmTagKey(0x3008, 0x0090)
#define DCM_ReferencedCalculatedDoseReferenceNumber DcmTagKey(0x3008, 0x0092)
#define DCM_BeamLimitingDeviceLeafPairsSequence  DcmTagKey(0x3008, 0x00a0)
#define DCM_EnhancedRTBeamLimitingDeviceSequence DcmTagKey(0x3008, 0x00a1)
#define DCM_EnhancedRTBeamLimitingOpeningSequence DcmTagKey(0x3008, 0x00a2)
#define DCM_EnhancedRTBeamLimitingDeviceDefinitionFlag DcmTagKey(0x3008, 0x00a3)
#define DCM_ParallelRTBeamDelimiterOpeningExtents DcmTagKey(0x3008, 0x00a4)
#define DCM_RecordedWedgeSequence                DcmTagKey(0x3008, 0x00b0)
#define DCM_RecordedCompensatorSequence          DcmTagKey(0x3008, 0x00c0)
#define DCM_RecordedBlockSequence                DcmTagKey(0x3008, 0x00d0)
#define DCM_RecordedBlockSlabSequence            DcmTagKey(0x3008, 0x00d1)
#define DCM_TreatmentSummaryMeasuredDoseReferenceSequence DcmTagKey(0x3008, 0x00e0)
#define DCM_RecordedSnoutSequence                DcmTagKey(0x3008, 0x00f0)
#define DCM_RecordedRangeShifterSequence         DcmTagKey(0x3008, 0x00f2)
#define DCM_RecordedLateralSpreadingDeviceSequence DcmTagKey(0x3008, 0x00f4)
#define DCM_RecordedRangeModulatorSequence       DcmTagKey(0x3008, 0x00f6)
#define DCM_RecordedSourceSequence               DcmTagKey(0x3008, 0x0100)
#define DCM_SourceSerialNumber                   DcmTagKey(0x3008, 0x0105)
#define DCM_TreatmentSessionApplicationSetupSequence DcmTagKey(0x3008, 0x0110)
#define DCM_ApplicationSetupCheck                DcmTagKey(0x3008, 0x0116)
#define DCM_RecordedBrachyAccessoryDeviceSequence DcmTagKey(0x3008, 0x0120)
#define DCM_ReferencedBrachyAccessoryDeviceNumber DcmTagKey(0x3008, 0x0122)
#define DCM_RecordedChannelSequence              DcmTagKey(0x3008, 0x0130)
#define DCM_SpecifiedChannelTotalTime            DcmTagKey(0x3008, 0x0132)
#define DCM_DeliveredChannelTotalTime            DcmTagKey(0x3008, 0x0134)
#define DCM_SpecifiedNumberOfPulses              DcmTagKey(0x3008, 0x0136)
#define DCM_DeliveredNumberOfPulses              DcmTagKey(0x3008, 0x0138)
#define DCM_SpecifiedPulseRepetitionInterval     DcmTagKey(0x3008, 0x013a)
#define DCM_DeliveredPulseRepetitionInterval     DcmTagKey(0x3008, 0x013c)
#define DCM_RecordedSourceApplicatorSequence     DcmTagKey(0x3008, 0x0140)
#define DCM_ReferencedSourceApplicatorNumber     DcmTagKey(0x3008, 0x0142)
#define DCM_RecordedChannelShieldSequence        DcmTagKey(0x3008, 0x0150)
#define DCM_ReferencedChannelShieldNumber        DcmTagKey(0x3008, 0x0152)
#define DCM_BrachyControlPointDeliveredSequence  DcmTagKey(0x3008, 0x0160)
#define DCM_SafePositionExitDate                 DcmTagKey(0x3008, 0x0162)
#define DCM_SafePositionExitTime                 DcmTagKey(0x3008, 0x0164)
#define DCM_SafePositionReturnDate               DcmTagKey(0x3008, 0x0166)
#define DCM_SafePositionReturnTime               DcmTagKey(0x3008, 0x0168)
#define DCM_PulseSpecificBrachyControlPointDeliveredSequence DcmTagKey(0x3008, 0x0171)
#define DCM_PulseNumber                          DcmTagKey(0x3008, 0x0172)
#define DCM_BrachyPulseControlPointDeliveredSequence DcmTagKey(0x3008, 0x0173)
#define DCM_CurrentTreatmentStatus               DcmTagKey(0x3008, 0x0200)
#define DCM_TreatmentStatusComment               DcmTagKey(0x3008, 0x0202)
#define DCM_FractionGroupSummarySequence         DcmTagKey(0x3008, 0x0220)
#define DCM_ReferencedFractionNumber             DcmTagKey(0x3008, 0x0223)
#define DCM_FractionGroupType                    DcmTagKey(0x3008, 0x0224)
#define DCM_BeamStopperPosition                  DcmTagKey(0x3008, 0x0230)
#define DCM_FractionStatusSummarySequence        DcmTagKey(0x3008, 0x0240)
#define DCM_TreatmentDate                        DcmTagKey(0x3008, 0x0250)
#define DCM_TreatmentTime                        DcmTagKey(0x3008, 0x0251)
#define DCM_RTPlanLabel                          DcmTagKey(0x300a, 0x0002)
#define DCM_RTPlanName                           DcmTagKey(0x300a, 0x0003)
#define DCM_RTPlanDescription                    DcmTagKey(0x300a, 0x0004)
#define DCM_RTPlanDate                           DcmTagKey(0x300a, 0x0006)
#define DCM_RTPlanTime                           DcmTagKey(0x300a, 0x0007)
#define DCM_TreatmentProtocols                   DcmTagKey(0x300a, 0x0009)
#define DCM_PlanIntent                           DcmTagKey(0x300a, 0x000a)
#define DCM_RETIRED_TreatmentSites               DcmTagKey(0x300a, 0x000b)
#define DCM_RTPlanGeometry                       DcmTagKey(0x300a, 0x000c)
#define DCM_PrescriptionDescription              DcmTagKey(0x300a, 0x000e)
#define DCM_DoseReferenceSequence                DcmTagKey(0x300a, 0x0010)
#define DCM_DoseReferenceNumber                  DcmTagKey(0x300a, 0x0012)
#define DCM_DoseReferenceUID                     DcmTagKey(0x300a, 0x0013)
#define DCM_DoseReferenceStructureType           DcmTagKey(0x300a, 0x0014)
#define DCM_NominalBeamEnergyUnit                DcmTagKey(0x300a, 0x0015)
#define DCM_DoseReferenceDescription             DcmTagKey(0x300a, 0x0016)
#define DCM_DoseReferencePointCoordinates        DcmTagKey(0x300a, 0x0018)
#define DCM_NominalPriorDose                     DcmTagKey(0x300a, 0x001a)
#define DCM_DoseReferenceType                    DcmTagKey(0x300a, 0x0020)
#define DCM_ConstraintWeight                     DcmTagKey(0x300a, 0x0021)
#define DCM_DeliveryWarningDose                  DcmTagKey(0x300a, 0x0022)
#define DCM_DeliveryMaximumDose                  DcmTagKey(0x300a, 0x0023)
#define DCM_TargetMinimumDose                    DcmTagKey(0x300a, 0x0025)
#define DCM_TargetPrescriptionDose               DcmTagKey(0x300a, 0x0026)
#define DCM_TargetMaximumDose                    DcmTagKey(0x300a, 0x0027)
#define DCM_TargetUnderdoseVolumeFraction        DcmTagKey(0x300a, 0x0028)
#define DCM_OrganAtRiskFullVolumeDose            DcmTagKey(0x300a, 0x002a)
#define DCM_OrganAtRiskLimitDose                 DcmTagKey(0x300a, 0x002b)
#define DCM_OrganAtRiskMaximumDose               DcmTagKey(0x300a, 0x002c)
#define DCM_OrganAtRiskOverdoseVolumeFraction    DcmTagKey(0x300a, 0x002d)
#define DCM_ToleranceTableSequence               DcmTagKey(0x300a, 0x0040)
#define DCM_ToleranceTableNumber                 DcmTagKey(0x300a, 0x0042)
#define DCM_ToleranceTableLabel                  DcmTagKey(0x300a, 0x0043)
#define DCM_GantryAngleTolerance                 DcmTagKey(0x300a, 0x0044)
#define DCM_BeamLimitingDeviceAngleTolerance     DcmTagKey(0x300a, 0x0046)
#define DCM_BeamLimitingDeviceToleranceSequence  DcmTagKey(0x300a, 0x0048)
#define DCM_BeamLimitingDevicePositionTolerance  DcmTagKey(0x300a, 0x004a)
#define DCM_SnoutPositionTolerance               DcmTagKey(0x300a, 0x004b)
#define DCM_PatientSupportAngleTolerance         DcmTagKey(0x300a, 0x004c)
#define DCM_TableTopEccentricAngleTolerance      DcmTagKey(0x300a, 0x004e)
#define DCM_TableTopPitchAngleTolerance          DcmTagKey(0x300a, 0x004f)
#define DCM_TableTopRollAngleTolerance           DcmTagKey(0x300a, 0x0050)
#define DCM_TableTopVerticalPositionTolerance    DcmTagKey(0x300a, 0x0051)
#define DCM_TableTopLongitudinalPositionTolerance DcmTagKey(0x300a, 0x0052)
#define DCM_TableTopLateralPositionTolerance     DcmTagKey(0x300a, 0x0053)
#define DCM_TableTopPositionAlignmentUID         DcmTagKey(0x300a, 0x0054)
#define DCM_RTPlanRelationship                   DcmTagKey(0x300a, 0x0055)
#define DCM_FractionGroupSequence                DcmTagKey(0x300a, 0x0070)
#define DCM_FractionGroupNumber                  DcmTagKey(0x300a, 0x0071)
#define DCM_FractionGroupDescription             DcmTagKey(0x300a, 0x0072)
#define DCM_NumberOfFractionsPlanned             DcmTagKey(0x300a, 0x0078)
#define DCM_NumberOfFractionPatternDigitsPerDay  DcmTagKey(0x300a, 0x0079)
#define DCM_RepeatFractionCycleLength            DcmTagKey(0x300a, 0x007a)
#define DCM_FractionPattern                      DcmTagKey(0x300a, 0x007b)
#define DCM_NumberOfBeams                        DcmTagKey(0x300a, 0x0080)
#define DCM_RETIRED_BeamDoseSpecificationPoint   DcmTagKey(0x300a, 0x0082)
#define DCM_ReferencedDoseReferenceUID           DcmTagKey(0x300a, 0x0083)
#define DCM_BeamDose                             DcmTagKey(0x300a, 0x0084)
#define DCM_BeamMeterset                         DcmTagKey(0x300a, 0x0086)
#define DCM_BeamDosePointDepth                   DcmTagKey(0x300a, 0x0088)
#define DCM_BeamDosePointEquivalentDepth         DcmTagKey(0x300a, 0x0089)
#define DCM_BeamDosePointSSD                     DcmTagKey(0x300a, 0x008a)
#define DCM_BeamDoseMeaning                      DcmTagKey(0x300a, 0x008b)
#define DCM_BeamDoseVerificationControlPointSequence DcmTagKey(0x300a, 0x008c)
#define DCM_RETIRED_AverageBeamDosePointDepth    DcmTagKey(0x300a, 0x008d)
#define DCM_RETIRED_AverageBeamDosePointEquivalentDepth DcmTagKey(0x300a, 0x008e)
#define DCM_RETIRED_AverageBeamDosePointSSD      DcmTagKey(0x300a, 0x008f)
#define DCM_BeamDoseType                         DcmTagKey(0x300a, 0x0090)
#define DCM_AlternateBeamDose                    DcmTagKey(0x300a, 0x0091)
#define DCM_AlternateBeamDoseType                DcmTagKey(0x300a, 0x0092)
#define DCM_DepthValueAveragingFlag              DcmTagKey(0x300a, 0x0093)
#define DCM_BeamDosePointSourceToExternalContourDistance DcmTagKey(0x300a, 0x0094)
#define DCM_NumberOfBrachyApplicationSetups      DcmTagKey(0x300a, 0x00a0)
#define DCM_BrachyApplicationSetupDoseSpecificationPoint DcmTagKey(0x300a, 0x00a2)
#define DCM_BrachyApplicationSetupDose           DcmTagKey(0x300a, 0x00a4)
#define DCM_BeamSequence                         DcmTagKey(0x300a, 0x00b0)
#define DCM_TreatmentMachineName                 DcmTagKey(0x300a, 0x00b2)
#define DCM_PrimaryDosimeterUnit                 DcmTagKey(0x300a, 0x00b3)
#define DCM_SourceAxisDistance                   DcmTagKey(0x300a, 0x00b4)
#define DCM_BeamLimitingDeviceSequence           DcmTagKey(0x300a, 0x00b6)
#define DCM_RTBeamLimitingDeviceType             DcmTagKey(0x300a, 0x00b8)
#define DCM_SourceToBeamLimitingDeviceDistance   DcmTagKey(0x300a, 0x00ba)
#define DCM_IsocenterToBeamLimitingDeviceDistance DcmTagKey(0x300a, 0x00bb)
#define DCM_NumberOfLeafJawPairs                 DcmTagKey(0x300a, 0x00bc)
#define DCM_LeafPositionBoundaries               DcmTagKey(0x300a, 0x00be)
#define DCM_BeamNumber                           DcmTagKey(0x300a, 0x00c0)
#define DCM_BeamName                             DcmTagKey(0x300a, 0x00c2)
#define DCM_BeamDescription                      DcmTagKey(0x300a, 0x00c3)
#define DCM_BeamType                             DcmTagKey(0x300a, 0x00c4)
#define DCM_BeamDeliveryDurationLimit            DcmTagKey(0x300a, 0x00c5)
#define DCM_RadiationType                        DcmTagKey(0x300a, 0x00c6)
#define DCM_HighDoseTechniqueType                DcmTagKey(0x300a, 0x00c7)
#define DCM_ReferenceImageNumber                 DcmTagKey(0x300a, 0x00c8)
#define DCM_PlannedVerificationImageSequence     DcmTagKey(0x300a, 0x00ca)
#define DCM_ImagingDeviceSpecificAcquisitionParameters DcmTagKey(0x300a, 0x00cc)
#define DCM_TreatmentDeliveryType                DcmTagKey(0x300a, 0x00ce)
#define DCM_NumberOfWedges                       DcmTagKey(0x300a, 0x00d0)
#define DCM_WedgeSequence                        DcmTagKey(0x300a, 0x00d1)
#define DCM_WedgeNumber                          DcmTagKey(0x300a, 0x00d2)
#define DCM_WedgeType                            DcmTagKey(0x300a, 0x00d3)
#define DCM_WedgeID                              DcmTagKey(0x300a, 0x00d4)
#define DCM_WedgeAngle                           DcmTagKey(0x300a, 0x00d5)
#define DCM_WedgeFactor                          DcmTagKey(0x300a, 0x00d6)
#define DCM_TotalWedgeTrayWaterEquivalentThickness DcmTagKey(0x300a, 0x00d7)
#define DCM_WedgeOrientation                     DcmTagKey(0x300a, 0x00d8)
#define DCM_IsocenterToWedgeTrayDistance         DcmTagKey(0x300a, 0x00d9)
#define DCM_SourceToWedgeTrayDistance            DcmTagKey(0x300a, 0x00da)
#define DCM_WedgeThinEdgePosition                DcmTagKey(0x300a, 0x00db)
#define DCM_BolusID                              DcmTagKey(0x300a, 0x00dc)
#define DCM_BolusDescription                     DcmTagKey(0x300a, 0x00dd)
#define DCM_EffectiveWedgeAngle                  DcmTagKey(0x300a, 0x00de)
#define DCM_NumberOfCompensators                 DcmTagKey(0x300a, 0x00e0)
#define DCM_MaterialID                           DcmTagKey(0x300a, 0x00e1)
#define DCM_TotalCompensatorTrayFactor           DcmTagKey(0x300a, 0x00e2)
#define DCM_CompensatorSequence                  DcmTagKey(0x300a, 0x00e3)
#define DCM_CompensatorNumber                    DcmTagKey(0x300a, 0x00e4)
#define DCM_CompensatorID                        DcmTagKey(0x300a, 0x00e5)
#define DCM_SourceToCompensatorTrayDistance      DcmTagKey(0x300a, 0x00e6)
#define DCM_CompensatorRows                      DcmTagKey(0x300a, 0x00e7)
#define DCM_CompensatorColumns                   DcmTagKey(0x300a, 0x00e8)
#define DCM_CompensatorPixelSpacing              DcmTagKey(0x300a, 0x00e9)
#define DCM_CompensatorPosition                  DcmTagKey(0x300a, 0x00ea)
#define DCM_CompensatorTransmissionData          DcmTagKey(0x300a, 0x00eb)
#define DCM_CompensatorThicknessData             DcmTagKey(0x300a, 0x00ec)
#define DCM_NumberOfBoli                         DcmTagKey(0x300a, 0x00ed)
#define DCM_CompensatorType                      DcmTagKey(0x300a, 0x00ee)
#define DCM_CompensatorTrayID                    DcmTagKey(0x300a, 0x00ef)
#define DCM_NumberOfBlocks                       DcmTagKey(0x300a, 0x00f0)
#define DCM_TotalBlockTrayFactor                 DcmTagKey(0x300a, 0x00f2)
#define DCM_TotalBlockTrayWaterEquivalentThickness DcmTagKey(0x300a, 0x00f3)
#define DCM_BlockSequence                        DcmTagKey(0x300a, 0x00f4)
#define DCM_BlockTrayID                          DcmTagKey(0x300a, 0x00f5)
#define DCM_SourceToBlockTrayDistance            DcmTagKey(0x300a, 0x00f6)
#define DCM_IsocenterToBlockTrayDistance         DcmTagKey(0x300a, 0x00f7)
#define DCM_BlockType                            DcmTagKey(0x300a, 0x00f8)
#define DCM_AccessoryCode                        DcmTagKey(0x300a, 0x00f9)
#define DCM_BlockDivergence                      DcmTagKey(0x300a, 0x00fa)
#define DCM_BlockMountingPosition                DcmTagKey(0x300a, 0x00fb)
#define DCM_BlockNumber                          DcmTagKey(0x300a, 0x00fc)
#define DCM_BlockName                            DcmTagKey(0x300a, 0x00fe)
#define DCM_BlockThickness                       DcmTagKey(0x300a, 0x0100)
#define DCM_BlockTransmission                    DcmTagKey(0x300a, 0x0102)
#define DCM_BlockNumberOfPoints                  DcmTagKey(0x300a, 0x0104)
#define DCM_BlockData                            DcmTagKey(0x300a, 0x0106)
#define DCM_ApplicatorSequence                   DcmTagKey(0x300a, 0x0107)
#define DCM_ApplicatorID                         DcmTagKey(0x300a, 0x0108)
#define DCM_ApplicatorType                       DcmTagKey(0x300a, 0x0109)
#define DCM_ApplicatorDescription                DcmTagKey(0x300a, 0x010a)
#define DCM_CumulativeDoseReferenceCoefficient   DcmTagKey(0x300a, 0x010c)
#define DCM_FinalCumulativeMetersetWeight        DcmTagKey(0x300a, 0x010e)
#define DCM_NumberOfControlPoints                DcmTagKey(0x300a, 0x0110)
#define DCM_ControlPointSequence                 DcmTagKey(0x300a, 0x0111)
#define DCM_ControlPointIndex                    DcmTagKey(0x300a, 0x0112)
#define DCM_NominalBeamEnergy                    DcmTagKey(0x300a, 0x0114)
#define DCM_DoseRateSet                          DcmTagKey(0x300a, 0x0115)
#define DCM_WedgePositionSequence                DcmTagKey(0x300a, 0x0116)
#define DCM_WedgePosition                        DcmTagKey(0x300a, 0x0118)
#define DCM_BeamLimitingDevicePositionSequence   DcmTagKey(0x300a, 0x011a)
#define DCM_LeafJawPositions                     DcmTagKey(0x300a, 0x011c)
#define DCM_GantryAngle                          DcmTagKey(0x300a, 0x011e)
#define DCM_GantryRotationDirection              DcmTagKey(0x300a, 0x011f)
#define DCM_BeamLimitingDeviceAngle              DcmTagKey(0x300a, 0x0120)
#define DCM_BeamLimitingDeviceRotationDirection  DcmTagKey(0x300a, 0x0121)
#define DCM_PatientSupportAngle                  DcmTagKey(0x300a, 0x0122)
#define DCM_PatientSupportRotationDirection      DcmTagKey(0x300a, 0x0123)
#define DCM_TableTopEccentricAxisDistance        DcmTagKey(0x300a, 0x0124)
#define DCM_TableTopEccentricAngle               DcmTagKey(0x300a, 0x0125)
#define DCM_TableTopEccentricRotationDirection   DcmTagKey(0x300a, 0x0126)
#define DCM_TableTopVerticalPosition             DcmTagKey(0x300a, 0x0128)
#define DCM_TableTopLongitudinalPosition         DcmTagKey(0x300a, 0x0129)
#define DCM_TableTopLateralPosition              DcmTagKey(0x300a, 0x012a)
#define DCM_IsocenterPosition                    DcmTagKey(0x300a, 0x012c)
#define DCM_SurfaceEntryPoint                    DcmTagKey(0x300a, 0x012e)
#define DCM_SourceToSurfaceDistance              DcmTagKey(0x300a, 0x0130)
#define DCM_AverageBeamDosePointSourceToExternalContourDistance DcmTagKey(0x300a, 0x0131)
#define DCM_SourceToExternalContourDistance      DcmTagKey(0x300a, 0x0132)
#define DCM_ExternalContourEntryPoint            DcmTagKey(0x300a, 0x0133)
#define DCM_CumulativeMetersetWeight             DcmTagKey(0x300a, 0x0134)
#define DCM_TableTopPitchAngle                   DcmTagKey(0x300a, 0x0140)
#define DCM_TableTopPitchRotationDirection       DcmTagKey(0x300a, 0x0142)
#define DCM_TableTopRollAngle                    DcmTagKey(0x300a, 0x0144)
#define DCM_TableTopRollRotationDirection        DcmTagKey(0x300a, 0x0146)
#define DCM_HeadFixationAngle                    DcmTagKey(0x300a, 0x0148)
#define DCM_GantryPitchAngle                     DcmTagKey(0x300a, 0x014a)
#define DCM_GantryPitchRotationDirection         DcmTagKey(0x300a, 0x014c)
#define DCM_GantryPitchAngleTolerance            DcmTagKey(0x300a, 0x014e)
#define DCM_FixationEye                          DcmTagKey(0x300a, 0x0150)
#define DCM_ChairHeadFramePosition               DcmTagKey(0x300a, 0x0151)
#define DCM_HeadFixationAngleTolerance           DcmTagKey(0x300a, 0x0152)
#define DCM_ChairHeadFramePositionTolerance      DcmTagKey(0x300a, 0x0153)
#define DCM_FixationLightAzimuthalAngleTolerance DcmTagKey(0x300a, 0x0154)
#define DCM_FixationLightPolarAngleTolerance     DcmTagKey(0x300a, 0x0155)
#define DCM_PatientSetupSequence                 DcmTagKey(0x300a, 0x0180)
#define DCM_PatientSetupNumber                   DcmTagKey(0x300a, 0x0182)
#define DCM_PatientSetupLabel                    DcmTagKey(0x300a, 0x0183)
#define DCM_PatientAdditionalPosition            DcmTagKey(0x300a, 0x0184)
#define DCM_FixationDeviceSequence               DcmTagKey(0x300a, 0x0190)
#define DCM_FixationDeviceType                   DcmTagKey(0x300a, 0x0192)
#define DCM_FixationDeviceLabel                  DcmTagKey(0x300a, 0x0194)
#define DCM_FixationDeviceDescription            DcmTagKey(0x300a, 0x0196)
#define DCM_FixationDevicePosition               DcmTagKey(0x300a, 0x0198)
#define DCM_FixationDevicePitchAngle             DcmTagKey(0x300a, 0x0199)
#define DCM_FixationDeviceRollAngle              DcmTagKey(0x300a, 0x019a)
#define DCM_ShieldingDeviceSequence              DcmTagKey(0x300a, 0x01a0)
#define DCM_ShieldingDeviceType                  DcmTagKey(0x300a, 0x01a2)
#define DCM_ShieldingDeviceLabel                 DcmTagKey(0x300a, 0x01a4)
#define DCM_ShieldingDeviceDescription           DcmTagKey(0x300a, 0x01a6)
#define DCM_ShieldingDevicePosition              DcmTagKey(0x300a, 0x01a8)
#define DCM_SetupTechnique                       DcmTagKey(0x300a, 0x01b0)
#define DCM_SetupTechniqueDescription            DcmTagKey(0x300a, 0x01b2)
#define DCM_SetupDeviceSequence                  DcmTagKey(0x300a, 0x01b4)
#define DCM_SetupDeviceType                      DcmTagKey(0x300a, 0x01b6)
#define DCM_SetupDeviceLabel                     DcmTagKey(0x300a, 0x01b8)
#define DCM_SetupDeviceDescription               DcmTagKey(0x300a, 0x01ba)
#define DCM_SetupDeviceParameter                 DcmTagKey(0x300a, 0x01bc)
#define DCM_SetupReferenceDescription            DcmTagKey(0x300a, 0x01d0)
#define DCM_TableTopVerticalSetupDisplacement    DcmTagKey(0x300a, 0x01d2)
#define DCM_TableTopLongitudinalSetupDisplacement DcmTagKey(0x300a, 0x01d4)
#define DCM_TableTopLateralSetupDisplacement     DcmTagKey(0x300a, 0x01d6)
#define DCM_BrachyTreatmentTechnique             DcmTagKey(0x300a, 0x0200)
#define DCM_BrachyTreatmentType                  DcmTagKey(0x300a, 0x0202)
#define DCM_TreatmentMachineSequence             DcmTagKey(0x300a, 0x0206)
#define DCM_SourceSequence                       DcmTagKey(0x300a, 0x0210)
#define DCM_SourceNumber                         DcmTagKey(0x300a, 0x0212)
#define DCM_SourceType                           DcmTagKey(0x300a, 0x0214)
#define DCM_SourceManufacturer                   DcmTagKey(0x300a, 0x0216)
#define DCM_ActiveSourceDiameter                 DcmTagKey(0x300a, 0x0218)
#define DCM_ActiveSourceLength                   DcmTagKey(0x300a, 0x021a)
#define DCM_SourceModelID                        DcmTagKey(0x300a, 0x021b)
#define DCM_SourceDescription                    DcmTagKey(0x300a, 0x021c)
#define DCM_SourceEncapsulationNominalThickness  DcmTagKey(0x300a, 0x0222)
#define DCM_SourceEncapsulationNominalTransmission DcmTagKey(0x300a, 0x0224)
#define DCM_SourceIsotopeName                    DcmTagKey(0x300a, 0x0226)
#define DCM_SourceIsotopeHalfLife                DcmTagKey(0x300a, 0x0228)
#define DCM_SourceStrengthUnits                  DcmTagKey(0x300a, 0x0229)
#define DCM_ReferenceAirKermaRate                DcmTagKey(0x300a, 0x022a)
#define DCM_SourceStrength                       DcmTagKey(0x300a, 0x022b)
#define DCM_SourceStrengthReferenceDate          DcmTagKey(0x300a, 0x022c)
#define DCM_SourceStrengthReferenceTime          DcmTagKey(0x300a, 0x022e)
#define DCM_ApplicationSetupSequence             DcmTagKey(0x300a, 0x0230)
#define DCM_ApplicationSetupType                 DcmTagKey(0x300a, 0x0232)
#define DCM_ApplicationSetupNumber               DcmTagKey(0x300a, 0x0234)
#define DCM_ApplicationSetupName                 DcmTagKey(0x300a, 0x0236)
#define DCM_ApplicationSetupManufacturer         DcmTagKey(0x300a, 0x0238)
#define DCM_TemplateNumber                       DcmTagKey(0x300a, 0x0240)
#define DCM_TemplateType                         DcmTagKey(0x300a, 0x0242)
#define DCM_TemplateName                         DcmTagKey(0x300a, 0x0244)
#define DCM_TotalReferenceAirKerma               DcmTagKey(0x300a, 0x0250)
#define DCM_BrachyAccessoryDeviceSequence        DcmTagKey(0x300a, 0x0260)
#define DCM_BrachyAccessoryDeviceNumber          DcmTagKey(0x300a, 0x0262)
#define DCM_BrachyAccessoryDeviceID              DcmTagKey(0x300a, 0x0263)
#define DCM_BrachyAccessoryDeviceType            DcmTagKey(0x300a, 0x0264)
#define DCM_BrachyAccessoryDeviceName            DcmTagKey(0x300a, 0x0266)
#define DCM_BrachyAccessoryDeviceNominalThickness DcmTagKey(0x300a, 0x026a)
#define DCM_BrachyAccessoryDeviceNominalTransmission DcmTagKey(0x300a, 0x026c)
#define DCM_ChannelEffectiveLength               DcmTagKey(0x300a, 0x0271)
#define DCM_ChannelInnerLength                   DcmTagKey(0x300a, 0x0272)
#define DCM_AfterloaderChannelID                 DcmTagKey(0x300a, 0x0273)
#define DCM_SourceApplicatorTipLength            DcmTagKey(0x300a, 0x0274)
#define DCM_ChannelSequence                      DcmTagKey(0x300a, 0x0280)
#define DCM_ChannelNumber                        DcmTagKey(0x300a, 0x0282)
#define DCM_ChannelLength                        DcmTagKey(0x300a, 0x0284)
#define DCM_ChannelTotalTime                     DcmTagKey(0x300a, 0x0286)
#define DCM_SourceMovementType                   DcmTagKey(0x300a, 0x0288)
#define DCM_NumberOfPulses                       DcmTagKey(0x300a, 0x028a)
#define DCM_PulseRepetitionInterval              DcmTagKey(0x300a, 0x028c)
#define DCM_SourceApplicatorNumber               DcmTagKey(0x300a, 0x0290)
#define DCM_SourceApplicatorID                   DcmTagKey(0x300a, 0x0291)
#define DCM_SourceApplicatorType                 DcmTagKey(0x300a, 0x0292)
#define DCM_SourceApplicatorName                 DcmTagKey(0x300a, 0x0294)
#define DCM_SourceApplicatorLength               DcmTagKey(0x300a, 0x0296)
#define DCM_SourceApplicatorManufacturer         DcmTagKey(0x300a, 0x0298)
#define DCM_SourceApplicatorWallNominalThickness DcmTagKey(0x300a, 0x029c)
#define DCM_SourceApplicatorWallNominalTransmission DcmTagKey(0x300a, 0x029e)
#define DCM_SourceApplicatorStepSize             DcmTagKey(0x300a, 0x02a0)
#define DCM_ApplicatorShapeReferencedROINumber   DcmTagKey(0x300a, 0x02a1)
#define DCM_TransferTubeNumber                   DcmTagKey(0x300a, 0x02a2)
#define DCM_TransferTubeLength                   DcmTagKey(0x300a, 0x02a4)
#define DCM_ChannelShieldSequence                DcmTagKey(0x300a, 0x02b0)
#define DCM_ChannelShieldNumber                  DcmTagKey(0x300a, 0x02b2)
#define DCM_ChannelShieldID                      DcmTagKey(0x300a, 0x02b3)
#define DCM_ChannelShieldName                    DcmTagKey(0x300a, 0x02b4)
#define DCM_ChannelShieldNominalThickness        DcmTagKey(0x300a, 0x02b8)
#define DCM_ChannelShieldNominalTransmission     DcmTagKey(0x300a, 0x02ba)
#define DCM_FinalCumulativeTimeWeight            DcmTagKey(0x300a, 0x02c8)
#define DCM_BrachyControlPointSequence           DcmTagKey(0x300a, 0x02d0)
#define DCM_ControlPointRelativePosition         DcmTagKey(0x300a, 0x02d2)
#define DCM_ControlPoint3DPosition               DcmTagKey(0x300a, 0x02d4)
#define DCM_CumulativeTimeWeight                 DcmTagKey(0x300a, 0x02d6)
#define DCM_CompensatorDivergence                DcmTagKey(0x300a, 0x02e0)
#define DCM_CompensatorMountingPosition          DcmTagKey(0x300a, 0x02e1)
#define DCM_SourceToCompensatorDistance          DcmTagKey(0x300a, 0x02e2)
#define DCM_TotalCompensatorTrayWaterEquivalentThickness DcmTagKey(0x300a, 0x02e3)
#define DCM_IsocenterToCompensatorTrayDistance   DcmTagKey(0x300a, 0x02e4)
#define DCM_CompensatorColumnOffset              DcmTagKey(0x300a, 0x02e5)
#define DCM_IsocenterToCompensatorDistances      DcmTagKey(0x300a, 0x02e6)
#define DCM_CompensatorRelativeStoppingPowerRatio DcmTagKey(0x300a, 0x02e7)
#define DCM_CompensatorMillingToolDiameter       DcmTagKey(0x300a, 0x02e8)
#define DCM_IonRangeCompensatorSequence          DcmTagKey(0x300a, 0x02ea)
#define DCM_CompensatorDescription               DcmTagKey(0x300a, 0x02eb)
#define DCM_CompensatorSurfaceRepresentationFlag DcmTagKey(0x300a, 0x02ec)
#define DCM_RadiationMassNumber                  DcmTagKey(0x300a, 0x0302)
#define DCM_RadiationAtomicNumber                DcmTagKey(0x300a, 0x0304)
#define DCM_RadiationChargeState                 DcmTagKey(0x300a, 0x0306)
#define DCM_ScanMode                             DcmTagKey(0x300a, 0x0308)
#define DCM_ModulatedScanModeType                DcmTagKey(0x300a, 0x0309)
#define DCM_VirtualSourceAxisDistances           DcmTagKey(0x300a, 0x030a)
#define DCM_SnoutSequence                        DcmTagKey(0x300a, 0x030c)
#define DCM_SnoutPosition                        DcmTagKey(0x300a, 0x030d)
#define DCM_SnoutID                              DcmTagKey(0x300a, 0x030f)
#define DCM_NumberOfRangeShifters                DcmTagKey(0x300a, 0x0312)
#define DCM_RangeShifterSequence                 DcmTagKey(0x300a, 0x0314)
#define DCM_RangeShifterNumber                   DcmTagKey(0x300a, 0x0316)
#define DCM_RangeShifterID                       DcmTagKey(0x300a, 0x0318)
#define DCM_RangeShifterType                     DcmTagKey(0x300a, 0x0320)
#define DCM_RangeShifterDescription              DcmTagKey(0x300a, 0x0322)
#define DCM_NumberOfLateralSpreadingDevices      DcmTagKey(0x300a, 0x0330)
#define DCM_LateralSpreadingDeviceSequence       DcmTagKey(0x300a, 0x0332)
#define DCM_LateralSpreadingDeviceNumber         DcmTagKey(0x300a, 0x0334)
#define DCM_LateralSpreadingDeviceID             DcmTagKey(0x300a, 0x0336)
#define DCM_LateralSpreadingDeviceType           DcmTagKey(0x300a, 0x0338)
#define DCM_LateralSpreadingDeviceDescription    DcmTagKey(0x300a, 0x033a)
#define DCM_LateralSpreadingDeviceWaterEquivalentThickness DcmTagKey(0x300a, 0x033c)
#define DCM_NumberOfRangeModulators              DcmTagKey(0x300a, 0x0340)
#define DCM_RangeModulatorSequence               DcmTagKey(0x300a, 0x0342)
#define DCM_RangeModulatorNumber                 DcmTagKey(0x300a, 0x0344)
#define DCM_RangeModulatorID                     DcmTagKey(0x300a, 0x0346)
#define DCM_RangeModulatorType                   DcmTagKey(0x300a, 0x0348)
#define DCM_RangeModulatorDescription            DcmTagKey(0x300a, 0x034a)
#define DCM_BeamCurrentModulationID              DcmTagKey(0x300a, 0x034c)
#define DCM_PatientSupportType                   DcmTagKey(0x300a, 0x0350)
#define DCM_PatientSupportID                     DcmTagKey(0x300a, 0x0352)
#define DCM_PatientSupportAccessoryCode          DcmTagKey(0x300a, 0x0354)
#define DCM_TrayAccessoryCode                    DcmTagKey(0x300a, 0x0355)
#define DCM_FixationLightAzimuthalAngle          DcmTagKey(0x300a, 0x0356)
#define DCM_FixationLightPolarAngle              DcmTagKey(0x300a, 0x0358)
#define DCM_MetersetRate                         DcmTagKey(0x300a, 0x035a)
#define DCM_RangeShifterSettingsSequence         DcmTagKey(0x300a, 0x0360)
#define DCM_RangeShifterSetting                  DcmTagKey(0x300a, 0x0362)
#define DCM_IsocenterToRangeShifterDistance      DcmTagKey(0x300a, 0x0364)
#define DCM_RangeShifterWaterEquivalentThickness DcmTagKey(0x300a, 0x0366)
#define DCM_LateralSpreadingDeviceSettingsSequence DcmTagKey(0x300a, 0x0370)
#define DCM_LateralSpreadingDeviceSetting        DcmTagKey(0x300a, 0x0372)
#define DCM_IsocenterToLateralSpreadingDeviceDistance DcmTagKey(0x300a, 0x0374)
#define DCM_RangeModulatorSettingsSequence       DcmTagKey(0x300a, 0x0380)
#define DCM_RangeModulatorGatingStartValue       DcmTagKey(0x300a, 0x0382)
#define DCM_RangeModulatorGatingStopValue        DcmTagKey(0x300a, 0x0384)
#define DCM_RangeModulatorGatingStartWaterEquivalentThickness DcmTagKey(0x300a, 0x0386)
#define DCM_RangeModulatorGatingStopWaterEquivalentThickness DcmTagKey(0x300a, 0x0388)
#define DCM_IsocenterToRangeModulatorDistance    DcmTagKey(0x300a, 0x038a)
#define DCM_ScanSpotTimeOffset                   DcmTagKey(0x300a, 0x038f)
#define DCM_ScanSpotTuneID                       DcmTagKey(0x300a, 0x0390)
#define DCM_ScanSpotPrescribedIndices            DcmTagKey(0x300a, 0x0391)
#define DCM_NumberOfScanSpotPositions            DcmTagKey(0x300a, 0x0392)
#define DCM_ScanSpotReordered                    DcmTagKey(0x300a, 0x0393)
#define DCM_ScanSpotPositionMap                  DcmTagKey(0x300a, 0x0394)
#define DCM_ScanSpotReorderingAllowed            DcmTagKey(0x300a, 0x0395)
#define DCM_ScanSpotMetersetWeights              DcmTagKey(0x300a, 0x0396)
#define DCM_ScanningSpotSize                     DcmTagKey(0x300a, 0x0398)
#define DCM_ScanSpotSizesDelivered               DcmTagKey(0x300a, 0x0399)
#define DCM_NumberOfPaintings                    DcmTagKey(0x300a, 0x039a)
#define DCM_IonToleranceTableSequence            DcmTagKey(0x300a, 0x03a0)
#define DCM_IonBeamSequence                      DcmTagKey(0x300a, 0x03a2)
#define DCM_IonBeamLimitingDeviceSequence        DcmTagKey(0x300a, 0x03a4)
#define DCM_IonBlockSequence                     DcmTagKey(0x300a, 0x03a6)
#define DCM_IonControlPointSequence              DcmTagKey(0x300a, 0x03a8)
#define DCM_IonWedgeSequence                     DcmTagKey(0x300a, 0x03aa)
#define DCM_IonWedgePositionSequence             DcmTagKey(0x300a, 0x03ac)
#define DCM_ReferencedSetupImageSequence         DcmTagKey(0x300a, 0x0401)
#define DCM_SetupImageComment                    DcmTagKey(0x300a, 0x0402)
#define DCM_MotionSynchronizationSequence        DcmTagKey(0x300a, 0x0410)
#define DCM_ControlPointOrientation              DcmTagKey(0x300a, 0x0412)
#define DCM_GeneralAccessorySequence             DcmTagKey(0x300a, 0x0420)
#define DCM_GeneralAccessoryID                   DcmTagKey(0x300a, 0x0421)
#define DCM_GeneralAccessoryDescription          DcmTagKey(0x300a, 0x0422)
#define DCM_GeneralAccessoryType                 DcmTagKey(0x300a, 0x0423)
#define DCM_GeneralAccessoryNumber               DcmTagKey(0x300a, 0x0424)
#define DCM_SourceToGeneralAccessoryDistance     DcmTagKey(0x300a, 0x0425)
#define DCM_IsocenterToGeneralAccessoryDistance  DcmTagKey(0x300a, 0x0426)
#define DCM_ApplicatorGeometrySequence           DcmTagKey(0x300a, 0x0431)
#define DCM_ApplicatorApertureShape              DcmTagKey(0x300a, 0x0432)
#define DCM_ApplicatorOpening                    DcmTagKey(0x300a, 0x0433)
#define DCM_ApplicatorOpeningX                   DcmTagKey(0x300a, 0x0434)
#define DCM_ApplicatorOpeningY                   DcmTagKey(0x300a, 0x0435)
#define DCM_SourceToApplicatorMountingPositionDistance DcmTagKey(0x300a, 0x0436)
#define DCM_NumberOfBlockSlabItems               DcmTagKey(0x300a, 0x0440)
#define DCM_BlockSlabSequence                    DcmTagKey(0x300a, 0x0441)
#define DCM_BlockSlabThickness                   DcmTagKey(0x300a, 0x0442)
#define DCM_BlockSlabNumber                      DcmTagKey(0x300a, 0x0443)
#define DCM_DeviceMotionControlSequence          DcmTagKey(0x300a, 0x0450)
#define DCM_DeviceMotionExecutionMode            DcmTagKey(0x300a, 0x0451)
#define DCM_DeviceMotionObservationMode          DcmTagKey(0x300a, 0x0452)
#define DCM_DeviceMotionParameterCodeSequence    DcmTagKey(0x300a, 0x0453)
#define DCM_DistalDepthFraction                  DcmTagKey(0x300a, 0x0501)
#define DCM_DistalDepth                          DcmTagKey(0x300a, 0x0502)
#define DCM_NominalRangeModulationFractions      DcmTagKey(0x300a, 0x0503)
#define DCM_NominalRangeModulatedRegionDepths    DcmTagKey(0x300a, 0x0504)
#define DCM_DepthDoseParametersSequence          DcmTagKey(0x300a, 0x0505)
#define DCM_DeliveredDepthDoseParametersSequence DcmTagKey(0x300a, 0x0506)
#define DCM_DeliveredDistalDepthFraction         DcmTagKey(0x300a, 0x0507)
#define DCM_DeliveredDistalDepth                 DcmTagKey(0x300a, 0x0508)
#define DCM_DeliveredNominalRangeModulationFractions DcmTagKey(0x300a, 0x0509)
#define DCM_DeliveredNominalRangeModulatedRegionDepths DcmTagKey(0x300a, 0x0510)
#define DCM_DeliveredReferenceDoseDefinition     DcmTagKey(0x300a, 0x0511)
#define DCM_ReferenceDoseDefinition              DcmTagKey(0x300a, 0x0512)
#define DCM_RTControlPointIndex                  DcmTagKey(0x300a, 0x0600)
#define DCM_RadiationGenerationModeIndex         DcmTagKey(0x300a, 0x0601)
#define DCM_ReferencedDefinedDeviceIndex         DcmTagKey(0x300a, 0x0602)
#define DCM_RadiationDoseIdentificationIndex     DcmTagKey(0x300a, 0x0603)
#define DCM_NumberOfRTControlPoints              DcmTagKey(0x300a, 0x0604)
#define DCM_ReferencedRadiationGenerationModeIndex DcmTagKey(0x300a, 0x0605)
#define DCM_TreatmentPositionIndex               DcmTagKey(0x300a, 0x0606)
#define DCM_ReferencedDeviceIndex                DcmTagKey(0x300a, 0x0607)
#define DCM_TreatmentPositionGroupLabel          DcmTagKey(0x300a, 0x0608)
#define DCM_TreatmentPositionGroupUID            DcmTagKey(0x300a, 0x0609)
#define DCM_TreatmentPositionGroupSequence       DcmTagKey(0x300a, 0x060a)
#define DCM_ReferencedTreatmentPositionIndex     DcmTagKey(0x300a, 0x060b)
#define DCM_ReferencedRadiationDoseIdentificationIndex DcmTagKey(0x300a, 0x060c)
#define DCM_RTAccessoryHolderWaterEquivalentThickness DcmTagKey(0x300a, 0x060d)
#define DCM_ReferencedRTAccessoryHolderDeviceIndex DcmTagKey(0x300a, 0x060e)
#define DCM_RTAccessoryHolderSlotExistenceFlag   DcmTagKey(0x300a, 0x060f)
#define DCM_RTAccessoryHolderSlotSequence        DcmTagKey(0x300a, 0x0610)
#define DCM_RTAccessoryHolderSlotID              DcmTagKey(0x300a, 0x0611)
#define DCM_RTAccessoryHolderSlotDistance        DcmTagKey(0x300a, 0x0612)
#define DCM_RTAccessorySlotDistance              DcmTagKey(0x300a, 0x0613)
#define DCM_RTAccessoryHolderDefinitionSequence  DcmTagKey(0x300a, 0x0614)
#define DCM_RTAccessoryDeviceSlotID              DcmTagKey(0x300a, 0x0615)
#define DCM_RTRadiationSequence                  DcmTagKey(0x300a, 0x0616)
#define DCM_RadiationDoseSequence                DcmTagKey(0x300a, 0x0617)
#define DCM_RadiationDoseIdentificationSequence  DcmTagKey(0x300a, 0x0618)
#define DCM_RadiationDoseIdentificationLabel     DcmTagKey(0x300a, 0x0619)
#define DCM_ReferenceDoseType                    DcmTagKey(0x300a, 0x061a)
#define DCM_PrimaryDoseValueIndicator            DcmTagKey(0x300a, 0x061b)
#define DCM_DoseValuesSequence                   DcmTagKey(0x300a, 0x061c)
#define DCM_DoseValuePurpose                     DcmTagKey(0x300a, 0x061d)
#define DCM_ReferenceDosePointCoordinates        DcmTagKey(0x300a, 0x061e)
#define DCM_RadiationDoseValuesParametersSequence DcmTagKey(0x300a, 0x061f)
#define DCM_MetersetToDoseMappingSequence        DcmTagKey(0x300a, 0x0620)
#define DCM_ExpectedInVivoMeasurementValuesSequence DcmTagKey(0x300a, 0x0621)
#define DCM_ExpectedInVivoMeasurementValueIndex  DcmTagKey(0x300a, 0x0622)
#define DCM_RadiationDoseInVivoMeasurementLabel  DcmTagKey(0x300a, 0x0623)
#define DCM_RadiationDoseCentralAxisDisplacement DcmTagKey(0x300a, 0x0624)
#define DCM_RadiationDoseValue                   DcmTagKey(0x300a, 0x0625)
#define DCM_RadiationDoseSourceToSkinDistance    DcmTagKey(0x300a, 0x0626)
#define DCM_RadiationDoseMeasurementPointCoordinates DcmTagKey(0x300a, 0x0627)
#define DCM_RadiationDoseSourceToExternalContourDistance DcmTagKey(0x300a, 0x0628)
#define DCM_RTToleranceSetSequence               DcmTagKey(0x300a, 0x0629)
#define DCM_RTToleranceSetLabel                  DcmTagKey(0x300a, 0x062a)
#define DCM_AttributeToleranceValuesSequence     DcmTagKey(0x300a, 0x062b)
#define DCM_ToleranceValue                       DcmTagKey(0x300a, 0x062c)
#define DCM_PatientSupportPositionToleranceSequence DcmTagKey(0x300a, 0x062d)
#define DCM_TreatmentTimeLimit                   DcmTagKey(0x300a, 0x062e)
#define DCM_CArmPhotonElectronControlPointSequence DcmTagKey(0x300a, 0x062f)
#define DCM_ReferencedRTRadiationSequence        DcmTagKey(0x300a, 0x0630)
#define DCM_ReferencedRTInstanceSequence         DcmTagKey(0x300a, 0x0631)
#define DCM_RETIRED_ReferencedRTPatientSetupSequence DcmTagKey(0x300a, 0x0632)
#define DCM_SourceToPatientSurfaceDistance       DcmTagKey(0x300a, 0x0634)
#define DCM_TreatmentMachineSpecialModeCodeSequence DcmTagKey(0x300a, 0x0635)
#define DCM_IntendedNumberOfFractions            DcmTagKey(0x300a, 0x0636)
#define DCM_RTRadiationSetIntent                 DcmTagKey(0x300a, 0x0637)
#define DCM_RTRadiationPhysicalAndGeometricContentDetailFlag DcmTagKey(0x300a, 0x0638)
#define DCM_RTRecordFlag                         DcmTagKey(0x300a, 0x0639)
#define DCM_TreatmentDeviceIdentificationSequence DcmTagKey(0x300a, 0x063a)
#define DCM_ReferencedRTPhysicianIntentSequence  DcmTagKey(0x300a, 0x063b)
#define DCM_CumulativeMeterset                   DcmTagKey(0x300a, 0x063c)
#define DCM_DeliveryRate                         DcmTagKey(0x300a, 0x063d)
#define DCM_DeliveryRateUnitSequence             DcmTagKey(0x300a, 0x063e)
#define DCM_TreatmentPositionSequence            DcmTagKey(0x300a, 0x063f)
#define DCM_RadiationSourceAxisDistance          DcmTagKey(0x300a, 0x0640)
#define DCM_NumberOfRTBeamLimitingDevices        DcmTagKey(0x300a, 0x0641)
#define DCM_RTBeamLimitingDeviceProximalDistance DcmTagKey(0x300a, 0x0642)
#define DCM_RTBeamLimitingDeviceDistalDistance   DcmTagKey(0x300a, 0x0643)
#define DCM_ParallelRTBeamDelimiterDeviceOrientationLabelCodeSequence DcmTagKey(0x300a, 0x0644)
#define DCM_BeamModifierOrientationAngle         DcmTagKey(0x300a, 0x0645)
#define DCM_FixedRTBeamDelimiterDeviceSequence   DcmTagKey(0x300a, 0x0646)
#define DCM_ParallelRTBeamDelimiterDeviceSequence DcmTagKey(0x300a, 0x0647)
#define DCM_NumberOfParallelRTBeamDelimiters     DcmTagKey(0x300a, 0x0648)
#define DCM_ParallelRTBeamDelimiterBoundaries    DcmTagKey(0x300a, 0x0649)
#define DCM_ParallelRTBeamDelimiterPositions     DcmTagKey(0x300a, 0x064a)
#define DCM_RTBeamLimitingDeviceOffset           DcmTagKey(0x300a, 0x064b)
#define DCM_RTBeamDelimiterGeometrySequence      DcmTagKey(0x300a, 0x064c)
#define DCM_RTBeamLimitingDeviceDefinitionSequence DcmTagKey(0x300a, 0x064d)
#define DCM_ParallelRTBeamDelimiterOpeningMode   DcmTagKey(0x300a, 0x064e)
#define DCM_ParallelRTBeamDelimiterLeafMountingSide DcmTagKey(0x300a, 0x064f)
#define DCM_RETIRED_PatientSetupUID              DcmTagKey(0x300a, 0x0650)
#define DCM_WedgeDefinitionSequence              DcmTagKey(0x300a, 0x0651)
#define DCM_RadiationBeamWedgeAngle              DcmTagKey(0x300a, 0x0652)
#define DCM_RadiationBeamWedgeThinEdgeDistance   DcmTagKey(0x300a, 0x0653)
#define DCM_RadiationBeamEffectiveWedgeAngle     DcmTagKey(0x300a, 0x0654)
#define DCM_NumberOfWedgePositions               DcmTagKey(0x300a, 0x0655)
#define DCM_RTBeamLimitingDeviceOpeningSequence  DcmTagKey(0x300a, 0x0656)
#define DCM_NumberOfRTBeamLimitingDeviceOpenings DcmTagKey(0x300a, 0x0657)
#define DCM_RadiationDosimeterUnitSequence       DcmTagKey(0x300a, 0x0658)
#define DCM_RTDeviceDistanceReferenceLocationCodeSequence DcmTagKey(0x300a, 0x0659)
#define DCM_RadiationDeviceConfigurationAndCommissioningKeySequence DcmTagKey(0x300a, 0x065a)
#define DCM_PatientSupportPositionParameterSequence DcmTagKey(0x300a, 0x065b)
#define DCM_PatientSupportPositionSpecificationMethod DcmTagKey(0x300a, 0x065c)
#define DCM_PatientSupportPositionDeviceParameterSequence DcmTagKey(0x300a, 0x065d)
#define DCM_DeviceOrderIndex                     DcmTagKey(0x300a, 0x065e)
#define DCM_PatientSupportPositionParameterOrderIndex DcmTagKey(0x300a, 0x065f)
#define DCM_PatientSupportPositionDeviceToleranceSequence DcmTagKey(0x300a, 0x0660)
#define DCM_PatientSupportPositionToleranceOrderIndex DcmTagKey(0x300a, 0x0661)
#define DCM_CompensatorDefinitionSequence        DcmTagKey(0x300a, 0x0662)
#define DCM_CompensatorMapOrientation            DcmTagKey(0x300a, 0x0663)
#define DCM_CompensatorProximalThicknessMap      DcmTagKey(0x300a, 0x0664)
#define DCM_CompensatorDistalThicknessMap        DcmTagKey(0x300a, 0x0665)
#define DCM_CompensatorBasePlaneOffset           DcmTagKey(0x300a, 0x0666)
#define DCM_CompensatorShapeFabricationCodeSequence DcmTagKey(0x300a, 0x0667)
#define DCM_CompensatorShapeSequence             DcmTagKey(0x300a, 0x0668)
#define DCM_RadiationBeamCompensatorMillingToolDiameter DcmTagKey(0x300a, 0x0669)
#define DCM_BlockDefinitionSequence              DcmTagKey(0x300a, 0x066a)
#define DCM_BlockEdgeData                        DcmTagKey(0x300a, 0x066b)
#define DCM_BlockOrientation                     DcmTagKey(0x300a, 0x066c)
#define DCM_RadiationBeamBlockThickness          DcmTagKey(0x300a, 0x066d)
#define DCM_RadiationBeamBlockSlabThickness      DcmTagKey(0x300a, 0x066e)
#define DCM_BlockEdgeDataSequence                DcmTagKey(0x300a, 0x066f)
#define DCM_NumberOfRTAccessoryHolders           DcmTagKey(0x300a, 0x0670)
#define DCM_GeneralAccessoryDefinitionSequence   DcmTagKey(0x300a, 0x0671)
#define DCM_NumberOfGeneralAccessories           DcmTagKey(0x300a, 0x0672)
#define DCM_BolusDefinitionSequence              DcmTagKey(0x300a, 0x0673)
#define DCM_NumberOfBoluses                      DcmTagKey(0x300a, 0x0674)
#define DCM_EquipmentFrameOfReferenceUID         DcmTagKey(0x300a, 0x0675)
#define DCM_EquipmentFrameOfReferenceDescription DcmTagKey(0x300a, 0x0676)
#define DCM_EquipmentReferencePointCoordinatesSequence DcmTagKey(0x300a, 0x0677)
#define DCM_EquipmentReferencePointCodeSequence  DcmTagKey(0x300a, 0x0678)
#define DCM_RTBeamLimitingDeviceAngle            DcmTagKey(0x300a, 0x0679)
#define DCM_SourceRollAngle                      DcmTagKey(0x300a, 0x067a)
#define DCM_RadiationGenerationModeSequence      DcmTagKey(0x300a, 0x067b)
#define DCM_RadiationGenerationModeLabel         DcmTagKey(0x300a, 0x067c)
#define DCM_RadiationGenerationModeDescription   DcmTagKey(0x300a, 0x067d)
#define DCM_RadiationGenerationModeMachineCodeSequence DcmTagKey(0x300a, 0x067e)
#define DCM_RadiationTypeCodeSequence            DcmTagKey(0x300a, 0x067f)
#define DCM_NominalEnergy                        DcmTagKey(0x300a, 0x0680)
#define DCM_MinimumNominalEnergy                 DcmTagKey(0x300a, 0x0681)
#define DCM_MaximumNominalEnergy                 DcmTagKey(0x300a, 0x0682)
#define DCM_RadiationFluenceModifierCodeSequence DcmTagKey(0x300a, 0x0683)
#define DCM_EnergyUnitCodeSequence               DcmTagKey(0x300a, 0x0684)
#define DCM_NumberOfRadiationGenerationModes     DcmTagKey(0x300a, 0x0685)
#define DCM_PatientSupportDevicesSequence        DcmTagKey(0x300a, 0x0686)
#define DCM_NumberOfPatientSupportDevices        DcmTagKey(0x300a, 0x0687)
#define DCM_RTBeamModifierDefinitionDistance     DcmTagKey(0x300a, 0x0688)
#define DCM_BeamAreaLimitSequence                DcmTagKey(0x300a, 0x0689)
#define DCM_ReferencedRTPrescriptionSequence     DcmTagKey(0x300a, 0x068a)
#define DCM_DoseValueInterpretation              DcmTagKey(0x300a, 0x068b)
#define DCM_TreatmentSessionUID                  DcmTagKey(0x300a, 0x0700)
#define DCM_RTRadiationUsage                     DcmTagKey(0x300a, 0x0701)
#define DCM_ReferencedRTRadiationSetSequence     DcmTagKey(0x300a, 0x0702)
#define DCM_ReferencedRTRadiationRecordSequence  DcmTagKey(0x300a, 0x0703)
#define DCM_RTRadiationSetDeliveryNumber         DcmTagKey(0x300a, 0x0704)
#define DCM_ClinicalFractionNumber               DcmTagKey(0x300a, 0x0705)
#define DCM_RTTreatmentFractionCompletionStatus  DcmTagKey(0x300a, 0x0706)
#define DCM_RTRadiationSetUsage                  DcmTagKey(0x300a, 0x0707)
#define DCM_TreatmentDeliveryContinuationFlag    DcmTagKey(0x300a, 0x0708)
#define DCM_TreatmentRecordContentOrigin         DcmTagKey(0x300a, 0x0709)
#define DCM_RTTreatmentTerminationStatus         DcmTagKey(0x300a, 0x0714)
#define DCM_RTTreatmentTerminationReasonCodeSequence DcmTagKey(0x300a, 0x0715)
#define DCM_MachineSpecificTreatmentTerminationCodeSequence DcmTagKey(0x300a, 0x0716)
#define DCM_RTRadiationSalvageRecordControlPointSequence DcmTagKey(0x300a, 0x0722)
#define DCM_StartingMetersetValueKnownFlag       DcmTagKey(0x300a, 0x0723)
#define DCM_TreatmentTerminationDescription      DcmTagKey(0x300a, 0x0730)
#define DCM_TreatmentToleranceViolationSequence  DcmTagKey(0x300a, 0x0731)
#define DCM_TreatmentToleranceViolationCategory  DcmTagKey(0x300a, 0x0732)
#define DCM_TreatmentToleranceViolationAttributeSequence DcmTagKey(0x300a, 0x0733)
#define DCM_TreatmentToleranceViolationDescription DcmTagKey(0x300a, 0x0734)
#define DCM_TreatmentToleranceViolationIdentification DcmTagKey(0x300a, 0x0735)
#define DCM_TreatmentToleranceViolationDateTime  DcmTagKey(0x300a, 0x0736)
#define DCM_RecordedRTControlPointDateTime       DcmTagKey(0x300a, 0x073a)
#define DCM_ReferencedRadiationRTControlPointIndex DcmTagKey(0x300a, 0x073b)
#define DCM_AlternateValueSequence               DcmTagKey(0x300a, 0x073e)
#define DCM_ConfirmationSequence                 DcmTagKey(0x300a, 0x073f)
#define DCM_InterlockSequence                    DcmTagKey(0x300a, 0x0740)
#define DCM_InterlockDateTime                    DcmTagKey(0x300a, 0x0741)
#define DCM_InterlockDescription                 DcmTagKey(0x300a, 0x0742)
#define DCM_InterlockOriginatingDeviceSequence   DcmTagKey(0x300a, 0x0743)
#define DCM_InterlockCodeSequence                DcmTagKey(0x300a, 0x0744)
#define DCM_InterlockResolutionCodeSequence      DcmTagKey(0x300a, 0x0745)
#define DCM_InterlockResolutionUserSequence      DcmTagKey(0x300a, 0x0746)
#define DCM_OverrideDateTime                     DcmTagKey(0x300a, 0x0760)
#define DCM_TreatmentToleranceViolationTypeCodeSequence DcmTagKey(0x300a, 0x0761)
#define DCM_TreatmentToleranceViolationCauseCodeSequence DcmTagKey(0x300a, 0x0762)
#define DCM_MeasuredMetersetToDoseMappingSequence DcmTagKey(0x300a, 0x0772)
#define DCM_ReferencedExpectedInVivoMeasurementValueIndex DcmTagKey(0x300a, 0x0773)
#define DCM_DoseMeasurementDeviceCodeSequence    DcmTagKey(0x300a, 0x0774)
#define DCM_AdditionalParameterRecordingInstanceSequence DcmTagKey(0x300a, 0x0780)
#define DCM_InterlockOriginDescription           DcmTagKey(0x300a, 0x0783)
#define DCM_RTPatientPositionScopeSequence       DcmTagKey(0x300a, 0x0784)
#define DCM_ReferencedTreatmentPositionGroupUID  DcmTagKey(0x300a, 0x0785)
#define DCM_RadiationOrderIndex                  DcmTagKey(0x300a, 0x0786)
#define DCM_OmittedRadiationSequence             DcmTagKey(0x300a, 0x0787)
#define DCM_ReasonForOmissionCodeSequence        DcmTagKey(0x300a, 0x0788)
#define DCM_RTDeliveryStartPatientPositionSequence DcmTagKey(0x300a, 0x0789)
#define DCM_RTTreatmentPreparationPatientPositionSequence DcmTagKey(0x300a, 0x078a)
#define DCM_ReferencedRTTreatmentPreparationSequence DcmTagKey(0x300a, 0x078b)
#define DCM_ReferencedPatientSetupPhotoSequence  DcmTagKey(0x300a, 0x078c)
#define DCM_PatientTreatmentPreparationMethodCodeSequence DcmTagKey(0x300a, 0x078d)
#define DCM_PatientTreatmentPreparationProcedureParameterDescription DcmTagKey(0x300a, 0x078e)
#define DCM_PatientTreatmentPreparationDeviceSequence DcmTagKey(0x300a, 0x078f)
#define DCM_PatientTreatmentPreparationProcedureSequence DcmTagKey(0x300a, 0x0790)
#define DCM_PatientTreatmentPreparationProcedureCodeSequence DcmTagKey(0x300a, 0x0791)
#define DCM_PatientTreatmentPreparationMethodDescription DcmTagKey(0x300a, 0x0792)
#define DCM_PatientTreatmentPreparationProcedureParameterSequence DcmTagKey(0x300a, 0x0793)
#define DCM_PatientSetupPhotoDescription         DcmTagKey(0x300a, 0x0794)
#define DCM_PatientTreatmentPreparationProcedureIndex DcmTagKey(0x300a, 0x0795)
#define DCM_ReferencedPatientSetupProcedureIndex DcmTagKey(0x300a, 0x0796)
#define DCM_RTRadiationTaskSequence              DcmTagKey(0x300a, 0x0797)
#define DCM_RTPatientPositionDisplacementSequence DcmTagKey(0x300a, 0x0798)
#define DCM_RTPatientPositionSequence            DcmTagKey(0x300a, 0x0799)
#define DCM_DisplacementReferenceLabel           DcmTagKey(0x300a, 0x079a)
#define DCM_DisplacementMatrix                   DcmTagKey(0x300a, 0x079b)
#define DCM_PatientSupportDisplacementSequence   DcmTagKey(0x300a, 0x079c)
#define DCM_DisplacementReferenceLocationCodeSequence DcmTagKey(0x300a, 0x079d)
#define DCM_RTRadiationSetDeliveryUsage          DcmTagKey(0x300a, 0x079e)
#define DCM_PatientTreatmentPreparationSequence  DcmTagKey(0x300a, 0x079f)
#define DCM_PatientToEquipmentRelationshipSequence DcmTagKey(0x300a, 0x07a0)
#define DCM_ImagingEquipmentToTreatmentDeliveryDeviceRelationshipSequence DcmTagKey(0x300a, 0x07a1)
#define DCM_ReferencedRTPlanSequence             DcmTagKey(0x300c, 0x0002)
#define DCM_ReferencedBeamSequence               DcmTagKey(0x300c, 0x0004)
#define DCM_ReferencedBeamNumber                 DcmTagKey(0x300c, 0x0006)
#define DCM_ReferencedReferenceImageNumber       DcmTagKey(0x300c, 0x0007)
#define DCM_StartCumulativeMetersetWeight        DcmTagKey(0x300c, 0x0008)
#define DCM_EndCumulativeMetersetWeight          DcmTagKey(0x300c, 0x0009)
#define DCM_ReferencedBrachyApplicationSetupSequence DcmTagKey(0x300c, 0x000a)
#define DCM_ReferencedBrachyApplicationSetupNumber DcmTagKey(0x300c, 0x000c)
#define DCM_ReferencedSourceNumber               DcmTagKey(0x300c, 0x000e)
#define DCM_ReferencedFractionGroupSequence      DcmTagKey(0x300c, 0x0020)
#define DCM_ReferencedFractionGroupNumber        DcmTagKey(0x300c, 0x0022)
#define DCM_ReferencedVerificationImageSequence  DcmTagKey(0x300c, 0x0040)
#define DCM_ReferencedReferenceImageSequence     DcmTagKey(0x300c, 0x0042)
#define DCM_ReferencedDoseReferenceSequence      DcmTagKey(0x300c, 0x0050)
#define DCM_ReferencedDoseReferenceNumber        DcmTagKey(0x300c, 0x0051)
#define DCM_BrachyReferencedDoseReferenceSequence DcmTagKey(0x300c, 0x0055)
#define DCM_ReferencedStructureSetSequence       DcmTagKey(0x300c, 0x0060)
#define DCM_ReferencedPatientSetupNumber         DcmTagKey(0x300c, 0x006a)
#define DCM_ReferencedDoseSequence               DcmTagKey(0x300c, 0x0080)
#define DCM_ReferencedToleranceTableNumber       DcmTagKey(0x300c, 0x00a0)
#define DCM_ReferencedBolusSequence              DcmTagKey(0x300c, 0x00b0)
#define DCM_ReferencedWedgeNumber                DcmTagKey(0x300c, 0x00c0)
#define DCM_ReferencedCompensatorNumber          DcmTagKey(0x300c, 0x00d0)
#define DCM_ReferencedBlockNumber                DcmTagKey(0x300c, 0x00e0)
#define DCM_ReferencedControlPointIndex          DcmTagKey(0x300c, 0x00f0)
#define DCM_ReferencedControlPointSequence       DcmTagKey(0x300c, 0x00f2)
#define DCM_ReferencedStartControlPointIndex     DcmTagKey(0x300c, 0x00f4)
#define DCM_ReferencedStopControlPointIndex      DcmTagKey(0x300c, 0x00f6)
#define DCM_ReferencedRangeShifterNumber         DcmTagKey(0x300c, 0x0100)
#define DCM_ReferencedLateralSpreadingDeviceNumber DcmTagKey(0x300c, 0x0102)
#define DCM_ReferencedRangeModulatorNumber       DcmTagKey(0x300c, 0x0104)
#define DCM_OmittedBeamTaskSequence              DcmTagKey(0x300c, 0x0111)
#define DCM_ReasonForOmission                    DcmTagKey(0x300c, 0x0112)
#define DCM_ReasonForOmissionDescription         DcmTagKey(0x300c, 0x0113)
#define DCM_PrescriptionOverviewSequence         DcmTagKey(0x300c, 0x0114)
#define DCM_TotalPrescriptionDose                DcmTagKey(0x300c, 0x0115)
#define DCM_PlanOverviewSequence                 DcmTagKey(0x300c, 0x0116)
#define DCM_PlanOverviewIndex                    DcmTagKey(0x300c, 0x0117)
#define DCM_ReferencedPlanOverviewIndex          DcmTagKey(0x300c, 0x0118)
#define DCM_NumberOfFractionsIncluded            DcmTagKey(0x300c, 0x0119)
#define DCM_DoseCalibrationConditionsSequence    DcmTagKey(0x300c, 0x0120)
#define DCM_AbsorbedDoseToMetersetRatio          DcmTagKey(0x300c, 0x0121)
#define DCM_DelineatedRadiationFieldSize         DcmTagKey(0x300c, 0x0122)
#define DCM_DoseCalibrationConditionsVerifiedFlag DcmTagKey(0x300c, 0x0123)
#define DCM_CalibrationReferencePointDepth       DcmTagKey(0x300c, 0x0124)
#define DCM_GatingBeamHoldTransitionSequence     DcmTagKey(0x300c, 0x0125)
#define DCM_BeamHoldTransition                   DcmTagKey(0x300c, 0x0126)
#define DCM_BeamHoldTransitionDateTime           DcmTagKey(0x300c, 0x0127)
#define DCM_BeamHoldOriginatingDeviceSequence    DcmTagKey(0x300c, 0x0128)
#define DCM_BeamHoldTransitionTriggerSource      DcmTagKey(0x300c, 0x0129)
#define DCM_ApprovalStatus                       DcmTagKey(0x300e, 0x0002)
#define DCM_ReviewDate                           DcmTagKey(0x300e, 0x0004)
#define DCM_ReviewTime                           DcmTagKey(0x300e, 0x0005)
#define DCM_ReviewerName                         DcmTagKey(0x300e, 0x0008)
#define DCM_RadiobiologicalDoseEffectSequence    DcmTagKey(0x3010, 0x0001)
#define DCM_RadiobiologicalDoseEffectFlag        DcmTagKey(0x3010, 0x0002)
#define DCM_EffectiveDoseCalculationMethodCategoryCodeSequence DcmTagKey(0x3010, 0x0003)
#define DCM_EffectiveDoseCalculationMethodCodeSequence DcmTagKey(0x3010, 0x0004)
#define DCM_EffectiveDoseCalculationMethodDescription DcmTagKey(0x3010, 0x0005)
#define DCM_ConceptualVolumeUID                  DcmTagKey(0x3010, 0x0006)
#define DCM_OriginatingSOPInstanceReferenceSequence DcmTagKey(0x3010, 0x0007)
#define DCM_ConceptualVolumeConstituentSequence  DcmTagKey(0x3010, 0x0008)
#define DCM_EquivalentConceptualVolumeInstanceReferenceSequence DcmTagKey(0x3010, 0x0009)
#define DCM_EquivalentConceptualVolumesSequence  DcmTagKey(0x3010, 0x000a)
#define DCM_ReferencedConceptualVolumeUID        DcmTagKey(0x3010, 0x000b)
#define DCM_ConceptualVolumeCombinationExpression DcmTagKey(0x3010, 0x000c)
#define DCM_ConceptualVolumeConstituentIndex     DcmTagKey(0x3010, 0x000d)
#define DCM_ConceptualVolumeCombinationFlag      DcmTagKey(0x3010, 0x000e)
#define DCM_ConceptualVolumeCombinationDescription DcmTagKey(0x3010, 0x000f)
#define DCM_ConceptualVolumeSegmentationDefinedFlag DcmTagKey(0x3010, 0x0010)
#define DCM_ConceptualVolumeSegmentationReferenceSequence DcmTagKey(0x3010, 0x0011)
#define DCM_ConceptualVolumeConstituentSegmentationReferenceSequence DcmTagKey(0x3010, 0x0012)
#define DCM_ConstituentConceptualVolumeUID       DcmTagKey(0x3010, 0x0013)
#define DCM_DerivationConceptualVolumeSequence   DcmTagKey(0x3010, 0x0014)
#define DCM_SourceConceptualVolumeUID            DcmTagKey(0x3010, 0x0015)
#define DCM_ConceptualVolumeDerivationAlgorithmSequence DcmTagKey(0x3010, 0x0016)
#define DCM_ConceptualVolumeDescription          DcmTagKey(0x3010, 0x0017)
#define DCM_SourceConceptualVolumeSequence       DcmTagKey(0x3010, 0x0018)
#define DCM_AuthorIdentificationSequence         DcmTagKey(0x3010, 0x0019)
#define DCM_ManufacturerModelVersion             DcmTagKey(0x3010, 0x001a)
#define DCM_DeviceAlternateIdentifier            DcmTagKey(0x3010, 0x001b)
#define DCM_DeviceAlternateIdentifierType        DcmTagKey(0x3010, 0x001c)
#define DCM_DeviceAlternateIdentifierFormat      DcmTagKey(0x3010, 0x001d)
#define DCM_SegmentationCreationTemplateLabel    DcmTagKey(0x3010, 0x001e)
#define DCM_SegmentationTemplateUID              DcmTagKey(0x3010, 0x001f)
#define DCM_ReferencedSegmentReferenceIndex      DcmTagKey(0x3010, 0x0020)
#define DCM_SegmentReferenceSequence             DcmTagKey(0x3010, 0x0021)
#define DCM_SegmentReferenceIndex                DcmTagKey(0x3010, 0x0022)
#define DCM_DirectSegmentReferenceSequence       DcmTagKey(0x3010, 0x0023)
#define DCM_CombinationSegmentReferenceSequence  DcmTagKey(0x3010, 0x0024)
#define DCM_ConceptualVolumeSequence             DcmTagKey(0x3010, 0x0025)
#define DCM_SegmentedRTAccessoryDeviceSequence   DcmTagKey(0x3010, 0x0026)
#define DCM_SegmentCharacteristicsSequence       DcmTagKey(0x3010, 0x0027)
#define DCM_RelatedSegmentCharacteristicsSequence DcmTagKey(0x3010, 0x0028)
#define DCM_SegmentCharacteristicsPrecedence     DcmTagKey(0x3010, 0x0029)
#define DCM_RTSegmentAnnotationSequence          DcmTagKey(0x3010, 0x002a)
#define DCM_SegmentAnnotationCategoryCodeSequence DcmTagKey(0x3010, 0x002b)
#define DCM_SegmentAnnotationTypeCodeSequence    DcmTagKey(0x3010, 0x002c)
#define DCM_DeviceLabel                          DcmTagKey(0x3010, 0x002d)
#define DCM_DeviceTypeCodeSequence               DcmTagKey(0x3010, 0x002e)
#define DCM_SegmentAnnotationTypeModifierCodeSequence DcmTagKey(0x3010, 0x002f)
#define DCM_PatientEquipmentRelationshipCodeSequence DcmTagKey(0x3010, 0x0030)
#define DCM_ReferencedFiducialsUID               DcmTagKey(0x3010, 0x0031)
#define DCM_PatientTreatmentOrientationSequence  DcmTagKey(0x3010, 0x0032)
#define DCM_UserContentLabel                     DcmTagKey(0x3010, 0x0033)
#define DCM_UserContentLongLabel                 DcmTagKey(0x3010, 0x0034)
#define DCM_EntityLabel                          DcmTagKey(0x3010, 0x0035)
#define DCM_EntityName                           DcmTagKey(0x3010, 0x0036)
#define DCM_EntityDescription                    DcmTagKey(0x3010, 0x0037)
#define DCM_EntityLongLabel                      DcmTagKey(0x3010, 0x0038)
#define DCM_DeviceIndex                          DcmTagKey(0x3010, 0x0039)
#define DCM_RTTreatmentPhaseIndex                DcmTagKey(0x3010, 0x003a)
#define DCM_RTTreatmentPhaseUID                  DcmTagKey(0x3010, 0x003b)
#define DCM_RTPrescriptionIndex                  DcmTagKey(0x3010, 0x003c)
#define DCM_RTSegmentAnnotationIndex             DcmTagKey(0x3010, 0x003d)
#define DCM_BasisRTTreatmentPhaseIndex           DcmTagKey(0x3010, 0x003e)
#define DCM_RelatedRTTreatmentPhaseIndex         DcmTagKey(0x3010, 0x003f)
#define DCM_ReferencedRTTreatmentPhaseIndex      DcmTagKey(0x3010, 0x0040)
#define DCM_ReferencedRTPrescriptionIndex        DcmTagKey(0x3010, 0x0041)
#define DCM_ReferencedParentRTPrescriptionIndex  DcmTagKey(0x3010, 0x0042)
#define DCM_ManufacturerDeviceIdentifier         DcmTagKey(0x3010, 0x0043)
#define DCM_InstanceLevelReferencedPerformedProcedureStepSequence DcmTagKey(0x3010, 0x0044)
#define DCM_RTTreatmentPhaseIntentPresenceFlag   DcmTagKey(0x3010, 0x0045)
#define DCM_RadiotherapyTreatmentType            DcmTagKey(0x3010, 0x0046)
#define DCM_TeletherapyRadiationType             DcmTagKey(0x3010, 0x0047)
#define DCM_BrachytherapySourceType              DcmTagKey(0x3010, 0x0048)
#define DCM_ReferencedRTTreatmentPhaseSequence   DcmTagKey(0x3010, 0x0049)
#define DCM_ReferencedDirectSegmentInstanceSequence DcmTagKey(0x3010, 0x004a)
#define DCM_IntendedRTTreatmentPhaseSequence     DcmTagKey(0x3010, 0x004b)
#define DCM_IntendedPhaseStartDate               DcmTagKey(0x3010, 0x004c)
#define DCM_IntendedPhaseEndDate                 DcmTagKey(0x3010, 0x004d)
#define DCM_RTTreatmentPhaseIntervalSequence     DcmTagKey(0x3010, 0x004e)
#define DCM_TemporalRelationshipIntervalAnchor   DcmTagKey(0x3010, 0x004f)
#define DCM_MinimumNumberOfIntervalDays          DcmTagKey(0x3010, 0x0050)
#define DCM_MaximumNumberOfIntervalDays          DcmTagKey(0x3010, 0x0051)
#define DCM_PertinentSOPClassesInStudy           DcmTagKey(0x3010, 0x0052)
#define DCM_PertinentSOPClassesInSeries          DcmTagKey(0x3010, 0x0053)
#define DCM_RTPrescriptionLabel                  DcmTagKey(0x3010, 0x0054)
#define DCM_RTPhysicianIntentPredecessorSequence DcmTagKey(0x3010, 0x0055)
#define DCM_RTTreatmentApproachLabel             DcmTagKey(0x3010, 0x0056)
#define DCM_RTPhysicianIntentSequence            DcmTagKey(0x3010, 0x0057)
#define DCM_RTPhysicianIntentIndex               DcmTagKey(0x3010, 0x0058)
#define DCM_RTTreatmentIntentType                DcmTagKey(0x3010, 0x0059)
#define DCM_RTPhysicianIntentNarrative           DcmTagKey(0x3010, 0x005a)
#define DCM_RTProtocolCodeSequence               DcmTagKey(0x3010, 0x005b)
#define DCM_ReasonForSuperseding                 DcmTagKey(0x3010, 0x005c)
#define DCM_RTDiagnosisCodeSequence              DcmTagKey(0x3010, 0x005d)
#define DCM_ReferencedRTPhysicianIntentIndex     DcmTagKey(0x3010, 0x005e)
#define DCM_RTPhysicianIntentInputInstanceSequence DcmTagKey(0x3010, 0x005f)
#define DCM_RTAnatomicPrescriptionSequence       DcmTagKey(0x3010, 0x0060)
#define DCM_PriorTreatmentDoseDescription        DcmTagKey(0x3010, 0x0061)
#define DCM_PriorTreatmentReferenceSequence      DcmTagKey(0x3010, 0x0062)
#define DCM_DosimetricObjectiveEvaluationScope   DcmTagKey(0x3010, 0x0063)
#define DCM_TherapeuticRoleCategoryCodeSequence  DcmTagKey(0x3010, 0x0064)
#define DCM_TherapeuticRoleTypeCodeSequence      DcmTagKey(0x3010, 0x0065)
#define DCM_ConceptualVolumeOptimizationPrecedence DcmTagKey(0x3010, 0x0066)
#define DCM_ConceptualVolumeCategoryCodeSequence DcmTagKey(0x3010, 0x0067)
#define DCM_ConceptualVolumeBlockingConstraint   DcmTagKey(0x3010, 0x0068)
#define DCM_ConceptualVolumeTypeCodeSequence     DcmTagKey(0x3010, 0x0069)
#define DCM_ConceptualVolumeTypeModifierCodeSequence DcmTagKey(0x3010, 0x006a)
#define DCM_RTPrescriptionSequence               DcmTagKey(0x3010, 0x006b)
#define DCM_DosimetricObjectiveSequence          DcmTagKey(0x3010, 0x006c)
#define DCM_DosimetricObjectiveTypeCodeSequence  DcmTagKey(0x3010, 0x006d)
#define DCM_DosimetricObjectiveUID               DcmTagKey(0x3010, 0x006e)
#define DCM_ReferencedDosimetricObjectiveUID     DcmTagKey(0x3010, 0x006f)
#define DCM_DosimetricObjectiveParameterSequence DcmTagKey(0x3010, 0x0070)
#define DCM_ReferencedDosimetricObjectivesSequence DcmTagKey(0x3010, 0x0071)
#define DCM_AbsoluteDosimetricObjectiveFlag      DcmTagKey(0x3010, 0x0073)
#define DCM_DosimetricObjectiveWeight            DcmTagKey(0x3010, 0x0074)
#define DCM_DosimetricObjectivePurpose           DcmTagKey(0x3010, 0x0075)
#define DCM_PlanningInputInformationSequence     DcmTagKey(0x3010, 0x0076)
#define DCM_TreatmentSite                        DcmTagKey(0x3010, 0x0077)
#define DCM_TreatmentSiteCodeSequence            DcmTagKey(0x3010, 0x0078)
#define DCM_FractionPatternSequence              DcmTagKey(0x3010, 0x0079)
#define DCM_TreatmentTechniqueNotes              DcmTagKey(0x3010, 0x007a)
#define DCM_PrescriptionNotes                    DcmTagKey(0x3010, 0x007b)
#define DCM_NumberOfIntervalFractions            DcmTagKey(0x3010, 0x007c)
#define DCM_NumberOfFractions                    DcmTagKey(0x3010, 0x007d)
#define DCM_IntendedDeliveryDuration             DcmTagKey(0x3010, 0x007e)
#define DCM_FractionationNotes                   DcmTagKey(0x3010, 0x007f)
#define DCM_RTTreatmentTechniqueCodeSequence     DcmTagKey(0x3010, 0x0080)
#define DCM_PrescriptionNotesSequence            DcmTagKey(0x3010, 0x0081)
#define DCM_FractionBasedRelationshipSequence    DcmTagKey(0x3010, 0x0082)
#define DCM_FractionBasedRelationshipIntervalAnchor DcmTagKey(0x3010, 0x0083)
#define DCM_MinimumHoursBetweenFractions         DcmTagKey(0x3010, 0x0084)
#define DCM_IntendedFractionStartTime            DcmTagKey(0x3010, 0x0085)
#define DCM_IntendedStartDayOfWeek               DcmTagKey(0x3010, 0x0086)
#define DCM_WeekdayFractionPatternSequence       DcmTagKey(0x3010, 0x0087)
#define DCM_DeliveryTimeStructureCodeSequence    DcmTagKey(0x3010, 0x0088)
#define DCM_TreatmentSiteModifierCodeSequence    DcmTagKey(0x3010, 0x0089)
#define DCM_RETIRED_RoboticBaseLocationIndicator DcmTagKey(0x3010, 0x0090)
#define DCM_RoboticPathNodeSetCodeSequence       DcmTagKey(0x3010, 0x0091)
#define DCM_RoboticNodeIdentifier                DcmTagKey(0x3010, 0x0092)
#define DCM_RTTreatmentSourceCoordinates         DcmTagKey(0x3010, 0x0093)
#define DCM_RadiationSourceCoordinateSystemYawAngle DcmTagKey(0x3010, 0x0094)
#define DCM_RadiationSourceCoordinateSystemRollAngle DcmTagKey(0x3010, 0x0095)
#define DCM_RadiationSourceCoordinateSystemPitchAngle DcmTagKey(0x3010, 0x0096)
#define DCM_RoboticPathControlPointSequence      DcmTagKey(0x3010, 0x0097)
#define DCM_TomotherapeuticControlPointSequence  DcmTagKey(0x3010, 0x0098)
#define DCM_TomotherapeuticLeafOpenDurations     DcmTagKey(0x3010, 0x0099)
#define DCM_TomotherapeuticLeafInitialClosedDurations DcmTagKey(0x3010, 0x009a)
#define DCM_ConceptualVolumeIdentificationSequence DcmTagKey(0x3010, 0x00a0)
#define DCM_RETIRED_Arbitrary                    DcmTagKey(0x4000, 0x0010)
#define DCM_RETIRED_TextComments                 DcmTagKey(0x4000, 0x4000)
#define DCM_RETIRED_ResultsID                    DcmTagKey(0x4008, 0x0040)
#define DCM_RETIRED_ResultsIDIssuer              DcmTagKey(0x4008, 0x0042)
#define DCM_RETIRED_ReferencedInterpretationSequence DcmTagKey(0x4008, 0x0050)
#define DCM_RETIRED_ReportProductionStatusTrial  DcmTagKey(0x4008, 0x00ff)
#define DCM_RETIRED_InterpretationRecordedDate   DcmTagKey(0x4008, 0x0100)
#define DCM_RETIRED_InterpretationRecordedTime   DcmTagKey(0x4008, 0x0101)
#define DCM_RETIRED_InterpretationRecorder       DcmTagKey(0x4008, 0x0102)
#define DCM_RETIRED_ReferenceToRecordedSound     DcmTagKey(0x4008, 0x0103)
#define DCM_RETIRED_InterpretationTranscriptionDate DcmTagKey(0x4008, 0x0108)
#define DCM_RETIRED_InterpretationTranscriptionTime DcmTagKey(0x4008, 0x0109)
#define DCM_RETIRED_InterpretationTranscriber    DcmTagKey(0x4008, 0x010a)
#define DCM_RETIRED_InterpretationText           DcmTagKey(0x4008, 0x010b)
#define DCM_RETIRED_InterpretationAuthor         DcmTagKey(0x4008, 0x010c)
#define DCM_RETIRED_InterpretationApproverSequence DcmTagKey(0x4008, 0x0111)
#define DCM_RETIRED_InterpretationApprovalDate   DcmTagKey(0x4008, 0x0112)
#define DCM_RETIRED_InterpretationApprovalTime   DcmTagKey(0x4008, 0x0113)
#define DCM_RETIRED_PhysicianApprovingInterpretation DcmTagKey(0x4008, 0x0114)
#define DCM_RETIRED_InterpretationDiagnosisDescription DcmTagKey(0x4008, 0x0115)
#define DCM_RETIRED_InterpretationDiagnosisCodeSequence DcmTagKey(0x4008, 0x0117)
#define DCM_RETIRED_ResultsDistributionListSequence DcmTagKey(0x4008, 0x0118)
#define DCM_RETIRED_DistributionName             DcmTagKey(0x4008, 0x0119)
#define DCM_RETIRED_DistributionAddress          DcmTagKey(0x4008, 0x011a)
#define DCM_RETIRED_InterpretationID             DcmTagKey(0x4008, 0x0200)
#define DCM_RETIRED_InterpretationIDIssuer       DcmTagKey(0x4008, 0x0202)
#define DCM_RETIRED_InterpretationTypeID         DcmTagKey(0x4008, 0x0210)
#define DCM_RETIRED_InterpretationStatusID       DcmTagKey(0x4008, 0x0212)
#define DCM_RETIRED_Impressions                  DcmTagKey(0x4008, 0x0300)
#define DCM_RETIRED_ResultsComments              DcmTagKey(0x4008, 0x4000)
#define DCM_LowEnergyDetectors                   DcmTagKey(0x4010, 0x0001)
#define DCM_HighEnergyDetectors                  DcmTagKey(0x4010, 0x0002)
#define DCM_DetectorGeometrySequence             DcmTagKey(0x4010, 0x0004)
#define DCM_ThreatROIVoxelSequence               DcmTagKey(0x4010, 0x1001)
#define DCM_ThreatROIBase                        DcmTagKey(0x4010, 0x1004)
#define DCM_ThreatROIExtents                     DcmTagKey(0x4010, 0x1005)
#define DCM_ThreatROIBitmap                      DcmTagKey(0x4010, 0x1006)
#define DCM_RouteSegmentID                       DcmTagKey(0x4010, 0x1007)
#define DCM_GantryType                           DcmTagKey(0x4010, 0x1008)
#define DCM_OOIOwnerType                         DcmTagKey(0x4010, 0x1009)
#define DCM_RouteSegmentSequence                 DcmTagKey(0x4010, 0x100a)
#define DCM_PotentialThreatObjectID              DcmTagKey(0x4010, 0x1010)
#define DCM_ThreatSequence                       DcmTagKey(0x4010, 0x1011)
#define DCM_ThreatCategory                       DcmTagKey(0x4010, 0x1012)
#define DCM_ThreatCategoryDescription            DcmTagKey(0x4010, 0x1013)
#define DCM_ATDAbilityAssessment                 DcmTagKey(0x4010, 0x1014)
#define DCM_ATDAssessmentFlag                    DcmTagKey(0x4010, 0x1015)
#define DCM_ATDAssessmentProbability             DcmTagKey(0x4010, 0x1016)
#define DCM_Mass                                 DcmTagKey(0x4010, 0x1017)
#define DCM_Density                              DcmTagKey(0x4010, 0x1018)
#define DCM_ZEffective                           DcmTagKey(0x4010, 0x1019)
#define DCM_BoardingPassID                       DcmTagKey(0x4010, 0x101a)
#define DCM_CenterOfMass                         DcmTagKey(0x4010, 0x101b)
#define DCM_CenterOfPTO                          DcmTagKey(0x4010, 0x101c)
#define DCM_BoundingPolygon                      DcmTagKey(0x4010, 0x101d)
#define DCM_RouteSegmentStartLocationID          DcmTagKey(0x4010, 0x101e)
#define DCM_RouteSegmentEndLocationID            DcmTagKey(0x4010, 0x101f)
#define DCM_RouteSegmentLocationIDType           DcmTagKey(0x4010, 0x1020)
#define DCM_AbortReason                          DcmTagKey(0x4010, 0x1021)
#define DCM_VolumeOfPTO                          DcmTagKey(0x4010, 0x1023)
#define DCM_AbortFlag                            DcmTagKey(0x4010, 0x1024)
#define DCM_RouteSegmentStartTime                DcmTagKey(0x4010, 0x1025)
#define DCM_RouteSegmentEndTime                  DcmTagKey(0x4010, 0x1026)
#define DCM_TDRType                              DcmTagKey(0x4010, 0x1027)
#define DCM_InternationalRouteSegment            DcmTagKey(0x4010, 0x1028)
#define DCM_ThreatDetectionAlgorithmAndVersion   DcmTagKey(0x4010, 0x1029)
#define DCM_AssignedLocation                     DcmTagKey(0x4010, 0x102a)
#define DCM_AlarmDecisionTime                    DcmTagKey(0x4010, 0x102b)
#define DCM_AlarmDecision                        DcmTagKey(0x4010, 0x1031)
#define DCM_NumberOfTotalObjects                 DcmTagKey(0x4010, 0x1033)
#define DCM_NumberOfAlarmObjects                 DcmTagKey(0x4010, 0x1034)
#define DCM_PTORepresentationSequence            DcmTagKey(0x4010, 0x1037)
#define DCM_ATDAssessmentSequence                DcmTagKey(0x4010, 0x1038)
#define DCM_TIPType                              DcmTagKey(0x4010, 0x1039)
#define DCM_DICOSVersion                         DcmTagKey(0x4010, 0x103a)
#define DCM_OOIOwnerCreationTime                 DcmTagKey(0x4010, 0x1041)
#define DCM_OOIType                              DcmTagKey(0x4010, 0x1042)
#define DCM_OOISize                              DcmTagKey(0x4010, 0x1043)
#define DCM_AcquisitionStatus                    DcmTagKey(0x4010, 0x1044)
#define DCM_BasisMaterialsCodeSequence           DcmTagKey(0x4010, 0x1045)
#define DCM_PhantomType                          DcmTagKey(0x4010, 0x1046)
#define DCM_OOIOwnerSequence                     DcmTagKey(0x4010, 0x1047)
#define DCM_ScanType                             DcmTagKey(0x4010, 0x1048)
#define DCM_ItineraryID                          DcmTagKey(0x4010, 0x1051)
#define DCM_ItineraryIDType                      DcmTagKey(0x4010, 0x1052)
#define DCM_ItineraryIDAssigningAuthority        DcmTagKey(0x4010, 0x1053)
#define DCM_RouteID                              DcmTagKey(0x4010, 0x1054)
#define DCM_RouteIDAssigningAuthority            DcmTagKey(0x4010, 0x1055)
#define DCM_InboundArrivalType                   DcmTagKey(0x4010, 0x1056)
#define DCM_CarrierID                            DcmTagKey(0x4010, 0x1058)
#define DCM_CarrierIDAssigningAuthority          DcmTagKey(0x4010, 0x1059)
#define DCM_SourceOrientation                    DcmTagKey(0x4010, 0x1060)
#define DCM_SourcePosition                       DcmTagKey(0x4010, 0x1061)
#define DCM_BeltHeight                           DcmTagKey(0x4010, 0x1062)
#define DCM_AlgorithmRoutingCodeSequence         DcmTagKey(0x4010, 0x1064)
#define DCM_TransportClassification              DcmTagKey(0x4010, 0x1067)
#define DCM_OOITypeDescriptor                    DcmTagKey(0x4010, 0x1068)
#define DCM_TotalProcessingTime                  DcmTagKey(0x4010, 0x1069)
#define DCM_DetectorCalibrationData              DcmTagKey(0x4010, 0x106c)
#define DCM_AdditionalScreeningPerformed         DcmTagKey(0x4010, 0x106d)
#define DCM_AdditionalInspectionSelectionCriteria DcmTagKey(0x4010, 0x106e)
#define DCM_AdditionalInspectionMethodSequence   DcmTagKey(0x4010, 0x106f)
#define DCM_AITDeviceType                        DcmTagKey(0x4010, 0x1070)
#define DCM_QRMeasurementsSequence               DcmTagKey(0x4010, 0x1071)
#define DCM_TargetMaterialSequence               DcmTagKey(0x4010, 0x1072)
#define DCM_SNRThreshold                         DcmTagKey(0x4010, 0x1073)
#define DCM_ImageScaleRepresentation             DcmTagKey(0x4010, 0x1075)
#define DCM_ReferencedPTOSequence                DcmTagKey(0x4010, 0x1076)
#define DCM_ReferencedTDRInstanceSequence        DcmTagKey(0x4010, 0x1077)
#define DCM_PTOLocationDescription               DcmTagKey(0x4010, 0x1078)
#define DCM_AnomalyLocatorIndicatorSequence      DcmTagKey(0x4010, 0x1079)
#define DCM_AnomalyLocatorIndicator              DcmTagKey(0x4010, 0x107a)
#define DCM_PTORegionSequence                    DcmTagKey(0x4010, 0x107b)
#define DCM_InspectionSelectionCriteria          DcmTagKey(0x4010, 0x107c)
#define DCM_SecondaryInspectionMethodSequence    DcmTagKey(0x4010, 0x107d)
#define DCM_PRCSToRCSOrientation                 DcmTagKey(0x4010, 0x107e)
#define DCM_MACParametersSequence                DcmTagKey(0x4ffe, 0x0001)
#define DCM_SharedFunctionalGroupsSequence       DcmTagKey(0x5200, 0x9229)
#define DCM_PerFrameFunctionalGroupsSequence     DcmTagKey(0x5200, 0x9230)
#define DCM_WaveformSequence                     DcmTagKey(0x5400, 0x0100)
#define DCM_ChannelMinimumValue                  DcmTagKey(0x5400, 0x0110)
#define DCM_ChannelMaximumValue                  DcmTagKey(0x5400, 0x0112)
#define DCM_WaveformBitsAllocated                DcmTagKey(0x5400, 0x1004)
#define DCM_WaveformSampleInterpretation         DcmTagKey(0x5400, 0x1006)
#define DCM_WaveformPaddingValue                 DcmTagKey(0x5400, 0x100a)
#define DCM_WaveformData                         DcmTagKey(0x5400, 0x1010)
#define DCM_FirstOrderPhaseCorrectionAngle       DcmTagKey(0x5600, 0x0010)
#define DCM_SpectroscopyData                     DcmTagKey(0x5600, 0x0020)
#define DCM_ExtendedOffsetTable                  DcmTagKey(0x7fe0, 0x0001)
#define DCM_ExtendedOffsetTableLengths           DcmTagKey(0x7fe0, 0x0002)
#define DCM_EncapsulatedPixelDataValueTotalLength DcmTagKey(0x7fe0, 0x0003)
#define DCM_FloatPixelData                       DcmTagKey(0x7fe0, 0x0008)
#define DCM_DoubleFloatPixelData                 DcmTagKey(0x7fe0, 0x0009)
#define DCM_PixelData                            DcmTagKey(0x7fe0, 0x0010)
#define DCM_RETIRED_CoefficientsSDVN             DcmTagKey(0x7fe0, 0x0020)
#define DCM_RETIRED_CoefficientsSDHN             DcmTagKey(0x7fe0, 0x0030)
#define DCM_RETIRED_CoefficientsSDDN             DcmTagKey(0x7fe0, 0x0040)
#define DCM_DigitalSignaturesSequence            DcmTagKey(0xfffa, 0xfffa)
#define DCM_DataSetTrailingPadding               DcmTagKey(0xfffc, 0xfffc)
#define DCM_Item                                 DcmTagKey(0xfffe, 0xe000)
#define DCM_ItemDelimitationItem                 DcmTagKey(0xfffe, 0xe00d)
#define DCM_SequenceDelimitationItem             DcmTagKey(0xfffe, 0xe0dd)

/*
** Tags where the group/element can vary (repeating tags).
** Number of entries: 77
*/
#define DCM_OverlayRows                          DcmTagKey(0x6000, 0x0010) /* (0x6000-0x60ff,0x0010) */
#define DCM_OverlayColumns                       DcmTagKey(0x6000, 0x0011) /* (0x6000-0x60ff,0x0011) */
#define DCM_NumberOfFramesInOverlay              DcmTagKey(0x6000, 0x0015) /* (0x6000-0x60ff,0x0015) */
#define DCM_OverlayDescription                   DcmTagKey(0x6000, 0x0022) /* (0x6000-0x60ff,0x0022) */
#define DCM_OverlayType                          DcmTagKey(0x6000, 0x0040) /* (0x6000-0x60ff,0x0040) */
#define DCM_OverlaySubtype                       DcmTagKey(0x6000, 0x0045) /* (0x6000-0x60ff,0x0045) */
#define DCM_OverlayOrigin                        DcmTagKey(0x6000, 0x0050) /* (0x6000-0x60ff,0x0050) */
#define DCM_ImageFrameOrigin                     DcmTagKey(0x6000, 0x0051) /* (0x6000-0x60ff,0x0051) */
#define DCM_OverlayBitsAllocated                 DcmTagKey(0x6000, 0x0100) /* (0x6000-0x60ff,0x0100) */
#define DCM_OverlayBitPosition                   DcmTagKey(0x6000, 0x0102) /* (0x6000-0x60ff,0x0102) */
#define DCM_OverlayActivationLayer               DcmTagKey(0x6000, 0x1001) /* (0x6000-0x60ff,0x1001) */
#define DCM_ROIArea                              DcmTagKey(0x6000, 0x1301) /* (0x6000-0x60ff,0x1301) */
#define DCM_ROIMean                              DcmTagKey(0x6000, 0x1302) /* (0x6000-0x60ff,0x1302) */
#define DCM_ROIStandardDeviation                 DcmTagKey(0x6000, 0x1303) /* (0x6000-0x60ff,0x1303) */
#define DCM_OverlayLabel                         DcmTagKey(0x6000, 0x1500) /* (0x6000-0x60ff,0x1500) */
#define DCM_OverlayData                          DcmTagKey(0x6000, 0x3000) /* (0x6000-0x60ff,0x3000) */
#define DCM_RETIRED_SourceImageIDs               DcmTagKey(0x0020, 0x3100) /* (0x0020,0x3100-0x31ff) */
#define DCM_RETIRED_CurveDimensions              DcmTagKey(0x5000, 0x0005) /* (0x5000-0x50ff,0x0005) */
#define DCM_RETIRED_NumberOfPoints               DcmTagKey(0x5000, 0x0010) /* (0x5000-0x50ff,0x0010) */
#define DCM_RETIRED_TypeOfData                   DcmTagKey(0x5000, 0x0020) /* (0x5000-0x50ff,0x0020) */
#define DCM_RETIRED_CurveDescription             DcmTagKey(0x5000, 0x0022) /* (0x5000-0x50ff,0x0022) */
#define DCM_RETIRED_AxisUnits                    DcmTagKey(0x5000, 0x0030) /* (0x5000-0x50ff,0x0030) */
#define DCM_RETIRED_AxisLabels                   DcmTagKey(0x5000, 0x0040) /* (0x5000-0x50ff,0x0040) */
#define DCM_RETIRED_DataValueRepresentation      DcmTagKey(0x5000, 0x0103) /* (0x5000-0x50ff,0x0103) */
#define DCM_RETIRED_MinimumCoordinateValue       DcmTagKey(0x5000, 0x0104) /* (0x5000-0x50ff,0x0104) */
#define DCM_RETIRED_MaximumCoordinateValue       DcmTagKey(0x5000, 0x0105) /* (0x5000-0x50ff,0x0105) */
#define DCM_RETIRED_CurveRange                   DcmTagKey(0x5000, 0x0106) /* (0x5000-0x50ff,0x0106) */
#define DCM_RETIRED_CurveDataDescriptor          DcmTagKey(0x5000, 0x0110) /* (0x5000-0x50ff,0x0110) */
#define DCM_RETIRED_CoordinateStartValue         DcmTagKey(0x5000, 0x0112) /* (0x5000-0x50ff,0x0112) */
#define DCM_RETIRED_CoordinateStepValue          DcmTagKey(0x5000, 0x0114) /* (0x5000-0x50ff,0x0114) */
#define DCM_RETIRED_CurveActivationLayer         DcmTagKey(0x5000, 0x1001) /* (0x5000-0x50ff,0x1001) */
#define DCM_RETIRED_AudioType                    DcmTagKey(0x5000, 0x2000) /* (0x5000-0x50ff,0x2000) */
#define DCM_RETIRED_AudioSampleFormat            DcmTagKey(0x5000, 0x2002) /* (0x5000-0x50ff,0x2002) */
#define DCM_RETIRED_NumberOfChannels             DcmTagKey(0x5000, 0x2004) /* (0x5000-0x50ff,0x2004) */
#define DCM_RETIRED_NumberOfSamples              DcmTagKey(0x5000, 0x2006) /* (0x5000-0x50ff,0x2006) */
#define DCM_RETIRED_SampleRate                   DcmTagKey(0x5000, 0x2008) /* (0x5000-0x50ff,0x2008) */
#define DCM_RETIRED_TotalTime                    DcmTagKey(0x5000, 0x200a) /* (0x5000-0x50ff,0x200a) */
#define DCM_RETIRED_AudioSampleData              DcmTagKey(0x5000, 0x200c) /* (0x5000-0x50ff,0x200c) */
#define DCM_RETIRED_AudioComments                DcmTagKey(0x5000, 0x200e) /* (0x5000-0x50ff,0x200e) */
#define DCM_RETIRED_CurveLabel                   DcmTagKey(0x5000, 0x2500) /* (0x5000-0x50ff,0x2500) */
#define DCM_RETIRED_CurveReferencedOverlaySequence DcmTagKey(0x5000, 0x2600) /* (0x5000-0x50ff,0x2600) */
#define DCM_RETIRED_CurveReferencedOverlayGroup  DcmTagKey(0x5000, 0x2610) /* (0x5000-0x50ff,0x2610) */
#define DCM_RETIRED_CurveData                    DcmTagKey(0x5000, 0x3000) /* (0x5000-0x50ff,0x3000) */
#define DCM_RETIRED_OverlayPlanes                DcmTagKey(0x6000, 0x0012) /* (0x6000-0x60ff,0x0012) */
#define DCM_RETIRED_OverlayPlaneOrigin           DcmTagKey(0x6000, 0x0052) /* (0x6000-0x60ff,0x0052) */
#define DCM_RETIRED_OverlayCompressionCode       DcmTagKey(0x6000, 0x0060) /* (0x6000-0x60ff,0x0060) */
#define DCM_RETIRED_OverlayCompressionOriginator DcmTagKey(0x6000, 0x0061) /* (0x6000-0x60ff,0x0061) */
#define DCM_RETIRED_OverlayCompressionLabel      DcmTagKey(0x6000, 0x0062) /* (0x6000-0x60ff,0x0062) */
#define DCM_RETIRED_OverlayCompressionDescription DcmTagKey(0x6000, 0x0063) /* (0x6000-0x60ff,0x0063) */
#define DCM_RETIRED_OverlayCompressionStepPointers DcmTagKey(0x6000, 0x0066) /* (0x6000-0x60ff,0x0066) */
#define DCM_RETIRED_OverlayRepeatInterval        DcmTagKey(0x6000, 0x0068) /* (0x6000-0x60ff,0x0068) */
#define DCM_RETIRED_OverlayBitsGrouped           DcmTagKey(0x6000, 0x0069) /* (0x6000-0x60ff,0x0069) */
#define DCM_RETIRED_OverlayFormat                DcmTagKey(0x6000, 0x0110) /* (0x6000-0x60ff,0x0110) */
#define DCM_RETIRED_OverlayLocation              DcmTagKey(0x6000, 0x0200) /* (0x6000-0x60ff,0x0200) */
#define DCM_RETIRED_OverlayCodeLabel             DcmTagKey(0x6000, 0x0800) /* (0x6000-0x60ff,0x0800) */
#define DCM_RETIRED_OverlayNumberOfTables        DcmTagKey(0x6000, 0x0802) /* (0x6000-0x60ff,0x0802) */
#define DCM_RETIRED_OverlayCodeTableLocation     DcmTagKey(0x6000, 0x0803) /* (0x6000-0x60ff,0x0803) */
#define DCM_RETIRED_OverlayBitsForCodeWord       DcmTagKey(0x6000, 0x0804) /* (0x6000-0x60ff,0x0804) */
#define DCM_RETIRED_OverlayDescriptorGray        DcmTagKey(0x6000, 0x1100) /* (0x6000-0x60ff,0x1100) */
#define DCM_RETIRED_OverlayDescriptorRed         DcmTagKey(0x6000, 0x1101) /* (0x6000-0x60ff,0x1101) */
#define DCM_RETIRED_OverlayDescriptorGreen       DcmTagKey(0x6000, 0x1102) /* (0x6000-0x60ff,0x1102) */
#define DCM_RETIRED_OverlayDescriptorBlue        DcmTagKey(0x6000, 0x1103) /* (0x6000-0x60ff,0x1103) */
#define DCM_RETIRED_OverlaysGray                 DcmTagKey(0x6000, 0x1200) /* (0x6000-0x60ff,0x1200) */
#define DCM_RETIRED_OverlaysRed                  DcmTagKey(0x6000, 0x1201) /* (0x6000-0x60ff,0x1201) */
#define DCM_RETIRED_OverlaysGreen                DcmTagKey(0x6000, 0x1202) /* (0x6000-0x60ff,0x1202) */
#define DCM_RETIRED_OverlaysBlue                 DcmTagKey(0x6000, 0x1203) /* (0x6000-0x60ff,0x1203) */
#define DCM_RETIRED_OverlayComments              DcmTagKey(0x6000, 0x4000) /* (0x6000-0x60ff,0x4000) */
#define DCM_RETIRED_VariablePixelData            DcmTagKey(0x7f00, 0x0010) /* (0x7f00-0x7fff,0x0010) */
#define DCM_RETIRED_VariableNextDataGroup        DcmTagKey(0x7f00, 0x0011) /* (0x7f00-0x7fff,0x0011) */
#define DCM_RETIRED_VariableCoefficientsSDVN     DcmTagKey(0x7f00, 0x0020) /* (0x7f00-0x7fff,0x0020) */
#define DCM_RETIRED_VariableCoefficientsSDHN     DcmTagKey(0x7f00, 0x0030) /* (0x7f00-0x7fff,0x0030) */
#define DCM_RETIRED_VariableCoefficientsSDDN     DcmTagKey(0x7f00, 0x0040) /* (0x7f00-0x7fff,0x0040) */
#define DCM_PrivateGroupLength                   DcmTagKey(0x0009, 0x0000) /* (0x0009-o-0xffff,0x0000) */
#define DCM_PrivateCreator                       DcmTagKey(0x0009, 0x0010) /* (0x0009-o-0xffff,0x0010-u-0x00ff) */
#define DCM_IllegalGroupLength                   DcmTagKey(0x0001, 0x0000) /* (0x0001-o-0x0007,0x0000) */
#define DCM_IllegalPrivateCreator                DcmTagKey(0x0001, 0x0010) /* (0x0001-o-0x0007,0x0010-u-0x00ff) */
#define DCM_GenericGroupLength                   DcmTagKey(0x0000, 0x0000) /* (0x0000-u-0xffff,0x0000) */

#endif /* !DCDEFTAG_H */
